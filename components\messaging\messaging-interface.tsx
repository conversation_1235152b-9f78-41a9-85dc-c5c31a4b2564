"use client"

import { useState, useEffect } from "react"
import { MessageThreadList } from "./message-thread-list"
import { ConversationView } from "./conversation-view"
import { Card } from "@/components/ui/card"
import { MessageSquare } from "lucide-react"
import type { MessageThreadWithDetails } from "@/lib/types"

export function MessagingInterface() {
  const [selectedThread, setSelectedThread] = useState<MessageThreadWithDetails | null>(null)
  const [threads, setThreads] = useState<MessageThreadWithDetails[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchThreads()
  }, [])

  const fetchThreads = async () => {
    try {
      const response = await fetch('/api/messages/threads')
      if (response.ok) {
        const data = await response.json()
        setThreads(data.threads || [])
      }
    } catch (error) {
      console.error('Error fetching threads:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleThreadSelect = (thread: MessageThreadWithDetails) => {
    setSelectedThread(thread)
  }

  const handleNewMessage = () => {
    // Refresh threads to show new message
    fetchThreads()
    if (selectedThread) {
      // Refresh the selected thread
      const updatedThread = threads.find(t => t.id === selectedThread.id)
      if (updatedThread) {
        setSelectedThread(updatedThread)
      }
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      {/* Thread List */}
      <div className="lg:col-span-1">
        <Card className="bg-neutral-900 border-neutral-800 h-full">
          <MessageThreadList
            threads={threads}
            selectedThread={selectedThread}
            onThreadSelect={handleThreadSelect}
            loading={loading}
            onRefresh={fetchThreads}
          />
        </Card>
      </div>

      {/* Conversation View */}
      <div className="lg:col-span-2">
        <Card className="bg-neutral-900 border-neutral-800 h-full">
          {selectedThread ? (
            <ConversationView
              thread={selectedThread}
              onNewMessage={handleNewMessage}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageSquare className="h-16 w-16 text-neutral-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-400 mb-2">
                  Select a conversation
                </h3>
                <p className="text-neutral-500">
                  Choose a conversation from the list to start messaging
                </p>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
