import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { BusinessManagement } from "@/components/admin/business-management"
import { AdminLoading } from "@/components/admin/admin-loading"

export const metadata: Metadata = {
  title: "Business Management - Admin Dashboard",
  description: "Manage business listings, approvals, and subscriptions",
}

export default function BusinessManagementPage() {
  return (
    <Suspense fallback={<AdminLoading type="table" title="Business Management" />}>
      <BusinessManagement />
    </Suspense>
  )
}
