"use client"

import { useEffect } from "react" // Import useEffect
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Star, MapPin, Phone, ExternalLink, Crown, Clock, Shield, Award } from "lucide-react"

interface BusinessProfileProps {
  businessId: string
}

export function BusinessProfile({ businessId }: BusinessProfileProps) {
  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  // Mock data for Elite Pressure Pros
  const business = {
    id: "elite-pressure-pros",
    name: "Elite Pressure Pros",
    description:
      "Premium residential & commercial pressure washing services with over 10 years of experience. We specialize in house washing, driveway cleaning, deck restoration, and commercial building maintenance.",
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://elitepressurepros.com",
    address: "123 Main St, Phoenix, AZ 85001",
    city: "Phoenix",
    state: "AZ",
    zipCode: "85001",
    rating: 4.9,
    reviewCount: 24,
    isPremium: true,
    verified: true,
    yearsInBusiness: 10,
    services: [
      "House Washing",
      "Driveway Cleaning",
      "Deck Restoration",
      "Commercial Cleaning",
      "Roof Cleaning",
      "Concrete Cleaning",
    ],
    gallery: [
      "/placeholder.svg?height=300&width=400&text=Before+After+House",
      "/placeholder.svg?height=300&width=400&text=Clean+Driveway",
      "/placeholder.svg?height=300&width=400&text=Deck+Restoration",
      "/placeholder.svg?height=300&width=400&text=Commercial+Building",
    ],
    reviews: [
      {
        id: 1,
        name: "Sarah Johnson",
        rating: 5,
        date: "2024-01-15",
        comment:
          "Absolutely amazing service! They transformed our house exterior completely. Very professional and reasonably priced.",
      },
      {
        id: 2,
        name: "Mike Chen",
        rating: 5,
        date: "2024-01-10",
        comment:
          "Elite Pressure Pros did an excellent job on our driveway and walkway. The results exceeded our expectations!",
      },
      {
        id: 3,
        name: "Lisa Rodriguez",
        rating: 4,
        date: "2024-01-05",
        comment: "Great work on our deck restoration. The team was punctual and cleaned up after themselves.",
      },
    ],
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-neutral-600"}`}
      />
    ))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Business Header */}
      <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-8 mb-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Business Image */}
          <div className="flex-shrink-0">
            <div className="w-48 h-48 bg-neutral-800 rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=192&width=192&text=Elite+Pressure+Pros+Logo"
                alt={business.name}
                width={192}
                height={192}
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Business Info */}
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-4">
              <h1 className="text-3xl font-bold text-white">{business.name}</h1>
              {business.isPremium && <Crown className="h-6 w-6 text-yellow-400" />}
              {business.verified && <Shield className="h-6 w-6 text-green-400" />}
            </div>

            {/* Rating */}
            <div className="flex items-center gap-3 mb-4">
              <div className="flex">{renderStars(business.rating)}</div>
              <span className="text-lg text-white font-semibold">{business.rating}</span>
              <span className="text-neutral-400">({business.reviewCount} reviews)</span>
            </div>

            {/* Location */}
            <div className="flex items-center gap-2 mb-4">
              <MapPin className="h-4 w-4 text-neutral-500" />
              <span className="text-neutral-300">{business.address}</span>
            </div>

            {/* Contact Info */}
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-neutral-500" />
                <span className="text-neutral-300">{business.phone}</span>
              </div>
              <a
                href={business.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-400 hover:text-blue-300"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Visit Website</span>
              </a>
            </div>

            {/* Services */}
            <div className="flex flex-wrap gap-2 mb-6">
              {business.services.map((service, index) => (
                <Badge key={index} variant="secondary" className="bg-neutral-800 text-neutral-300 border-neutral-700">
                  {service}
                </Badge>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button className="bg-blue-gradient-hover">Get Free Quote</Button>
              <Button
                variant="outline"
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
              >
                Call Now
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* About */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">About Elite Pressure Pros</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-300 leading-relaxed">{business.description}</p>
            </CardContent>
          </Card>

          {/* Gallery */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Our Work</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {business.gallery.map((image, index) => (
                  <div key={index} className="bg-neutral-800 rounded-lg overflow-hidden">
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`Work example ${index + 1}`}
                      width={400}
                      height={300}
                      className="w-full h-48 object-cover"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Reviews */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Customer Reviews</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {business.reviews.map((review) => (
                <div key={review.id} className="border-b border-neutral-800 pb-6 last:border-b-0">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 bg-blue-gradient rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold">{review.name.charAt(0)}</span>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">{review.name}</h4>
                      <div className="flex items-center gap-2">
                        <div className="flex">{renderStars(review.rating)}</div>
                        <span className="text-neutral-400 text-sm">{review.date}</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-neutral-300">{review.comment}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Info */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Quick Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-blue-400" />
                <div>
                  <p className="text-white font-medium">Years in Business</p>
                  <p className="text-neutral-400">{business.yearsInBusiness}+ years</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Award className="h-5 w-5 text-blue-400" />
                <div>
                  <p className="text-white font-medium">Premium Member</p>
                  <p className="text-neutral-400">Verified & Insured</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Shield className="h-5 w-5 text-blue-400" />
                <div>
                  <p className="text-white font-medium">Licensed & Bonded</p>
                  <p className="text-neutral-400">Fully insured business</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Card */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Get in Touch</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full bg-blue-gradient-hover">Request Free Quote</Button>
              <Button
                variant="outline"
                className="w-full border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
              >
                Call {business.phone}
              </Button>
              <Button
                variant="outline"
                className="w-full border-neutral-700 text-neutral-300 hover:bg-neutral-800 bg-transparent"
              >
                Send Message
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
