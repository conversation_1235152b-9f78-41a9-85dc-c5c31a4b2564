"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Crown, Star, TrendingUp, Users, MessageSquare, Shield } from "lucide-react"

export function PricingPlans() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly")

  const plans = [
    {
      name: "Basic",
      description: "Perfect for getting started",
      price: { monthly: 0, yearly: 0 },
      popular: false,
      features: [
        "Business Profile",
        "Receive Customer Reviews",
        "Appear in Search Results",
        "Basic Contact Information",
        "Up to 5 Photos",
        "Service Category Selection",
        "Customer Inquiry Form",
      ],
      limitations: ["Standard search ranking", "Limited photo uploads", "Basic profile features"],
      cta: "Your Current Plan",
      ctaVariant: "outline" as const,
      icon: Users,
    },
    {
      name: "Premium",
      description: "Everything you need to grow",
      price: { monthly: 49, yearly: 490 },
      popular: true,
      features: [
        "Everything in Basic, PLUS:",
        "HIGHER Search Ranking",
        '"Featured" Profile Badge',
        "Unlimited Photos in Gallery",
        "View Customer Lead Information",
        "Priority Customer Support",
        "Advanced Analytics Dashboard",
        "Custom Business Profile URL",
        "Social Media Integration",
        "Review Response Tools",
        "Lead Notification Alerts",
      ],
      cta: "Upgrade to Premium",
      ctaVariant: "default" as const,
      icon: Crown,
    },
  ]

  const handleUpgrade = (planName: string) => {
    // Frontend only - would integrate with Stripe
    console.log(`Upgrade to ${planName} clicked`)
  }

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        {/* Billing Toggle */}
        <div className="flex justify-center mb-12">
          <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-1 flex">
            <button
              onClick={() => setBillingCycle("monthly")}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                billingCycle === "monthly" ? "bg-blue-500 text-white" : "text-neutral-400 hover:text-white"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle("yearly")}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-colors relative ${
                billingCycle === "yearly" ? "bg-blue-500 text-white" : "text-neutral-400 hover:text-white"
              }`}
            >
              Yearly
              <Badge className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5">Save 17%</Badge>
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {plans.map((plan) => {
            const Icon = plan.icon
            const price = plan.price[billingCycle]
            const yearlyDiscount = billingCycle === "yearly" && plan.price.yearly < plan.price.monthly * 12

            return (
              <Card
                key={plan.name}
                className={`relative ${
                  plan.popular
                    ? "border-2 border-blue-500/50 bg-blue-500/5 scale-105"
                    : "bg-neutral-900 border-neutral-800"
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center mb-4">
                    <div
                      className={`p-3 rounded-full ${plan.popular ? "bg-blue-gradient glow-blue" : "bg-neutral-800"}`}
                    >
                      <Icon className={`h-6 w-6 ${plan.popular ? "text-white" : "text-neutral-400"}`} />
                    </div>
                  </div>
                  <CardTitle className="text-2xl text-white mb-2">{plan.name}</CardTitle>
                  <p className="text-neutral-400 mb-4">{plan.description}</p>

                  <div className="mb-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-white">${price}</span>
                      {price > 0 && (
                        <span className="text-neutral-400 ml-2">/{billingCycle === "monthly" ? "month" : "year"}</span>
                      )}
                    </div>
                    {yearlyDiscount && (
                      <p className="text-green-400 text-sm mt-1">
                        Save ${plan.price.monthly * 12 - plan.price.yearly} per year
                      </p>
                    )}
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Features List */}
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <Check
                          className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                            plan.popular ? "text-blue-400" : "text-green-400"
                          }`}
                        />
                        <span
                          className={`text-sm ${
                            feature.includes("PLUS:") ? "font-semibold text-blue-400" : "text-neutral-300"
                          }`}
                        >
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {/* Limitations (for Basic plan) */}
                  {plan.limitations && (
                    <div className="mb-6 p-3 bg-neutral-800 rounded-lg">
                      <p className="text-neutral-400 text-xs mb-2">Limitations:</p>
                      <ul className="space-y-1">
                        {plan.limitations.map((limitation, index) => (
                          <li key={index} className="text-neutral-500 text-xs">
                            • {limitation}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* CTA Button */}
                  <Button
                    onClick={() => handleUpgrade(plan.name)}
                    variant={plan.ctaVariant}
                    className={`w-full ${
                      plan.popular
                        ? "bg-blue-gradient-hover"
                        : "border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                    }`}
                    disabled={plan.name === "Basic"}
                  >
                    {plan.popular && <Crown className="h-4 w-4 mr-2" />}
                    {plan.cta}
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Feature Comparison */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-white text-center mb-8">Why Choose Premium?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-neutral-900 border-neutral-800 text-center">
              <CardContent className="p-6">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-semibold mb-2">3x More Visibility</h4>
                <p className="text-neutral-400 text-sm">
                  Premium listings appear first in search results and get featured placement
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 text-center">
              <CardContent className="p-6">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
                  <MessageSquare className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-semibold mb-2">More Leads</h4>
                <p className="text-neutral-400 text-sm">
                  Get instant notifications and detailed customer information for every inquiry
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 text-center">
              <CardContent className="p-6">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-semibold mb-2">Build Trust</h4>
                <p className="text-neutral-400 text-sm">
                  Featured badge and unlimited photos help establish credibility with customers
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
