import { Head<PERSON> } from "@/components/header"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Users,
  MessageSquare,
  Star,
  Camera,
  TrendingUp,
  Shield,
  Clock,
  CheckCircle,
  ArrowRight,
  Phone,
  Mail,
} from "lucide-react"
import Link from "next/link"

export default function ForBusinessesPage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20 mb-6">For Businesses</Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Get More Leads. <span className="text-white">Grow Your Business.</span>
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-3xl mx-auto">
            Join Phoenix's #1 directory to connect with local customers actively looking for your pressure washing
            services.
          </p>
          <Button asChild size="lg" className="bg-blue-gradient-hover text-lg px-8 py-4 mb-4">
            <Link href="/onboarding">
              Create Your Free Listing
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <p className="text-sm text-neutral-500">No setup fees • Cancel anytime • Get leads in 24 hours</p>
        </div>
      </section>

      {/* How Our Directory Helps You */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">How Our Directory Helps You</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Get Discovered</h3>
                <p className="text-neutral-400">
                  Be found by customers in your area actively searching for pressure washing services.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6 text-center">
                <MessageSquare className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Receive Leads</h3>
                <p className="text-neutral-400">
                  Get direct quote requests from potential customers ready to hire your services.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6 text-center">
                <Star className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Build Trust</h3>
                <p className="text-neutral-400">
                  Collect and showcase reviews from satisfied customers to build your reputation.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6 text-center">
                <Camera className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Showcase Work</h3>
                <p className="text-neutral-400">
                  Upload before/after photos to your gallery and demonstrate your quality work.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 px-4 bg-neutral-900/30">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Join a Growing Network</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <div className="text-4xl font-bold text-blue-400 mb-2">500+</div>
                <div className="text-neutral-400">Active Businesses</div>
              </CardContent>
            </Card>
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <div className="text-4xl font-bold text-blue-400 mb-2">2,500+</div>
                <div className="text-neutral-400">Monthly Searches</div>
              </CardContent>
            </Card>
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <div className="text-4xl font-bold text-blue-400 mb-2">85%</div>
                <div className="text-neutral-400">Lead Response Rate</div>
              </CardContent>
            </Card>
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <div className="text-4xl font-bold text-blue-400 mb-2">4.8★</div>
                <div className="text-neutral-400">Average Business Rating</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Everything You Need to Succeed</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="space-y-6">
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <TrendingUp className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Business Dashboard</h3>
                      <p className="text-neutral-400">
                        Manage your profile, track leads, respond to reviews, and monitor your business performance all
                        in one place.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Camera className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Photo Gallery</h3>
                      <p className="text-neutral-400">
                        Showcase your best work with before/after photos that help customers visualize your quality and
                        expertise.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <MessageSquare className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Lead Management</h3>
                      <p className="text-neutral-400">
                        Receive and manage quote requests directly through our platform. Never miss a potential customer
                        again.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Star className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Review Management</h3>
                      <p className="text-neutral-400">
                        Build trust with potential customers by collecting and showcasing authentic reviews from your
                        satisfied clients.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Shield className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Verified Badge</h3>
                      <p className="text-neutral-400">
                        Stand out with our verification badge that shows customers you're a legitimate, licensed
                        business.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Clock className="h-8 w-8 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">24/7 Support</h3>
                      <p className="text-neutral-400">
                        Get help when you need it with our dedicated business support team available around the clock.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-16 px-4 bg-neutral-900/50">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Don't Just Take Our Word For It</h2>
          <Card className="bg-neutral-900 border-neutral-800 max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <div className="flex justify-center mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="h-6 w-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-xl text-white mb-6">
                "Signing up was the best marketing decision I made this year. We get at least 3-4 new leads a week from
                our profile, and the quality of customers is excellent. The dashboard makes it easy to manage
                everything."
              </blockquote>
              <div className="text-neutral-400">
                <div className="font-semibold">Sarah J.</div>
                <div>Owner of AZ Suds Power Washing</div>
                <div className="text-sm">(Member since June 2024)</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Pricing Preview */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Simple, Transparent Pricing</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-white mb-4">Free Listing</h3>
                <div className="text-4xl font-bold text-blue-400 mb-6">
                  $0<span className="text-lg text-neutral-400">/month</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Basic business profile
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Contact information display
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Customer reviews
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Basic photo gallery (5 photos)
                  </li>
                </ul>
                <Button variant="outline" className="w-full bg-transparent" asChild>
                  <Link href="/onboarding">Get Started Free</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-blue-500/20 border-2 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-gradient text-white">Most Popular</Badge>
              </div>
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-white mb-4">Premium Listing</h3>
                <div className="text-4xl font-bold text-blue-400 mb-6">
                  $49<span className="text-lg text-neutral-400">/month</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Everything in Free, plus:
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Priority placement in search
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Unlimited photo gallery
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Lead management dashboard
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Verified business badge
                  </li>
                  <li className="flex items-center text-neutral-300">
                    <CheckCircle className="h-5 w-5 text-blue-400 mr-3" />
                    Priority customer support
                  </li>
                </ul>
                <Button className="w-full bg-blue-gradient-hover" asChild>
                  <Link href="/onboarding">Start Premium Trial</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
          <div className="text-center mt-8">
            <Link href="/pricing" className="text-blue-400 hover:text-blue-300 transition-colors">
              View detailed pricing and features →
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">Ready to Get More Customers?</h2>
          <p className="text-xl text-neutral-400 mb-8 max-w-2xl mx-auto">
            Join hundreds of successful pressure washing businesses already growing with PressureWash Pro.
          </p>
          <Button asChild size="lg" className="bg-blue-gradient-hover text-lg px-8 py-4 mb-6">
            <Link href="/onboarding">
              Create Your Free Listing Today
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <div className="flex items-center justify-center space-x-8 text-sm text-neutral-500">
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2" />
              Questions? Call (*************
            </div>
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              <EMAIL>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">PressureWash Pro</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/for-businesses" className="hover:text-white transition-colors">
                    List Your Business
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
