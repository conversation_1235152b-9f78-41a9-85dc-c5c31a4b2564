#!/usr/bin/env node

/**
 * Test Environment Setup Script
 * Sets up the testing environment and validates configuration
 */

const { createClient } = require('@supabase/supabase-js')
const { readFileSync, writeFileSync, existsSync } = require('fs')
const { join } = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.test.local' })
require('dotenv').config({ path: '.env.test' })

async function validateSupabaseConnection() {
  console.log('🔗 Validating Supabase connection...')
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    console.log('   Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set')
    return false
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const { data, error } = await supabase.from('services').select('count').limit(1)
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message)
      return false
    }
    
    console.log('✅ Supabase connection successful')
    return true
  } catch (error) {
    console.error('❌ Supabase connection error:', error.message)
    return false
  }
}

async function checkRequiredTables() {
  console.log('📋 Checking required database tables...')
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  const requiredTables = [
    'profiles',
    'businesses',
    'business_members',
    'locations',
    'services',
    'business_services',
    'portfolio_images',
    'reviews',
    'message_threads',
    'messages'
  ]

  let allTablesExist = true

  for (const table of requiredTables) {
    try {
      const { error } = await supabase.from(table).select('*').limit(1)
      
      if (error) {
        console.error(`❌ Table '${table}' not accessible:`, error.message)
        allTablesExist = false
      } else {
        console.log(`✅ Table '${table}' exists and accessible`)
      }
    } catch (error) {
      console.error(`❌ Error checking table '${table}':`, error.message)
      allTablesExist = false
    }
  }

  return allTablesExist
}

function createTestEnvFile() {
  console.log('📝 Creating test environment file...')
  
  const testEnvPath = '.env.test.local'
  
  if (existsSync(testEnvPath)) {
    console.log('⚠️  .env.test.local already exists, skipping creation')
    return true
  }

  const templateContent = `# Test Environment Variables (Local)
# This file is automatically generated and should be customized for your environment

# Test Environment
NODE_ENV=test
TEST_ENV=local

# Supabase Test Configuration
NEXT_PUBLIC_SUPABASE_URL=${process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321'}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your_test_anon_key'}
SUPABASE_SERVICE_ROLE_KEY=${process.env.SUPABASE_SERVICE_ROLE_KEY || 'your_test_service_role_key'}

# Local Supabase Database URL
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres

# Test Configuration
SEED_TEST_DB=true
TEST_TIMEOUT=30000
API_BASE_URL=http://localhost:3000
`

  try {
    writeFileSync(testEnvPath, templateContent)
    console.log('✅ Created .env.test.local file')
    console.log('   Please update the values with your actual test environment configuration')
    return true
  } catch (error) {
    console.error('❌ Failed to create .env.test.local:', error.message)
    return false
  }
}

function installPlaywrightBrowsers() {
  console.log('🎭 Installing Playwright browsers...')
  
  const { execSync } = require('child_process')
  
  try {
    execSync('npx playwright install', { stdio: 'inherit' })
    console.log('✅ Playwright browsers installed')
    return true
  } catch (error) {
    console.error('❌ Failed to install Playwright browsers:', error.message)
    console.log('   You can install them manually with: npx playwright install')
    return false
  }
}

async function seedTestDatabase() {
  console.log('🌱 Seeding test database...')
  
  try {
    const { TestSeedManager } = require('../tests/database/seed-manager')
    const seedManager = new TestSeedManager()
    
    await seedManager.resetDatabase()
    
    const hasTestData = await seedManager.verifyTestData()
    
    if (hasTestData) {
      console.log('✅ Test database seeded successfully')
      return true
    } else {
      console.error('❌ Test database seeding verification failed')
      return false
    }
  } catch (error) {
    console.error('❌ Failed to seed test database:', error.message)
    return false
  }
}

async function runSetup() {
  console.log('🧪 Setting up test environment for PressureWash Pro Directory\n')
  
  const steps = [
    { name: 'Create Test Environment File', fn: createTestEnvFile },
    { name: 'Validate Supabase Connection', fn: validateSupabaseConnection },
    { name: 'Check Required Tables', fn: checkRequiredTables },
    { name: 'Install Playwright Browsers', fn: installPlaywrightBrowsers },
  ]

  // Only seed database if explicitly requested
  if (process.env.SEED_TEST_DB === 'true' || process.argv.includes('--seed')) {
    steps.push({ name: 'Seed Test Database', fn: seedTestDatabase })
  }

  let passed = 0
  let failed = 0

  for (const step of steps) {
    console.log(`\n--- ${step.name} ---`)
    
    try {
      const result = await step.fn()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`❌ Step '${step.name}' failed:`, error.message)
      failed++
    }
  }

  console.log('\n' + '='.repeat(50))
  console.log('📊 Setup Results')
  console.log('='.repeat(50))
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)

  if (failed === 0) {
    console.log('\n🎉 Test environment setup complete!')
    console.log('   Next steps:')
    console.log('   1. Update .env.test.local with your actual test environment values')
    console.log('   2. Run `npm run test` to run unit tests')
    console.log('   3. Run `npm run test:integration` to run integration tests')
    console.log('   4. Run `npm run test:e2e` to run end-to-end tests')
  } else {
    console.log('\n⚠️  Some setup steps failed. Please check the errors above.')
  }

  process.exit(failed === 0 ? 0 : 1)
}

// Run setup
runSetup().catch(error => {
  console.error('💥 Setup script crashed:', error.message)
  process.exit(1)
})