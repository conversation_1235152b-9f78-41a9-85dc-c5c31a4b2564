// Tests for image processing utilities
import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock the Supabase admin client
vi.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
        getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'https://test-url.com/image.jpg' } })
      }))
    }
  }
}))

// Mock the database functions
vi.mock('@/lib/database', () => ({
  addPortfolioImage: vi.fn().mockResolvedValue({ image: { id: 'test-id' }, error: null })
}))

// Import after mocking
import { downloadImageFromUrl, processBusinessImages } from '@/lib/image-utils'

describe('Image Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('downloadImageFromUrl', () => {
    it('should download and validate image successfully', async () => {
      const mockImageData = 'fake-image-data'
      const mockImageBuffer = Buffer.from(mockImageData)

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: {
          get: vi.fn().mockReturnValue('image/jpeg')
        },
        arrayBuffer: vi.fn().mockResolvedValue(mockImageBuffer.buffer)
      })

      const result = await downloadImageFromUrl('https://example.com/image.jpg', 0)

      expect(result.contentType).toBe('image/jpeg')
      expect(result.filename).toBe('image-1.jpg')
      expect(result.size).toBeGreaterThan(0)
      expect(Buffer.isBuffer(result.buffer)).toBe(true)
    })

    it('should handle download timeout', async () => {
      global.fetch = vi.fn().mockImplementation(() =>
        new Promise((_, reject) => {
          const error = new Error('AbortError')
          error.name = 'AbortError'
          setTimeout(() => reject(error), 100)
        })
      )

      await expect(downloadImageFromUrl('https://example.com/slow-image.jpg'))
        .rejects.toThrow('Download timeout')
    })

    it('should reject unsupported image formats', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: {
          get: vi.fn().mockReturnValue('application/pdf')
        },
        arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(100))
      })

      await expect(downloadImageFromUrl('https://example.com/document.pdf'))
        .rejects.toThrow('Unsupported image format: application/pdf')
    })

    it('should reject images that are too large', async () => {
      const largeBuffer = Buffer.alloc(15 * 1024 * 1024) // 15MB
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: {
          get: vi.fn().mockReturnValue('image/jpeg')
        },
        arrayBuffer: vi.fn().mockResolvedValue(largeBuffer.buffer)
      })

      await expect(downloadImageFromUrl('https://example.com/large-image.jpg'))
        .rejects.toThrow('Image too large')
    })

    it('should handle HTTP errors', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      })

      await expect(downloadImageFromUrl('https://example.com/missing.jpg'))
        .rejects.toThrow('HTTP 404: Not Found')
    })
  })

  describe('processBusinessImages', () => {
    it('should process multiple images successfully', async () => {
      const mockImageBuffer = Buffer.from('fake-image-data')
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: {
          get: vi.fn().mockReturnValue('image/jpeg')
        },
        arrayBuffer: vi.fn().mockResolvedValue(mockImageBuffer.buffer)
      })

      const photoUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ]

      const result = await processBusinessImages(photoUrls, 'test-business-id')

      expect(result.success).toBe(2)
      expect(result.failed).toBe(0)
      expect(result.errors).toHaveLength(0)
      expect(result.uploadedImages).toHaveLength(2)
    })

    it('should handle mixed success and failure', async () => {
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          headers: { get: vi.fn().mockReturnValue('image/jpeg') },
          arrayBuffer: vi.fn().mockResolvedValue(Buffer.from('image1').buffer)
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found'
        })

      const photoUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/missing.jpg'
      ]

      const result = await processBusinessImages(photoUrls, 'test-business-id')

      expect(result.success).toBe(1)
      expect(result.failed).toBe(1)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toContain('Image 2: HTTP 404: Not Found')
    })

    it('should limit number of images processed', async () => {
      const mockImageBuffer = Buffer.from('fake-image-data')
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: { get: vi.fn().mockReturnValue('image/jpeg') },
        arrayBuffer: vi.fn().mockResolvedValue(mockImageBuffer.buffer)
      })

      // Create 15 URLs (more than the limit of 10)
      const photoUrls = Array.from({ length: 15 }, (_, i) => `https://example.com/image${i}.jpg`)

      const result = await processBusinessImages(photoUrls, 'test-business-id')

      // Should only process 10 images (MAX_IMAGES_PER_BUSINESS)
      expect(result.success).toBe(10)
      expect(result.uploadedImages).toHaveLength(10)
    })

    it('should return empty result for no URLs', async () => {
      const result = await processBusinessImages([], 'test-business-id')

      expect(result.success).toBe(0)
      expect(result.failed).toBe(0)
      expect(result.errors).toHaveLength(0)
      expect(result.uploadedImages).toHaveLength(0)
    })

    it('should return empty result for undefined URLs', async () => {
      const result = await processBusinessImages(undefined as any, 'test-business-id')

      expect(result.success).toBe(0)
      expect(result.failed).toBe(0)
      expect(result.errors).toHaveLength(0)
      expect(result.uploadedImages).toHaveLength(0)
    })
  })
})
