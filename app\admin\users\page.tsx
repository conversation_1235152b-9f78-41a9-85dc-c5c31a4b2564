import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { UserManagement } from "@/components/admin/user-management"
import { AdminLoading } from "@/components/admin/admin-loading"

export const metadata: Metadata = {
  title: "User Management - Admin Dashboard",
  description: "Manage platform users, accounts, and permissions",
}

export default function UserManagementPage() {
  return (
    <Suspense fallback={<AdminLoading type="table" title="User Management" />}>
      <UserManagement />
    </Suspense>
  )
}
