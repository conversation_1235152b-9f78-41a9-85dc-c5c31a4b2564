'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import type { User } from '@supabase/supabase-js'
import type { Profile } from '@/lib/types'

interface UserContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signUp: (email: string, password: string, fullName: string) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: string }>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

// Mock user data for testing - using existing user ID from database
const mockUser = {
  id: '5354596e-3cd1-4992-9824-7c0d88fe8a05',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  aud: 'authenticated',
  role: 'authenticated',
  app_metadata: {},
  user_metadata: { full_name: '<PERSON> Houser' }
} as User

const mockProfile = {
  id: '5354596e-3cd1-4992-9824-7c0d88fe8a05',
  full_name: 'Cody Houser',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
} as Profile

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(mockUser)
  const [profile, setProfile] = useState<Profile | null>(mockProfile)
  const [loading, setLoading] = useState(false) // No loading needed for mock data

  console.log("UserProvider initialized with mock data for testing")

  useEffect(() => {
    // Set mock data immediately
    setUser(mockUser)
    setProfile(mockProfile)
    setLoading(false)
  }, [])

  // Mock authentication functions for testing
  const signIn = async (email: string, password: string) => {
    console.log('Mock signIn called with:', email)
    return {}
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    console.log('Mock signUp called with:', email, fullName)
    return {}
  }

  const signOut = async () => {
    console.log('Mock signOut called')
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    console.log('Mock updateProfile called with:', updates)
    if (profile) {
      const updatedProfile = { ...profile, ...updates, updated_at: new Date().toISOString() }
      setProfile(updatedProfile)
    }
    return {}
  }

  return (
    <UserContext.Provider value={{ 
      user, 
      profile, 
      loading, 
      signIn, 
      signUp, 
      signOut, 
      updateProfile 
    }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
