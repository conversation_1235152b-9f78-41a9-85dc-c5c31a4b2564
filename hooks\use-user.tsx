'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import type { User, Session } from '@supabase/supabase-js'
import type { Profile } from '@/lib/types'
import { getAuthErrorMessage } from '@/lib/auth'
import { supabase } from '@/lib/supabase'

interface UserContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signUp: (email: string, password: string, fullName: string) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  refreshUser: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: string }>
  resetPassword: (email: string) => Promise<{ error?: string }>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Fetch user profile from database
  const fetchProfile = useCallback(async (userId: string): Promise<Profile | null> => {
    if (!supabase) {
      console.error('Supabase client not available')
      return null
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }, [])

  // Create or update user profile
  const upsertProfile = useCallback(async (user: User): Promise<Profile | null> => {
    if (!supabase) {
      console.error('Supabase client not available')
      return null
    }

    try {
      const profileData = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('profiles')
        .upsert(profileData, { onConflict: 'id' })
        .select()
        .single()

      if (error) {
        console.error('Error upserting profile:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error upserting profile:', error)
      return null
    }
  }, [])

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true)
        setError(null)

        if (!supabase) {
          console.log('Supabase client not available')
          setLoading(false)
          return
        }

        // Get initial session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Error getting session:', sessionError)
          setError(getAuthErrorMessage(sessionError))
          return
        }

        setSession(session)

        if (session?.user) {
          setUser(session.user)

          // Fetch or create profile
          let userProfile = await fetchProfile(session.user.id)
          if (!userProfile) {
            userProfile = await upsertProfile(session.user)
          }
          setProfile(userProfile)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        setError(getAuthErrorMessage(error))
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()

    // Listen for auth changes
    if (supabase) {
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          console.log('Auth state changed:', event, session?.user?.email)

          setSession(session)
          setUser(session?.user ?? null)

          if (session?.user) {
            // Fetch or create profile for new user
            let userProfile = await fetchProfile(session.user.id)
            if (!userProfile) {
              userProfile = await upsertProfile(session.user)
            }
            setProfile(userProfile)
          } else {
            setProfile(null)
          }

          setLoading(false)
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    }
  }, [fetchProfile, upsertProfile])

  // Authentication functions
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)

      if (!supabase) {
        const errorMessage = 'Authentication service unavailable'
        setError(errorMessage)
        return { error: errorMessage }
      }

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        const errorMessage = getAuthErrorMessage(error)
        setError(errorMessage)
        return { error: errorMessage }
      }

      return {}
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error)
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const signUp = useCallback(async (email: string, password: string, fullName: string) => {
    try {
      setLoading(true)
      setError(null)

      if (!supabase) {
        const errorMessage = 'Authentication service unavailable'
        setError(errorMessage)
        return { error: errorMessage }
      }

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      })

      if (error) {
        const errorMessage = getAuthErrorMessage(error)
        setError(errorMessage)
        return { error: errorMessage }
      }

      return {}
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error)
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const signOut = useCallback(async () => {
    try {
      setLoading(true)

      if (supabase) {
        const { error } = await supabase.auth.signOut()

        if (error) {
          console.error('Error signing out:', error)
          setError(getAuthErrorMessage(error))
        }
      }

      // Clear state regardless of API call success
      setUser(null)
      setProfile(null)
      setSession(null)
      setError(null)
      router.push('/')
    } catch (error) {
      console.error('Error signing out:', error)
      setError(getAuthErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }, [router])

  // Refresh profile data
  const refreshProfile = useCallback(async () => {
    if (user) {
      const userProfile = await fetchProfile(user.id)
      setProfile(userProfile)
    }
  }, [user, fetchProfile])

  // Refresh user data
  const refreshUser = useCallback(async () => {
    if (!supabase) {
      console.log('Supabase client not available')
      return
    }

    const { data: { user: currentUser } } = await supabase.auth.getUser()
    setUser(currentUser)

    if (currentUser) {
      const userProfile = await fetchProfile(currentUser.id)
      setProfile(userProfile)
    }
  }, [fetchProfile])

  // Update profile function
  const updateProfile = useCallback(async (updates: Partial<Profile>) => {
    if (!user || !profile) {
      return { error: 'No user logged in' }
    }

    if (!supabase) {
      return { error: 'Authentication service unavailable' }
    }

    try {
      setLoading(true)
      setError(null)

      const updatedData = {
        ...updates,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(updatedData)
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        const errorMessage = getAuthErrorMessage(error)
        setError(errorMessage)
        return { error: errorMessage }
      }

      setProfile(data)
      return {}
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error)
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [user, profile])

  // Reset password function
  const resetPassword = useCallback(async (email: string) => {
    try {
      setLoading(true)
      setError(null)

      if (!supabase) {
        const errorMessage = 'Authentication service unavailable'
        setError(errorMessage)
        return { error: errorMessage }
      }

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        const errorMessage = getAuthErrorMessage(error)
        setError(errorMessage)
        return { error: errorMessage }
      }

      return {}
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error)
      setError(errorMessage)
      return { error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [])

  const value: UserContextType = {
    user,
    profile,
    session,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    refreshProfile,
    refreshUser,
    updateProfile,
    resetPassword
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
