"use client"

import { forwardRef, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { AlertCircle, CheckCircle, Loader2, Eye, EyeOff } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BaseFieldProps {
  label?: string
  error?: string
  touched?: boolean
  required?: boolean
  disabled?: boolean
  className?: string
  description?: string
}

interface TextFieldProps extends BaseFieldProps {
  type?: 'text' | 'email' | 'password' | 'tel' | 'url'
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  maxLength?: number
  showCharCount?: boolean
  autoComplete?: string
}

interface TextareaFieldProps extends BaseFieldProps {
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  rows?: number
  maxLength?: number
  showCharCount?: boolean
}

interface SelectFieldProps extends BaseFieldProps {
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  options: { value: string; label: string }[]
}

interface CheckboxFieldProps extends BaseFieldProps {
  checked: boolean
  onChange: (checked: boolean) => void
  onBlur?: () => void
}

// Base field wrapper component
function FieldWrapper({ 
  label, 
  error, 
  touched, 
  required, 
  className, 
  description, 
  children 
}: BaseFieldProps & { children: React.ReactNode }) {
  const hasError = touched && error
  const isValid = touched && !error

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label className="text-sm font-medium text-neutral-300">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        {children}
        
        {/* Validation icon */}
        {(hasError || isValid) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-red-400" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-400" />
            )}
          </div>
        )}
      </div>
      
      {description && !hasError && (
        <p className="text-xs text-neutral-500">{description}</p>
      )}
      
      {hasError && (
        <p className="text-xs text-red-400 flex items-center">
          <AlertCircle className="h-3 w-3 mr-1" />
          {error}
        </p>
      )}
    </div>
  )
}

// Text input field
export const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
  ({ 
    type = 'text', 
    value, 
    onChange, 
    onBlur, 
    placeholder, 
    maxLength, 
    showCharCount, 
    autoComplete,
    disabled,
    ...fieldProps 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const isPassword = type === 'password'
    const inputType = isPassword && showPassword ? 'text' : type

    return (
      <FieldWrapper {...fieldProps} disabled={disabled}>
        <div className="relative">
          <Input
            ref={ref}
            type={inputType}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            placeholder={placeholder}
            maxLength={maxLength}
            autoComplete={autoComplete}
            disabled={disabled}
            className={cn(
              'bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500',
              'focus:border-blue-500/40 focus:ring-blue-500/20',
              fieldProps.touched && fieldProps.error && 'border-red-500/50 focus:border-red-500/50',
              fieldProps.touched && !fieldProps.error && 'border-green-500/50 focus:border-green-500/50',
              isPassword && 'pr-10'
            )}
          />
          
          {/* Password visibility toggle */}
          {isPassword && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-neutral-800"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-neutral-400" />
              ) : (
                <Eye className="h-4 w-4 text-neutral-400" />
              )}
            </Button>
          )}
        </div>
        
        {showCharCount && maxLength && (
          <div className="text-xs text-neutral-500 text-right">
            {value.length}/{maxLength}
          </div>
        )}
      </FieldWrapper>
    )
  }
)

TextField.displayName = 'TextField'

// Textarea field
export const TextareaField = forwardRef<HTMLTextAreaElement, TextareaFieldProps>(
  ({ 
    value, 
    onChange, 
    onBlur, 
    placeholder, 
    rows = 4, 
    maxLength, 
    showCharCount,
    disabled,
    ...fieldProps 
  }, ref) => {
    return (
      <FieldWrapper {...fieldProps} disabled={disabled}>
        <Textarea
          ref={ref}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          placeholder={placeholder}
          rows={rows}
          maxLength={maxLength}
          disabled={disabled}
          className={cn(
            'bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500',
            'focus:border-blue-500/40 focus:ring-blue-500/20',
            fieldProps.touched && fieldProps.error && 'border-red-500/50 focus:border-red-500/50',
            fieldProps.touched && !fieldProps.error && 'border-green-500/50 focus:border-green-500/50'
          )}
        />
        
        {showCharCount && maxLength && (
          <div className="text-xs text-neutral-500 text-right">
            {value.length}/{maxLength}
          </div>
        )}
      </FieldWrapper>
    )
  }
)

TextareaField.displayName = 'TextareaField'

// Select field
export function SelectField({ 
  value, 
  onChange, 
  onBlur, 
  placeholder, 
  options,
  disabled,
  ...fieldProps 
}: SelectFieldProps) {
  return (
    <FieldWrapper {...fieldProps} disabled={disabled}>
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger 
          className={cn(
            'bg-neutral-900 border-neutral-800 text-white',
            'focus:border-blue-500/40 focus:ring-blue-500/20',
            fieldProps.touched && fieldProps.error && 'border-red-500/50 focus:border-red-500/50',
            fieldProps.touched && !fieldProps.error && 'border-green-500/50 focus:border-green-500/50'
          )}
          onBlur={onBlur}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className="bg-neutral-900 border-neutral-800">
          {options.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </FieldWrapper>
  )
}

// Checkbox field
export function CheckboxField({ 
  checked, 
  onChange, 
  onBlur,
  disabled,
  ...fieldProps 
}: CheckboxFieldProps) {
  return (
    <FieldWrapper {...fieldProps} disabled={disabled}>
      <div className="flex items-center space-x-2">
        <Checkbox
          checked={checked}
          onCheckedChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          className="border-neutral-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
        />
        {fieldProps.label && (
          <Label className="text-sm text-neutral-300 cursor-pointer">
            {fieldProps.label}
            {fieldProps.required && <span className="text-red-400 ml-1">*</span>}
          </Label>
        )}
      </div>
    </FieldWrapper>
  )
}

// Loading field (for async validation)
export function LoadingField({ message = 'Validating...' }: { message?: string }) {
  return (
    <div className="flex items-center space-x-2 text-sm text-neutral-400">
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>{message}</span>
    </div>
  )
}
