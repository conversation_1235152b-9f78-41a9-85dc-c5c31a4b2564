/**
 * Global Test Setup
 * Configures the testing environment and utilities
 */

import '@testing-library/jest-dom'
import { beforeAll, afterEach, vi } from 'vitest'
import { TestSeedManager } from './database/seed-manager'
import { cleanup } from '@testing-library/react'

// Global test configuration
const TEST_TIMEOUT = 30000

// Set up test environment
beforeAll(async () => {
  // Initialize test database if needed
  if (process.env.SEED_TEST_DB === 'true') {
    const seedManager = new TestSeedManager()
    await seedManager.resetDatabase()
  }
}, TEST_TIMEOUT)

// Clean up after each test
afterEach(() => {
  cleanup()
})

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Mock Next.js router for tests
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  pathname: '/',
  query: {},
  asPath: '/',
  route: '/',
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
}

vi.mock('next/router', () => ({
  useRouter: () => mockRouter,
}))

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Supabase client for unit tests
let mockUser: any = null
let mockSession: any = null

vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { user: mockUser }, error: null })
      ),
      signInWithPassword: vi.fn().mockImplementation(({ email, password }) => {
        if (email === '<EMAIL>' && password === 'testpassword123') {
          mockUser = { id: '123', email: '<EMAIL>', email_confirmed_at: new Date().toISOString() }
          mockSession = { user: mockUser, access_token: 'mock-token' }
          return Promise.resolve({ data: { user: mockUser, session: mockSession }, error: null })
        }
        return Promise.resolve({ data: { user: null, session: null }, error: { message: 'Invalid login credentials' } })
      }),
      signUp: vi.fn().mockImplementation(({ email, password, options }) => {
        mockUser = { 
          id: '123', 
          email, 
          email_confirmed_at: new Date().toISOString(),
          user_metadata: options?.data || {}
        }
        mockSession = { user: mockUser, access_token: 'mock-token' }
        return Promise.resolve({ data: { user: mockUser, session: mockSession }, error: null })
      }),
      signOut: vi.fn().mockImplementation(() => {
        mockUser = null
        mockSession = null
        return Promise.resolve({ error: null })
      }),
      resetPasswordForEmail: vi.fn().mockResolvedValue({ error: null }),
      updateUser: vi.fn().mockResolvedValue({ data: { user: { id: '123' } }, error: null }),
      getSession: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { session: mockSession }, error: null })
      ),
      refreshSession: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { session: mockSession }, error: null })
      ),
      onAuthStateChange: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      like: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    })),
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn(),
        download: vi.fn(),
        remove: vi.fn(),
        list: vi.fn(),
      })),
    },
  },
  createServerClient: vi.fn().mockResolvedValue({
    auth: {
      getUser: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { user: mockUser }, error: null })
      ),
      signInWithPassword: vi.fn().mockImplementation(({ email, password }) => {
        if (email === '<EMAIL>' && password === 'testpassword123') {
          mockUser = { id: '123', email: '<EMAIL>', email_confirmed_at: new Date().toISOString() }
          mockSession = { user: mockUser, access_token: 'mock-token' }
          return Promise.resolve({ data: { user: mockUser, session: mockSession }, error: null })
        }
        return Promise.resolve({ data: { user: null, session: null }, error: { message: 'Invalid login credentials' } })
      }),
      signUp: vi.fn().mockImplementation(({ email, password, options }) => {
        mockUser = { 
          id: '123', 
          email, 
          email_confirmed_at: new Date().toISOString(),
          user_metadata: options?.data || {}
        }
        mockSession = { user: mockUser, access_token: 'mock-token' }
        return Promise.resolve({ data: { user: mockUser, session: mockSession }, error: null })
      }),
      signOut: vi.fn().mockImplementation(() => {
        mockUser = null
        mockSession = null
        return Promise.resolve({ error: null })
      }),
      resetPasswordForEmail: vi.fn().mockResolvedValue({ error: null }),
      updateUser: vi.fn().mockResolvedValue({ data: { user: { id: '123' } }, error: null }),
      getSession: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { session: mockSession }, error: null })
      ),
      refreshSession: vi.fn().mockImplementation(() => 
        Promise.resolve({ data: { session: mockSession }, error: null })
      ),
      onAuthStateChange: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      like: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    })),
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn(),
        download: vi.fn(),
        remove: vi.fn(),
        list: vi.fn(),
      })),
    },
  }),
}))

// Global test utilities
global.testUtils = {
  // Add any global test utilities here
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock fetch for API tests
  mockFetch: (response: any, status = 200) => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: status >= 200 && status < 300,
        status,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response)),
      })
    ) as any
  },
  
  // Reset all mocks
  resetMocks: () => {
    vi.clearAllMocks()
  },
}

// Declare global types
declare global {
  var testUtils: {
    waitFor: (ms: number) => Promise<void>
    mockFetch: (response: any, status?: number) => void
    resetMocks: () => void
  }
}