import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = await createServerClient()

  try {
    // Get the current user session
    const {
      data: { user },
      error
    } = supabase ? await supabase.auth.getUser() : { data: { user: null }, error: null }

    if (error) {
      console.error('Middleware auth error:', error)
    }

    const { pathname } = req.nextUrl

    // Define protected routes that require authentication
    const protectedRoutes = ['/dashboard', '/onboarding']
    const authRoutes = ['/auth/login', '/auth/signup', '/auth/forgot-password']

    // Check if current path is a protected route
    const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
    const isAuthRoute = authRoutes.some(route => pathname.startsWith(route))

    // If user is not signed in and trying to access a protected route
    if (!user && isProtectedRoute) {
      const redirectUrl = new URL('/auth/login', req.url)
      redirectUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // If user is signed in and trying to access auth routes, redirect to dashboard
    if (user && isAuthRoute) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    // For API routes that require authentication
    if (pathname.startsWith('/api/') && !pathname.startsWith('/api/search') && !pathname.startsWith('/api/auth/')) {
      // Allow certain public API routes
      const publicApiRoutes = ['/api/businesses/', '/api/reviews/']
      const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))

      if (!isPublicApiRoute && !user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    }

    return res
  } catch (error) {
    console.error('Middleware error:', error)
    return res
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}