"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Search, 
  RefreshCw, 
  MessageSquare, 
  Building2, 
  User,
  Clock,
  CheckCircle2,
  XCircle
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import type { MessageThreadWithDetails } from "@/lib/types"

interface MessageThreadListProps {
  threads: MessageThreadWithDetails[]
  selectedThread: MessageThreadWithDetails | null
  onThreadSelect: (thread: MessageThreadWithDetails) => void
  loading: boolean
  onRefresh: () => void
}

export function MessageThreadList({
  threads,
  selectedThread,
  onThreadSelect,
  loading,
  onRefresh
}: MessageThreadListProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const filteredThreads = threads.filter(thread => {
    if (!searchQuery) return true
    
    const query = searchQuery.toLowerCase()
    return (
      thread.subject?.toLowerCase().includes(query) ||
      thread.business?.name.toLowerCase().includes(query) ||
      thread.user?.full_name?.toLowerCase().includes(query)
    )
  })

  const getThreadStatus = (thread: MessageThreadWithDetails) => {
    switch (thread.status) {
      case 'active':
        return { icon: CheckCircle2, color: 'text-green-400', label: 'Active' }
      case 'closed':
        return { icon: XCircle, color: 'text-red-400', label: 'Closed' }
      case 'archived':
        return { icon: Clock, color: 'text-neutral-400', label: 'Archived' }
      default:
        return { icon: MessageSquare, color: 'text-blue-400', label: 'Active' }
    }
  }

  const getUnreadCount = (thread: MessageThreadWithDetails) => {
    if (!thread.messages) return 0
    return thread.messages.filter(msg => !msg.read_at && msg.author_id !== thread.user_id).length
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-white">Conversations</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="text-neutral-400 hover:text-white"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-neutral-800 border-neutral-700 text-white"
          />
        </div>
      </div>

      {/* Thread List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {loading ? (
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="p-3 rounded-lg bg-neutral-800 animate-pulse">
                  <div className="h-4 bg-neutral-700 rounded mb-2"></div>
                  <div className="h-3 bg-neutral-700 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : filteredThreads.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-neutral-600 mx-auto mb-3" />
              <p className="text-neutral-400 mb-2">
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </p>
              <p className="text-neutral-500 text-sm">
                {searchQuery ? 'Try a different search term' : 'Start a conversation with a business'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredThreads.map((thread) => {
                const status = getThreadStatus(thread)
                const unreadCount = getUnreadCount(thread)
                const isSelected = selectedThread?.id === thread.id
                const lastMessage = thread.messages?.[thread.messages.length - 1]

                return (
                  <button
                    key={thread.id}
                    onClick={() => onThreadSelect(thread)}
                    className={`w-full p-3 rounded-lg text-left transition-colors ${
                      isSelected
                        ? 'bg-blue-500/20 border border-blue-500/30'
                        : 'hover:bg-neutral-800 border border-transparent'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        {thread.business ? (
                          <Building2 className="h-4 w-4 text-blue-400 flex-shrink-0" />
                        ) : (
                          <User className="h-4 w-4 text-green-400 flex-shrink-0" />
                        )}
                        <span className="font-medium text-white truncate">
                          {thread.business?.name || thread.user?.full_name || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        {unreadCount > 0 && (
                          <Badge variant="secondary" className="bg-blue-500 text-white text-xs">
                            {unreadCount}
                          </Badge>
                        )}
                        <status.icon className={`h-3 w-3 ${status.color}`} />
                      </div>
                    </div>

                    <div className="mb-2">
                      <p className="text-sm font-medium text-neutral-300 truncate">
                        {thread.subject || 'No subject'}
                      </p>
                      {lastMessage && (
                        <p className="text-xs text-neutral-500 truncate mt-1">
                          {lastMessage.content}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-xs text-neutral-500">
                      <span>
                        {formatDistanceToNow(new Date(thread.updated_at), { addSuffix: true })}
                      </span>
                      <span className="capitalize">{status.label}</span>
                    </div>
                  </button>
                )
              })}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
