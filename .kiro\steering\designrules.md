---
inclusion: fileMatch
fileMatchPattern: ['**/*.tsx', '**/components/**/*.ts']
---

# Design System & UI Guidelines

## Core Design Tokens

### Colors (Dark Theme)
- **Background**: `bg-black` or `bg-neutral-950`
- **Cards**: `bg-neutral-900` with `border-neutral-800`
- **Nested Cards**: `bg-neutral-800` with `border-neutral-700`
- **Text Primary**: `text-white`
- **Text Secondary**: `text-neutral-400`
- **Text Muted**: `text-neutral-500`
- **Primary Blue**: `text-blue-400`, `bg-blue-500`, `border-blue-500`
- **Success**: `text-green-400`, `bg-green-500`
- **Warning**: `text-yellow-400`, `bg-yellow-500`
- **Error**: `text-red-400`, `bg-red-500`

### Typography
- **Headings**: Use `font-medium` or `font-semibold`
- **Body**: Default weight, `text-neutral-400` for descriptions
- **Labels**: `text-white` for form labels and important text
- **Font**: Geist font family (configured in globals.css)

## Component Patterns

### Cards
```tsx
// Standard card
className="bg-neutral-900 border border-neutral-800 rounded-lg"

// Interactive card with hover
className="bg-neutral-900 border border-neutral-800 rounded-lg card-hover-blue"

// Nested card (inside modals/dialogs)
className="bg-neutral-800 border border-neutral-700 rounded-lg"
```

### Buttons
```tsx
// Primary action
<Button className="bg-blue-gradient-hover text-white shadow-lg">

// Outline button
<Button variant="outline" className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300">

// Ghost button
<Button variant="ghost" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10">

// Destructive action
<Button variant="ghost" className="text-red-400 hover:text-red-300 hover:bg-neutral-800">
```

### Form Elements
```tsx
// Input fields
<Input className="bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500" />

// Nested inputs (in cards/modals)
<Input className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500" />

// Select components
<Select>
  <SelectTrigger className="bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40">
  <SelectContent className="bg-neutral-900 border-neutral-800">
    <SelectItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
```

### Badges
```tsx
// Active/Success state
<Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">

// Warning state
<Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">

// Neutral/Default
<Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">

// Tag badges (inside cards)
<Badge className="bg-neutral-800 text-neutral-400 border-neutral-700">
```

### Tabs
```tsx
<TabsList className="bg-neutral-900 border-blue-500/20">
  <TabsTrigger className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400 hover:bg-blue-500/5">
```

## Business-Specific Patterns

### Business Cards
- Use `card-hover-blue` class for interactive business listings
- Rating stars: `text-yellow-400` for filled, `text-neutral-600` for empty
- Service tags: Use neutral badges with `bg-neutral-800`
- Premium indicators: Use `bg-blue-gradient` badges

### Search & Filters
- Search inputs: `bg-neutral-900` with blue focus states
- Filter buttons: Outline variant with blue accents
- Location inputs: Include `MapPin` icon from `lucide-react`

### Reviews & Ratings
- Star ratings: `★` character with `text-yellow-400`
- Review cards: Standard card styling with `bg-neutral-900`
- User avatars: Default to `User` icon with blue gradient background

## Implementation Rules

### Icons
- Always use `lucide-react` icons
- Default color: `text-neutral-400` or `text-neutral-500`
- In accent elements: Match the accent color

### Responsive Design
- Always include responsive breakpoints: `sm:`, `md:`, `lg:`, `xl:`
- Mobile-first approach with progressive enhancement
- Test all components at mobile, tablet, and desktop sizes

### Accessibility
- Use semantic HTML elements (`<main>`, `<section>`, `<article>`)
- Include proper ARIA attributes for interactive elements
- Provide `alt` text for meaningful images
- Use `sr-only` for screen reader context when needed
- Ensure color contrast meets WCAG guidelines

### Code Style
- Use Tailwind utility classes, avoid custom CSS when possible
- Group related classes: layout → colors → typography → effects
- Use consistent spacing scale (4, 6, 8, 12, 16, 24, 32)
- Prefer composition over complex single components

### Custom Classes Available
- `bg-blue-gradient`: Blue gradient background
- `bg-blue-gradient-hover`: Interactive gradient with hover effects
- `card-hover-blue`: Card hover effect with blue accent
- `glow-blue`: Subtle blue glow effect
- `glow-blue-strong`: Prominent blue glow effect