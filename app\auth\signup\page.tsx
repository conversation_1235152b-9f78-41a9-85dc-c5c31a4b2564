'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'
import { useUser } from '@/hooks/use-user'
import { isValidEmail, validatePassword } from '@/lib/auth'

export default function SignupPage() {
  const [fullName, setFullName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [fieldErrors, setFieldErrors] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  
  const router = useRouter()
  const { signUp, user } = useUser()

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push('/dashboard')
    }
  }, [user, router])

  // Validate form fields
  const validateForm = () => {
    let isValid = true
    const errors = {
      fullName: '',
      email: '',
      password: '',
      confirmPassword: ''
    }
    
    // Reset errors
    setError('')
    
    // Validate full name
    if (!fullName.trim()) {
      errors.fullName = 'Full name is required'
      isValid = false
    } else if (fullName.trim().length < 2) {
      errors.fullName = 'Full name must be at least 2 characters'
      isValid = false
    }
    
    // Validate email
    if (!email) {
      errors.email = 'Email is required'
      isValid = false
    } else if (!isValidEmail(email)) {
      errors.email = 'Please enter a valid email address'
      isValid = false
    }
    
    // Validate password
    if (!password) {
      errors.password = 'Password is required'
      isValid = false
    } else {
      const passwordValidation = validatePassword(password)
      if (!passwordValidation.isValid) {
        errors.password = passwordValidation.message || 'Invalid password'
        isValid = false
      }
    }
    
    // Validate confirm password
    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
      isValid = false
    } else if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
      isValid = false
    }
    
    setFieldErrors(errors)
    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setLoading(true)
    setError('')
    setSuccess(false)
    
    try {
      const result = await signUp(email, password, fullName.trim())
      
      if (result.error) {
        setError(result.error)
      } else {
        setSuccess(true)
        // Don't redirect immediately - show success message first
        setTimeout(() => {
          router.push('/auth/login?message=Please check your email to confirm your account')
        }, 2000)
      }
    } catch (error) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-neutral-900 border-neutral-800">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white mb-2">
                  Account created successfully!
                </h2>
                <p className="text-neutral-400 text-sm">
                  We've sent a confirmation email to <strong>{email}</strong>. 
                  Please check your inbox and click the confirmation link to activate your account.
                </p>
              </div>
              <Button
                onClick={() => router.push('/auth/login')}
                className="w-full bg-blue-gradient-hover text-white"
              >
                Continue to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-neutral-900 border-neutral-800">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-semibold text-white text-center">
            Create your account
          </CardTitle>
          <CardDescription className="text-neutral-400 text-center">
            Join PressureWash Pro and grow your business
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error message */}
          {error && (
            <Alert className="bg-red-500/10 border-red-500/20 text-red-400">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Full name field */}
            <div className="space-y-2">
              <Label htmlFor="fullName" className="text-white">
                Full Name
              </Label>
              <Input
                id="fullName"
                type="text"
                placeholder="Enter your full name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                disabled={loading}
                autoComplete="name"
              />
              {fieldErrors.fullName && (
                <p className="text-sm text-red-400">{fieldErrors.fullName}</p>
              )}
            </div>
            
            {/* Email field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                disabled={loading}
                autoComplete="email"
              />
              {fieldErrors.email && (
                <p className="text-sm text-red-400">{fieldErrors.email}</p>
              )}
            </div>
            
            {/* Password field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-white">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Create a password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500 pr-10"
                  disabled={loading}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-300"
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {fieldErrors.password && (
                <p className="text-sm text-red-400">{fieldErrors.password}</p>
              )}
            </div>
            
            {/* Confirm password field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-white">
                Confirm Password
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500 pr-10"
                  disabled={loading}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-300"
                  disabled={loading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {fieldErrors.confirmPassword && (
                <p className="text-sm text-red-400">{fieldErrors.confirmPassword}</p>
              )}
            </div>
            
            {/* Submit button */}
            <Button
              type="submit"
              className="w-full bg-blue-gradient-hover text-white shadow-lg"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating account...
                </>
              ) : (
                'Create account'
              )}
            </Button>
          </form>
          
          {/* Sign in link */}
          <div className="text-center text-sm text-neutral-400">
            Already have an account?{' '}
            <Link
              href="/auth/login"
              className="text-blue-400 hover:text-blue-300 hover:underline"
            >
              Sign in
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
