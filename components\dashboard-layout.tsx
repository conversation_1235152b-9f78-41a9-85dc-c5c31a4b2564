"use client"

import { useState } from "react"
import { Head<PERSON> } from "@/components/header"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { ProfileTab } from "@/components/dashboard/profile-tab"
import { GalleryTab } from "@/components/dashboard/gallery-tab"
import { ServicesTab } from "@/components/dashboard/services-tab"
import { ReviewsTab } from "@/components/dashboard/reviews-tab"
import { LeadsTab } from "@/components/dashboard/leads-tab"
import { SubscriptionTab } from "@/components/dashboard/subscription-tab"
import { BusinessOnboarding } from "@/components/dashboard/business-onboarding"
import type { Business } from "@/lib/types"

interface DashboardLayoutProps {
  initialBusiness: Business | null
}

export type DashboardTab = "profile" | "gallery" | "services" | "reviews" | "leads" | "subscription"

export function DashboardLayout({ initialBusiness }: DashboardLayoutProps) {
  const [activeTab, setActiveTab] = useState<DashboardTab>("profile")
  const [business, setBusiness] = useState<Business | null>(initialBusiness)

  // If no business exists, show onboarding
  if (!business) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <BusinessOnboarding onBusinessCreated={setBusiness} />
      </div>
    )
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return <ProfileTab business={business} onBusinessUpdate={setBusiness} />
      case "gallery":
        return <GalleryTab business={business} />
      case "services":
        return <ServicesTab business={business} />
      case "reviews":
        return <ReviewsTab business={business} />
      case "leads":
        return <LeadsTab business={business} />
      case "subscription":
        return <SubscriptionTab business={business} />
      default:
        return <ProfileTab business={business} onBusinessUpdate={setBusiness} />
    }
  }

  return (
    <div className="min-h-screen bg-black">
      <Header />

      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">Business Dashboard</h1>
          <p className="text-neutral-400">Manage your pressure washing business listing</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <DashboardSidebar activeTab={activeTab} onTabChange={setActiveTab} business={business} />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">{renderTabContent()}</div>
        </div>
      </div>
    </div>
  )
}
