-- Create quote_requests table for storing customer quote requests
CREATE TABLE IF NOT EXISTS quote_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
  
  -- Customer Information
  customer_name VARCHAR(255) NOT NULL,
  customer_email VARCHAR(255) NOT NULL,
  customer_phone VARCHAR(50) NOT NULL,
  
  -- Service Address
  service_address TEXT NOT NULL,
  service_city VARCHAR(100) NOT NULL,
  service_state VARCHAR(50) NOT NULL,
  service_zip_code VARCHAR(20) NOT NULL,
  
  -- Project Details
  property_type VARCHAR(50) NOT NULL DEFAULT 'residential' CHECK (property_type IN ('residential', 'commercial')),
  service_type VARCHAR(255),
  square_footage VARCHAR(50),
  project_description TEXT NOT NULL,
  additional_notes TEXT,
  
  -- Scheduling
  preferred_date DATE,
  urgency VARCHAR(50) NOT NULL DEFAULT 'flexible' CHECK (urgency IN ('asap', 'within_week', 'within_month', 'flexible')),
  
  -- Status and Metadata
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'quoted', 'accepted', 'declined', 'completed')),
  quote_amount DECIMAL(10,2),
  quote_notes TEXT,
  business_response TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  responded_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_quote_requests_business_id ON quote_requests(business_id);
CREATE INDEX IF NOT EXISTS idx_quote_requests_status ON quote_requests(status);
CREATE INDEX IF NOT EXISTS idx_quote_requests_created_at ON quote_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_quote_requests_customer_email ON quote_requests(customer_email);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_quote_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER quote_requests_updated_at
  BEFORE UPDATE ON quote_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_quote_requests_updated_at();

-- Enable Row Level Security
ALTER TABLE quote_requests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Business owners can see their own quote requests
CREATE POLICY "Business owners can view their quote requests" ON quote_requests
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses 
      WHERE owner_id = auth.uid()
    )
  );

-- Business owners can update their own quote requests
CREATE POLICY "Business owners can update their quote requests" ON quote_requests
  FOR UPDATE USING (
    business_id IN (
      SELECT id FROM businesses 
      WHERE owner_id = auth.uid()
    )
  );

-- Anyone can create quote requests (for now, since auth is disabled)
CREATE POLICY "Anyone can create quote requests" ON quote_requests
  FOR INSERT WITH CHECK (true);

-- Customers can view their own quote requests by email
CREATE POLICY "Customers can view their own quote requests" ON quote_requests
  FOR SELECT USING (customer_email = auth.email());

COMMENT ON TABLE quote_requests IS 'Stores customer quote requests for pressure washing services';
COMMENT ON COLUMN quote_requests.status IS 'Current status of the quote request';
COMMENT ON COLUMN quote_requests.urgency IS 'How quickly the customer needs the service';
COMMENT ON COLUMN quote_requests.property_type IS 'Type of property (residential or commercial)';
