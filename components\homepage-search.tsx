"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

export function HomepageSearch() {
  const [location, setLocation] = useState("")
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const params = new URLSearchParams()
    if (location) params.set('location', location)
    router.push(`/search?${params.toString()}`)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  return (
    <form 
      onSubmit={handleSubmit}
      className="flex flex-col md:flex-row gap-4 p-6 bg-neutral-900 rounded-xl border border-neutral-800"
    >
      <input
        type="text"
        placeholder="Enter city, state, or zip code..."
        value={location}
        onChange={(e) => setLocation(e.target.value)}
        onKeyDown={handleKeyDown}
        className="flex-1 px-4 py-3 bg-neutral-800 border border-neutral-700 rounded-xl text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <button
        type="submit"
        className="px-8 py-3 bg-blue-gradient-hover text-white font-medium rounded-xl text-center hover:opacity-90 transition-opacity"
      >
        Search
      </button>
    </form>
  )
}
