import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Mail, Phone, MapPin, Clock, MessageSquare, HelpCircle, Building } from "lucide-react"
import Link from "next/link"

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Contact <span className="text-white">Us</span>
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-2xl mx-auto">
            Have a question or feedback? We'd love to hear from you. Our team is here to help you get the most out of
            PressureWash Pro.
          </p>
        </div>
      </section>

      {/* Contact Content */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <Card className="bg-neutral-900 border-neutral-800 mb-8">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <MessageSquare className="h-6 w-6 mr-2 text-blue-400" />
                    Get in Touch Directly
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <Mail className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Email Support</h3>
                      <p className="text-neutral-400 mb-2"><EMAIL></p>
                      <p className="text-sm text-neutral-500">We typically respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <Phone className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Phone Support</h3>
                      <p className="text-neutral-400 mb-2">(*************</p>
                      <p className="text-sm text-neutral-500">Mon-Fri: 9:00 AM - 6:00 PM MST</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <MapPin className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Office Address</h3>
                      <p className="text-neutral-400 mb-1">123 Business Plaza, Suite 456</p>
                      <p className="text-neutral-400 mb-2">Phoenix, AZ 85001</p>
                      <p className="text-sm text-neutral-500">By appointment only</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <Clock className="h-6 w-6 text-blue-400 mt-1" />
                    <div>
                      <h3 className="text-white font-semibold mb-1">Business Hours</h3>
                      <div className="text-neutral-400 space-y-1">
                        <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                        <p>Saturday: 10:00 AM - 4:00 PM</p>
                        <p>Sunday: Closed</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Support Categories */}
              <Card className="bg-neutral-900 border-neutral-800">
                <CardHeader>
                  <CardTitle className="text-white">Support Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3 p-3 bg-neutral-800 rounded-lg">
                      <Building className="h-5 w-5 text-blue-400" />
                      <div>
                        <div className="text-white font-medium">Business Owners</div>
                        <div className="text-sm text-neutral-400">Listing & account help</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-neutral-800 rounded-lg">
                      <MessageSquare className="h-5 w-5 text-blue-400" />
                      <div>
                        <div className="text-white font-medium">Customers</div>
                        <div className="text-sm text-neutral-400">Search & booking support</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-neutral-800 rounded-lg">
                      <HelpCircle className="h-5 w-5 text-blue-400" />
                      <div>
                        <div className="text-white font-medium">Technical Issues</div>
                        <div className="text-sm text-neutral-400">Bug reports & troubleshooting</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-neutral-800 rounded-lg">
                      <Mail className="h-5 w-5 text-blue-400" />
                      <div>
                        <div className="text-white font-medium">General Inquiries</div>
                        <div className="text-sm text-neutral-400">Questions & feedback</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div>
              <Card className="bg-neutral-900 border-neutral-800">
                <CardHeader>
                  <CardTitle className="text-white">Send Us a Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName" className="text-neutral-300">
                          First Name
                        </Label>
                        <Input
                          id="firstName"
                          placeholder="John"
                          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-neutral-300">
                          Last Name
                        </Label>
                        <Input
                          id="lastName"
                          placeholder="Doe"
                          className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email" className="text-neutral-300">
                        Email Address
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                      />
                    </div>

                    <div>
                      <Label htmlFor="category" className="text-neutral-300">
                        Category
                      </Label>
                      <Select>
                        <SelectTrigger className="bg-neutral-800 border-neutral-700 text-white">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent className="bg-neutral-800 border-neutral-700">
                          <SelectItem value="business">Business Owner Support</SelectItem>
                          <SelectItem value="customer">Customer Support</SelectItem>
                          <SelectItem value="technical">Technical Issue</SelectItem>
                          <SelectItem value="general">General Inquiry</SelectItem>
                          <SelectItem value="feedback">Feedback</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="subject" className="text-neutral-300">
                        Subject
                      </Label>
                      <Input
                        id="subject"
                        placeholder="Brief description of your inquiry"
                        className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                      />
                    </div>

                    <div>
                      <Label htmlFor="message" className="text-neutral-300">
                        Message
                      </Label>
                      <Textarea
                        id="message"
                        placeholder="Please provide details about your inquiry..."
                        rows={6}
                        className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                      />
                    </div>

                    <Button className="w-full bg-blue-gradient-hover">Submit Message</Button>

                    <p className="text-sm text-neutral-500 text-center">
                      We respect your privacy. Your information will never be shared with third parties.
                    </p>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 bg-neutral-900/50">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Frequently Asked Questions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-3">How do I list my business?</h3>
                <p className="text-neutral-400">
                  Simply sign up for a free account and complete our onboarding process. You'll be live and receiving
                  leads within 24 hours.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-3">Is there a setup fee?</h3>
                <p className="text-neutral-400">
                  No setup fees! Our basic listing is completely free. Premium features are available with our paid
                  plans.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-3">How do customers find me?</h3>
                <p className="text-neutral-400">
                  Customers search by location and service type. Your business appears in results based on proximity,
                  ratings, and profile completeness.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-3">Can I cancel anytime?</h3>
                <p className="text-neutral-400">
                  Yes, you can cancel your premium subscription at any time. Your basic listing will remain active even
                  after cancellation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">PressureWash Pro</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/for-businesses" className="hover:text-white transition-colors">
                    List Your Business
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
