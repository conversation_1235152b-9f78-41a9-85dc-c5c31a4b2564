import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const supabase = await supabaseAdmin
    if (!supabase) {
      return NextResponse.json({ error: 'Database not configured' }, { status: 500 })
    }
    const body = await request.json()

    const {
      businessId,
      customerInfo,
      serviceAddress,
      projectDetails
    } = body

    // Validate required fields
    if (!customerInfo?.name || !customerInfo?.email || !customerInfo?.phone) {
      return NextResponse.json(
        { error: 'Customer information is required' },
        { status: 400 }
      )
    }

    if (!serviceAddress?.street || !serviceAddress?.city || !serviceAddress?.state || !serviceAddress?.zipCode) {
      return NextResponse.json(
        { error: 'Service address is required' },
        { status: 400 }
      )
    }

    if (!projectDetails?.description) {
      return NextResponse.json(
        { error: 'Project description is required' },
        { status: 400 }
      )
    }

    // Verify business exists
    const { slug } = await params
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name, email')
      .eq('slug', slug)
      .single()

    if (businessError || !business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      )
    }

    // Create quote request record
    const { data: quoteRequest, error: insertError } = await supabase
      .from('quote_requests')
      .insert({
        business_id: business.id,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        service_address: `${serviceAddress.street}, ${serviceAddress.city}, ${serviceAddress.state} ${serviceAddress.zipCode}`,
        service_city: serviceAddress.city,
        service_state: serviceAddress.state,
        service_zip_code: serviceAddress.zipCode,
        property_type: projectDetails.propertyType || 'residential',
        service_type: projectDetails.serviceType,
        square_footage: projectDetails.squareFootage,
        project_description: projectDetails.description,
        additional_notes: projectDetails.additionalNotes,
        preferred_date: projectDetails.preferredDate || null,
        urgency: projectDetails.urgency || 'flexible',
        status: 'pending'
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating quote request:', insertError)
      return NextResponse.json(
        { error: 'Failed to create quote request' },
        { status: 500 }
      )
    }

    // TODO: Send email notification to business owner
    // This would typically integrate with an email service like SendGrid, Resend, etc.

    return NextResponse.json({
      success: true,
      quoteRequest: {
        id: quoteRequest.id,
        status: quoteRequest.status,
        created_at: quoteRequest.created_at
      }
    })

  } catch (error) {
    console.error('Quote request error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
