#!/usr/bin/env node

/**
 * Comprehensive Test Runner
 * Runs all types of tests in sequence with proper setup and cleanup
 */

const { execSync } = require('child_process')
const { existsSync } = require('fs')

// Test configuration
const TEST_TYPES = {
  unit: {
    name: 'Unit Tests',
    command: 'npm run test',
    description: 'Component and utility function tests',
  },
  integration: {
    name: 'Integration Tests',
    command: 'npm run test:integration',
    description: 'Database and API integration tests',
  },
  e2e: {
    name: 'End-to-End Tests',
    command: 'npm run test:e2e',
    description: 'Full user journey tests',
  },
}

function checkPrerequisites() {
  console.log('🔍 Checking test prerequisites...')
  
  const requiredFiles = [
    '.env.test.local',
    'vitest.config.ts',
    'playwright.config.ts',
  ]

  let allPresent = true

  for (const file of requiredFiles) {
    if (existsSync(file)) {
      console.log(`✅ ${file} exists`)
    } else {
      console.error(`❌ ${file} missing`)
      allPresent = false
    }
  }

  if (!allPresent) {
    console.log('\n💡 Run `node scripts/setup-test-env.js` to set up the test environment')
    return false
  }

  return true
}

function runCommand(command, description) {
  console.log(`\n🚀 Running: ${description}`)
  console.log(`   Command: ${command}`)
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'test' }
    })
    return true
  } catch (error) {
    console.error(`❌ Command failed with exit code: ${error.status}`)
    return false
  }
}

async function runTestSuite(testTypes = Object.keys(TEST_TYPES)) {
  console.log('🧪 Running Comprehensive Test Suite\n')
  
  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1)
  }

  const results = {
    passed: 0,
    failed: 0,
    skipped: 0,
    details: []
  }

  // Run each test type
  for (const testType of testTypes) {
    if (!TEST_TYPES[testType]) {
      console.warn(`⚠️  Unknown test type: ${testType}`)
      results.skipped++
      continue
    }

    const test = TEST_TYPES[testType]
    console.log(`\n${'='.repeat(60)}`)
    console.log(`📋 ${test.name}`)
    console.log(`   ${test.description}`)
    console.log('='.repeat(60))

    const success = runCommand(test.command, test.name)
    
    if (success) {
      results.passed++
      results.details.push({ name: test.name, status: 'PASSED' })
    } else {
      results.failed++
      results.details.push({ name: test.name, status: 'FAILED' })
    }
  }

  // Print summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 Test Suite Summary')
  console.log('='.repeat(60))
  
  results.details.forEach(result => {
    const icon = result.status === 'PASSED' ? '✅' : '❌'
    console.log(`${icon} ${result.name}: ${result.status}`)
  })

  console.log(`\n📈 Overall Results:`)
  console.log(`   ✅ Passed: ${results.passed}`)
  console.log(`   ❌ Failed: ${results.failed}`)
  console.log(`   ⏭️  Skipped: ${results.skipped}`)
  
  const total = results.passed + results.failed
  const successRate = total > 0 ? Math.round((results.passed / total) * 100) : 0
  console.log(`   📊 Success Rate: ${successRate}%`)

  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Your application is ready for deployment.')
  } else {
    console.log('\n⚠️  Some tests failed. Please review the output above.')
  }

  return results.failed === 0
}

// Parse command line arguments
const args = process.argv.slice(2)
const testTypes = args.length > 0 ? args : Object.keys(TEST_TYPES)

// Validate test types
const validTypes = testTypes.filter(type => TEST_TYPES[type])
const invalidTypes = testTypes.filter(type => !TEST_TYPES[type])

if (invalidTypes.length > 0) {
  console.error(`❌ Invalid test types: ${invalidTypes.join(', ')}`)
  console.log(`   Valid types: ${Object.keys(TEST_TYPES).join(', ')}`)
  process.exit(1)
}

// Run tests
runTestSuite(validTypes)
  .then(success => {
    process.exit(success ? 0 : 1)
  })
  .catch(error => {
    console.error('💥 Test runner crashed:', error.message)
    process.exit(1)
  })