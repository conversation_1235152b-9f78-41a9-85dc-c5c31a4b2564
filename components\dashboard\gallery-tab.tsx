"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import type { Business, PortfolioImage } from "@/lib/types"
import { Upload, Trash2, ImageIcon } from "lucide-react"
import Image from "next/image"

interface GalleryTabProps {
  business: Business & { slug: string }
}

export function GalleryTab({ business }: GalleryTabProps) {
  const { toast } = useToast()
  const [images, setImages] = useState<PortfolioImage[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    fetchImages()
  }, [business.id])

  const fetchImages = async () => {
    try {
      if (!supabase) {
        // No images when Supabase is not configured
        setImages([])
        return
      }

      const { data, error } = await supabase
        .from("portfolio_images")
        .select("*")
        .eq("business_id", business.id)
        .order("uploaded_at", { ascending: false })

      if (error) throw error
      setImages(data || [])
    } catch (error) {
      console.error("Error fetching images:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    try {
      // Validate file type
      const allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"]
      if (!allowedTypes.includes(file.type)) {
        throw new Error("Please upload a valid image file (JPEG, PNG, WebP, or GIF)")
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error("File size must be less than 5MB")
      }

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop()
      const fileName = `${business.id}/${Date.now()}.${fileExt}`

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("business-portfolios")
        .upload(fileName, file)

      if (uploadError) {
        console.error("Storage upload error:", uploadError)

        // Fallback: Create a placeholder entry in the database
        console.log("Storage failed, creating placeholder entry...")

        const placeholderImage = {
          business_id: business.id,
          image_url: `data:image/svg+xml;base64,${btoa(`<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#1f2937"/><text x="50%" y="50%" font-family="Arial" font-size="14" fill="#60a5fa" text-anchor="middle" dy=".3em">Storage Error: ${file.name}</text><text x="50%" y="70%" font-family="Arial" font-size="12" fill="#9ca3af" text-anchor="middle" dy=".3em">Please configure storage bucket</text></svg>`)}`,
          caption: `Storage Error: ${file.name}`,
          display_order: images.length
        }

        const { error: dbError } = await supabase
          .from("portfolio_images")
          .insert(placeholderImage)

        if (dbError) {
          throw new Error(`Database error: ${dbError.message}`)
        }

        toast({
          title: "Storage Issue",
          description: "Image saved as placeholder. Please configure storage bucket for real uploads.",
          variant: "destructive"
        })

        await fetchImages() // Refresh the list
        return
      }

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from("business-portfolios").getPublicUrl(fileName)

      // Save to database using API endpoint
      const response = await fetch(`/api/businesses/${business.slug}/portfolio`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          image_url: publicUrl,
          caption: "",
          display_order: 0
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save image')
      }

      const { image } = await response.json()
      setImages((prev) => [image, ...prev])
      toast({
        title: "Image Uploaded!",
        description: "Your image has been added to the gallery.",
      })
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        title: "Upload Failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      })
    } finally {
      setUploading(false)
    }
  }

  const handleDeleteImage = async (imageId: string) => {
    try {
      const response = await fetch(`/api/businesses/${business.slug}/portfolio?imageId=${imageId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete image')
      }

      setImages((prev) => prev.filter((img) => img.id !== imageId))
      toast({
        title: "Image Deleted",
        description: "The image has been removed from your gallery.",
      })
    } catch (error) {
      console.error("Error deleting image:", error)
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete image. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Photo Gallery</CardTitle>
        <CardDescription className="text-neutral-400">
          Showcase your work with before/after photos to attract more customers.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Upload Section */}
        <div className="mb-6">
          <div className="border-2 border-dashed border-neutral-700 rounded-lg p-6 text-center">
            <ImageIcon className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
            <h3 className="text-white font-medium mb-2">Upload Photos</h3>
            <p className="text-neutral-400 text-sm mb-4">
              Add photos of your pressure washing work to showcase your quality
            </p>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                disabled={uploading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button disabled={uploading} className="bg-blue-gradient-hover">
                <Upload className="h-4 w-4 mr-2" />
                {uploading ? "Uploading..." : "Choose Photo"}
              </Button>
            </div>
          </div>
        </div>

        {/* Gallery Grid */}
        {loading ? (
          <div className="text-center py-8">
            <p className="text-neutral-400">Loading gallery...</p>
          </div>
        ) : images.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-neutral-400">No photos uploaded yet</p>
            <p className="text-neutral-500 text-sm">Upload your first photo to get started</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <div className="aspect-square bg-neutral-800 rounded-lg overflow-hidden">
                  {/* Use regular img tag for mock images to avoid Next.js config issues */}
                  {image.image_url?.includes('via.placeholder.com') ? (
                    <img
                      src={image.image_url}
                      alt={image.caption || "Gallery image"}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Image
                      src={image.image_url || "/placeholder.svg"}
                      alt={image.caption || "Gallery image"}
                      width={300}
                      height={300}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>

                {/* Overlay with actions */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteImage(image.id)}
                    className="text-red-400 hover:text-red-300 hover:bg-neutral-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
