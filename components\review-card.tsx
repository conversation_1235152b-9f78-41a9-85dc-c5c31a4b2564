"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { useUser } from "@/hooks/use-user"
import { 
  Star, 
  MoreVertical, 
  Edit3, 
  Trash2, 
  Flag, 
  Calendar 
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"
import type { Review } from "@/lib/types"
import { ReviewForm } from "./review-form"
import { ReportReviewDialog } from "./report-review-dialog"

interface ReviewCardProps {
  review: Review & { profile?: { full_name: string } }
  businessSlug?: string
  businessName?: string
  onReviewUpdated?: (review: Review) => void
  onReviewDeleted?: (reviewId: string) => void
  showBusinessInfo?: boolean
}

export function ReviewCard({ 
  review, 
  businessSlug, 
  businessName,
  onReviewUpdated, 
  onReviewDeleted,
  showBusinessInfo = false
}: ReviewCardProps) {
  const { user } = useUser()
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showReportDialog, setShowReportDialog] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const isOwnReview = user?.id === review.author_id
  const isGoogleReview = review.review_source === 'google'
  const canEdit = isOwnReview && businessSlug && businessName && !isGoogleReview

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={cn(
          "h-4 w-4",
          index < rating 
            ? "text-yellow-400 fill-current" 
            : "text-neutral-600"
        )}
      />
    ))
  }

  const handleDelete = async () => {
    if (!isOwnReview) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/reviews/${review.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to delete review')
      }

      toast({
        title: "Review Deleted",
        description: "Your review has been deleted successfully."
      })

      if (onReviewDeleted) {
        onReviewDeleted(review.id)
      }
    } catch (error) {
      console.error('Error deleting review:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete review",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const handleReviewUpdated = (updatedReview: Review) => {
    setIsEditing(false)
    if (onReviewUpdated) {
      onReviewUpdated(updatedReview)
    }
  }

  if (isEditing && canEdit) {
    return (
      <ReviewForm
        businessSlug={businessSlug!}
        businessName={businessName!}
        existingReview={review}
        onReviewSubmitted={handleReviewUpdated}
        onCancel={() => setIsEditing(false)}
      />
    )
  }

  return (
    <>
      <Card className="bg-neutral-900 border-neutral-800">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {/* Header with rating and author */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1">
                    {renderStars(review.rating)}
                  </div>
                  <Badge variant="secondary" className="bg-neutral-800 text-neutral-300">
                    {review.rating} star{review.rating !== 1 ? 's' : ''}
                  </Badge>
                </div>
                
                {!isGoogleReview && (isOwnReview || user) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-neutral-900 border-neutral-800">
                      {isOwnReview && canEdit && (
                        <>
                          <DropdownMenuItem 
                            onClick={() => setIsEditing(true)}
                            className="text-neutral-300 hover:text-white hover:bg-neutral-800"
                          >
                            <Edit3 className="h-4 w-4 mr-2" />
                            Edit Review
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => setShowDeleteConfirm(true)}
                            className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Review
                          </DropdownMenuItem>
                        </>
                      )}
                      {!isOwnReview && user && (
                        <DropdownMenuItem 
                          onClick={() => setShowReportDialog(true)}
                          className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10"
                        >
                          <Flag className="h-4 w-4 mr-2" />
                          Report Review
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>

              {/* Review content */}
              {review.content && (
                <p className="text-neutral-300 mb-4 leading-relaxed">
                  {review.content}
                </p>
              )}

              {/* Footer with author and date */}
              <div className="flex items-center justify-between text-sm text-neutral-500">
                <div className="flex items-center space-x-4">
                  <span>
                    By {review.author_name || review.profile?.full_name || 'Anonymous'}
                    {review.review_source === 'google' && (
                      <Badge variant="outline" className="ml-2 text-xs bg-blue-500/10 text-blue-400 border-blue-500/20">
                        Google Review
                      </Badge>
                    )}
                    {isOwnReview && <Badge variant="outline" className="ml-2 text-xs">Your Review</Badge>}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {formatDistanceToNow(new Date(review.created_at), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="bg-neutral-900 border-neutral-800">
          <DialogHeader>
            <DialogTitle className="text-white">Delete Review</DialogTitle>
            <DialogDescription className="text-neutral-400">
              Are you sure you want to delete this review? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isDeleting ? 'Deleting...' : 'Delete Review'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Report Review Dialog */}
      {showReportDialog && (
        <ReportReviewDialog
          reviewId={review.id}
          isOpen={showReportDialog}
          onClose={() => setShowReportDialog(false)}
        />
      )}
    </>
  )
}
