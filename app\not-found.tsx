import { But<PERSON> } from '@/components/ui/button'
import { Search, Home } from 'lucide-react'
import Link from 'next/link'
import { Header } from '@/components/header'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-black">
      <Header />
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-2xl mx-auto">
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-white mb-4">Page Not Found</h2>
          <p className="text-neutral-400 text-lg mb-8">
            Sorry, we couldn&apos;t find the page you&apos;re looking for.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-blue-600 hover:bg-blue-700">
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>
            </Button>
            <Button asChild variant="outline" className="border-neutral-700 text-neutral-300 hover:bg-neutral-800">
              <Link href="/search">
                <Search className="h-4 w-4 mr-2" />
                Search Services
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
