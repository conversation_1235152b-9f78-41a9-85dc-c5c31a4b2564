import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, getBusinessMembers, addBusinessMember, updateBusinessMember, removeBusinessMember } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const addMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.enum(['admin', 'member'], { required_error: 'Role must be admin or member' })
})

const updateMemberSchema = z.object({
  role: z.enum(['admin', 'member'], { required_error: 'Role must be admin or member' })
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    
    // Get the business to verify ownership/membership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user has access (owner or member)
    const { members, error: membersError } = await getBusinessMembers(business.id)
    
    if (membersError) {
      return NextResponse.json({ error: membersError.message }, { status: 500 })
    }
    
    const userMember = members.find(m => m.user_id === user.id)
    if (!userMember) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    return NextResponse.json({ members })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const body = await request.json()
    
    // Validate input
    const validation = addMemberSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    const { email, role } = validation.data
    
    // Get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner or admin
    const { members, error: membersError } = await getBusinessMembers(business.id)
    
    if (membersError) {
      return NextResponse.json({ error: membersError.message }, { status: 500 })
    }
    
    const userMember = members.find(m => m.user_id === user.id)
    if (!userMember || (userMember.role !== 'owner' && userMember.role !== 'admin')) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 })
    }
    
    const { member, error } = await addBusinessMember(business.id, email, role)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ member })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}