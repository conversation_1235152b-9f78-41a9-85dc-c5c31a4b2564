# Project Structure

## Root Level Organization
```
├── app/                    # Next.js App Router pages and layouts
├── components/             # Reusable React components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility functions and configurations
├── public/                 # Static assets
├── scripts/                # Database and utility scripts
└── styles/                 # Global CSS files
```

## App Directory (Next.js App Router)
- **Route-based file structure** following Next.js 13+ conventions
- **Nested layouts** for consistent UI across route groups
- **API routes** in `app/api/` for backend functionality
- **Page components** as default exports in `page.tsx` files
- **Loading states** in `loading.tsx` files

### Key App Routes
```
app/
├── api/                    # Backend API endpoints
│   ├── businesses/         # Business CRUD operations
│   ├── messages/           # Messaging system
│   └── services/           # Service management
├── auth/                   # Authentication pages
├── business/[slug]/        # Dynamic business profile pages
├── dashboard/              # Protected user dashboard
├── search/                 # Search and filtering
└── page.tsx               # Homepage
```

## Components Organization
- **Feature-based grouping** (auth/, business/, dashboard/)
- **Shared UI components** in `components/ui/`
- **Page-specific components** grouped by feature
- **Compound components** for complex UI patterns

### Component Naming Conventions
- **kebab-case** for file names (`business-card.tsx`)
- **PascalCase** for component names (`BusinessCard`)
- **Descriptive, feature-specific names** (`business-onboarding.tsx`)

## Library Structure
```
lib/
├── auth.ts                 # Authentication utilities
├── database.ts             # Database operations and queries
├── supabase.ts            # Supabase client configuration
├── types.ts               # TypeScript type definitions
└── utils.ts               # General utility functions
```

## Database Schema Patterns
- **UUID primary keys** for all entities
- **Multi-tenant design** with Row-Level Security
- **Denormalized fields** for performance (avg_rating, review_count)
- **Comprehensive relationships** between all entities
- **Audit fields** (created_at, updated_at) on all tables

## File Naming Conventions
- **kebab-case** for all files and directories
- **Descriptive names** that indicate purpose
- **Feature prefixes** for related components (`business-`, `auth-`)
- **Type suffixes** for clarity (`.tsx` for components, `.ts` for utilities)

## Import/Export Patterns
- **Named exports** preferred over default exports (except pages)
- **Barrel exports** from `lib/` modules
- **Relative imports** for local files
- **Absolute imports** using `@/` alias for project root

## Code Organization Principles
- **Separation of concerns** between UI, logic, and data
- **Single responsibility** for components and functions
- **Consistent error handling** patterns
- **Type-first development** with comprehensive TypeScript coverage