# Google Maps Integration Setup Guide

This guide will help you set up Google Maps integration for your pressure washing directory application.

## Overview

The application now supports both:
- **Google Maps** (when API key is provided) - Interactive maps with accurate geocoding
- **Placeholder Map** (fallback) - Static grid pattern with mock positioning

## Features

✅ **Interactive Google Maps** with custom styled pins that match your UI  
✅ **Custom markers** with blue gradient design and glow effects  
✅ **Hover popups** showing business info, ratings, and distance  
✅ **Dark theme** map styling to match your application  
✅ **Accurate geocoding** using Google Maps Geocoding API  
✅ **Secure API key handling** (server-side geocoding endpoints)  
✅ **Graceful fallbacks** when API is unavailable  

## Step 1: Get Google Maps API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - **Maps JavaScript API** (for displaying maps)
   - **Geocoding API** (for address to coordinates conversion)
   - **Places API** (optional, for enhanced place data)

4. Create credentials:
   - Go to "Credentials" in the left sidebar
   - Click "Create Credentials" → "API Key"
   - Copy the generated API key

5. Secure your API key:
   - Click on the API key to edit it
   - Under "Application restrictions", select "HTTP referrers"
   - Add your domains:
     - `localhost:3000/*` (for development)
     - `yourdomain.com/*` (for production)
   - Under "API restrictions", select "Restrict key"
   - Choose the APIs you enabled above

## Step 2: Install Dependencies

Run one of these commands in your project directory:

```bash
# Using npm
npm install @googlemaps/react-wrapper

# Using yarn
yarn add @googlemaps/react-wrapper

# Using pnpm
pnpm add @googlemaps/react-wrapper
```

## Step 3: Add Environment Variable

Add your Google Maps API key to your `.env.local` file:

```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

**Important:** Replace `your_actual_api_key_here` with your real API key from Step 1.

## Step 4: Restart Development Server

After adding the environment variable, restart your development server:

```bash
npm run dev
```

## Step 5: Test the Integration

1. Navigate to the search results page in your application
2. You should see an interactive Google Map instead of the placeholder
3. Business pins should appear with your custom blue gradient styling
4. Hover over pins to see business information popups
5. The map should be draggable and zoomable

## File Structure

The integration includes these new files:

```
components/
├── google-map-view.tsx          # Google Maps React component
├── map-view.tsx                 # Updated to use Google Maps with fallback

lib/
├── google-geocoding.ts          # Google Maps geocoding service
├── client-geocoding.ts          # Client-side geocoding utilities

app/api/
└── geocoding/
    └── route.ts                 # Secure server-side geocoding API
```

## How It Works

### Map Display
- **With API key**: Shows interactive Google Maps with custom markers
- **Without API key**: Shows placeholder map with grid pattern
- **API failure**: Gracefully falls back to placeholder

### Geocoding
- **Primary**: Google Maps Geocoding API (more accurate)
- **Fallback**: OpenStreetMap Nominatim (free but less accurate)
- **Security**: API key stays on server, client uses secure endpoints

### Custom Markers
- Uses Google Maps Advanced Markers API with proper Map ID
- Maintains your exact pin design (blue gradient, glow effects)
- Click-to-open info windows with business information
- Dark theme styling that matches your UI
- Responsive and accessible

## Customization

### Map Styling
Edit `components/google-map-view.tsx` to customize:
- Map theme colors (currently dark theme)
- Zoom levels and controls
- Map center and bounds

### Pin Design
The custom markers in `CustomMarker` component can be modified to:
- Change colors and styling
- Add animations
- Modify popup content
- Add click interactions

### Geocoding Behavior
Adjust geocoding settings in `lib/client-geocoding.ts`:
- Batch processing limits
- Rate limiting delays
- Error handling

## Troubleshooting

### Map Not Loading
1. Check that your API key is correctly set in `.env.local`
2. Verify the API key has the correct APIs enabled
3. Check browser console for error messages
4. Ensure your domain is whitelisted in Google Cloud Console

### Geocoding Issues
1. Check that Geocoding API is enabled in Google Cloud Console
2. Verify you haven't exceeded API quotas
3. Test with the `/api/geocoding` endpoint directly

### Styling Issues
1. Ensure Tailwind CSS classes are properly configured
2. Check that custom CSS classes (`bg-blue-gradient`, `glow-blue`) are defined
3. Verify the map container has proper dimensions

## API Usage and Costs

### Free Tier
Google Maps provides a generous free tier:
- **Maps JavaScript API**: $200 credit per month (≈28,000 map loads)
- **Geocoding API**: $200 credit per month (≈40,000 requests)

### Cost Optimization
- Geocoding results are cached to minimize API calls
- Batch processing reduces request frequency
- Fallback to free OpenStreetMap when appropriate

## Security Best Practices

✅ **API key restrictions** set up in Google Cloud Console  
✅ **Server-side geocoding** keeps API key secure  
✅ **Domain restrictions** prevent unauthorized usage  
✅ **Rate limiting** prevents abuse  
✅ **Error handling** prevents information leakage  

## Next Steps

1. **Test thoroughly** with various addresses and locations
2. **Monitor API usage** in Google Cloud Console
3. **Set up billing alerts** to avoid unexpected charges
4. **Consider caching** geocoding results in your database
5. **Add more map features** like clustering, directions, etc.

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Google Cloud Console setup
3. Test the API endpoints directly
4. Review the fallback behavior

The application is designed to work gracefully with or without Google Maps, so your users will always have a functional map experience.
