import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, updateBusinessServices, getServices } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const updateServicesSchema = z.object({
  serviceIds: z.array(z.number().int().positive('Service ID must be a positive integer'))
    .min(1, 'At least one service must be selected')
    .max(20, 'Cannot select more than 20 services')
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user has access (owner or member)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    // Return the business services
    return NextResponse.json({ services: business.services || [] })
  } catch (error) {
    console.error('Error fetching business services:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const body = await request.json()
    
    // Validate input
    const validation = updateServicesSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will also be enforced by RLS)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const { serviceIds } = validation.data
    
    // Validate that all service IDs exist
    const { services: allServices, error: servicesError } = await getServices()
    if (servicesError) {
      return NextResponse.json({ error: 'Failed to validate services' }, { status: 500 })
    }
    
    const validServiceIds = allServices.map(s => s.id)
    const invalidIds = serviceIds.filter(id => !validServiceIds.includes(id))
    
    if (invalidIds.length > 0) {
      return NextResponse.json({ 
        error: `Invalid service IDs: ${invalidIds.join(', ')}` 
      }, { status: 400 })
    }
    
    const { error } = await updateBusinessServices(business.id, serviceIds)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating business services:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}