"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { getServices } from "@/lib/database"
import { Filter, Star, Crown } from "lucide-react"

interface FilterPanelProps {
  filters: {
    services: string[]
    rating: number
    premium: boolean
  }
  onFiltersChange: (filters: any) => void
}

export function FilterPanel({ filters, onFiltersChange }: FilterPanelProps) {
  const [services, setServices] = useState<{ id: number; name: string }[]>([])
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    const { services, error } = await getServices()
    if (!error && services) {
      setServices(services)
    }
  }

  const handleServiceToggle = (serviceName: string) => {
    const newServices = filters.services.includes(serviceName)
      ? filters.services.filter((s) => s !== serviceName)
      : [...filters.services, serviceName]

    onFiltersChange({ ...filters, services: newServices })
  }

  const handleRatingChange = (rating: number) => {
    onFiltersChange({ ...filters, rating: filters.rating === rating ? 0 : rating })
  }

  const handlePremiumToggle = () => {
    onFiltersChange({ ...filters, premium: !filters.premium })
  }

  const clearFilters = () => {
    onFiltersChange({ services: [], rating: 0, premium: false })
  }

  const activeFiltersCount = filters.services.length + (filters.rating > 0 ? 1 : 0) + (filters.premium ? 1 : 0)

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 relative bg-transparent"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {activeFiltersCount > 0 && (
            <Badge className="ml-2 bg-blue-500 text-white text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 bg-neutral-900 border-neutral-800" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-white">Filters</h3>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" size="sm" onClick={clearFilters} className="text-blue-400 hover:text-blue-300">
                Clear all
              </Button>
            )}
          </div>

          {/* Services Filter */}
          <div>
            <h4 className="text-sm font-medium text-white mb-2">Services</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {services.map((service) => (
                <div key={service.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`service-${service.id}`}
                    checked={filters.services.includes(service.name)}
                    onCheckedChange={() => handleServiceToggle(service.name)}
                    className="border-neutral-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <label htmlFor={`service-${service.id}`} className="text-sm text-neutral-300 cursor-pointer">
                    {service.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Rating Filter */}
          <div>
            <h4 className="text-sm font-medium text-white mb-2">Minimum Rating</h4>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  onClick={() => handleRatingChange(rating)}
                  className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                    filters.rating >= rating ? "bg-blue-500/20 text-blue-400" : "text-neutral-400 hover:text-white"
                  }`}
                >
                  <Star className="h-3 w-3" />
                  {rating}+
                </button>
              ))}
            </div>
          </div>

          {/* Premium Filter */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="premium"
              checked={filters.premium}
              onCheckedChange={handlePremiumToggle}
              className="border-neutral-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
            />
            <label htmlFor="premium" className="text-sm text-neutral-300 cursor-pointer flex items-center gap-1">
              <Crown className="h-3 w-3 text-yellow-400" />
              Premium listings only
            </label>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
