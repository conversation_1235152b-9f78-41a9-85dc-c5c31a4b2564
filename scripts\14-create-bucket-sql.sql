-- Create Storage Bucket via SQL
-- Use this if the dashboard method doesn't work

-- ===== CREATE BUCKET =====
INSERT INTO storage.buckets (
  id, 
  name, 
  public, 
  file_size_limit, 
  allowed_mime_types,
  created_at,
  updated_at
)
VALUES (
  'business-portfolios',
  'business-portfolios',
  true,
  52428800, -- 50MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types,
  updated_at = NOW();

-- ===== ENABLE RLS ON STORAGE =====
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

-- ===== CREATE BUCKET POLICIES =====
CREATE POLICY "Allow public bucket access"
ON storage.buckets FOR SELECT 
USING (public = true);

-- ===== CREATE OBJECT POLICIES =====

-- Allow authenticated users to upload
CREATE POLICY "Allow authenticated uploads to business-portfolios"
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- Allow public viewing
CREATE POLICY "Allow public access to business-portfolios"
ON storage.objects FOR SELECT 
USING (bucket_id = 'business-portfolios');

-- Allow authenticated updates
CREATE POLICY "Allow authenticated updates to business-portfolios"
ON storage.objects FOR UPDATE 
USING (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- Allow authenticated deletes
CREATE POLICY "Allow authenticated deletes from business-portfolios"
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- ===== VERIFICATION =====
DO $$
DECLARE
  bucket_count INTEGER;
  policy_count INTEGER;
BEGIN
  -- Check bucket
  SELECT COUNT(*) INTO bucket_count 
  FROM storage.buckets 
  WHERE id = 'business-portfolios';
  
  -- Check policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'storage' 
    AND tablename = 'objects'
    AND policyname LIKE '%business-portfolios%';
  
  RAISE NOTICE '=== STORAGE SETUP VERIFICATION ===';
  RAISE NOTICE 'Bucket exists: %', CASE WHEN bucket_count > 0 THEN 'YES' ELSE 'NO' END;
  RAISE NOTICE 'Policies created: % policies', policy_count;
  
  IF bucket_count > 0 AND policy_count >= 4 THEN
    RAISE NOTICE '✅ SUCCESS: Storage is ready for image uploads!';
  ELSE
    RAISE NOTICE '❌ ISSUE: Storage setup incomplete';
  END IF;
END $$;
