// Image processing utilities for bulk import
import { supabaseAdmin } from '@/lib/supabase'
import { addPortfolioImage } from '@/lib/database'

// Configuration constants
const MAX_IMAGES_PER_BUSINESS = 10
const MAX_IMAGE_SIZE = 10 * 1024 * 1024 // 10MB
const DOWNLOAD_TIMEOUT = 30000 // 30 seconds
const SUPPORTED_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']

export interface ImageDownloadResult {
  buffer: Buffer
  contentType: string
  filename: string
  size: number
}

export interface ImageProcessingResult {
  success: number
  failed: number
  errors: string[]
  uploadedImages: Array<{
    url: string
    filename: string
    size: number
  }>
}

/**
 * Download image from URL with timeout and validation
 */
export async function downloadImageFromUrl(url: string, index: number = 0): Promise<ImageDownloadResult> {
  try {
    // Create AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), DOWNLOAD_TIMEOUT)

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'PressureWash-Directory/1.0',
        'Accept': 'image/*',
      }
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type') || 'image/jpeg'
    
    // Validate content type
    if (!SUPPORTED_FORMATS.includes(contentType.toLowerCase())) {
      throw new Error(`Unsupported image format: ${contentType}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Validate file size
    if (buffer.length > MAX_IMAGE_SIZE) {
      throw new Error(`Image too large: ${buffer.length} bytes (max: ${MAX_IMAGE_SIZE})`)
    }

    if (buffer.length === 0) {
      throw new Error('Empty image file')
    }

    // Generate filename
    const extension = getExtensionFromContentType(contentType)
    const filename = `image-${index + 1}.${extension}`

    return {
      buffer,
      contentType,
      filename,
      size: buffer.length
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Download timeout')
    }
    throw error
  }
}

/**
 * Upload image buffer to Supabase storage
 */
export async function uploadImageToSupabase(
  imageData: ImageDownloadResult,
  businessId: string
): Promise<string> {
  if (!supabaseAdmin) {
    throw new Error('Supabase not configured')
  }

  // Generate unique filename with timestamp
  const timestamp = Date.now()
  const randomId = Math.random().toString(36).substring(2)
  const fileName = `${businessId}/${timestamp}-${randomId}-${imageData.filename}`

  // Upload to Supabase Storage
  const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
    .from('business-portfolios')
    .upload(fileName, imageData.buffer, {
      contentType: imageData.contentType,
      cacheControl: '3600',
      upsert: false
    })

  if (uploadError) {
    throw new Error(`Storage upload failed: ${uploadError.message}`)
  }

  // Get public URL
  const { data: { publicUrl } } = supabaseAdmin.storage
    .from('business-portfolios')
    .getPublicUrl(fileName)

  return publicUrl
}

/**
 * Process all images for a business
 */
export async function processBusinessImages(
  photoUrls: string[],
  businessId: string
): Promise<ImageProcessingResult> {
  const result: ImageProcessingResult = {
    success: 0,
    failed: 0,
    errors: [],
    uploadedImages: []
  }

  if (!photoUrls || photoUrls.length === 0) {
    return result
  }

  // Limit number of images
  const urlsToProcess = photoUrls.slice(0, MAX_IMAGES_PER_BUSINESS)

  console.log(`Processing ${urlsToProcess.length} images for business ${businessId}`)

  for (let i = 0; i < urlsToProcess.length; i++) {
    const url = urlsToProcess[i]
    
    try {
      console.log(`Downloading image ${i + 1}/${urlsToProcess.length} from: ${url.substring(0, 100)}...`)
      
      // Download image
      const imageData = await downloadImageFromUrl(url, i)
      
      // Upload to Supabase
      const publicUrl = await uploadImageToSupabase(imageData, businessId)
      
      // Add to database
      const { image, error } = await addPortfolioImage(businessId, {
        image_url: publicUrl,
        caption: `Business photo ${i + 1}`,
        display_order: i
      })

      if (error) {
        throw new Error(`Database insert failed: ${error.message}`)
      }

      result.success++
      result.uploadedImages.push({
        url: publicUrl,
        filename: imageData.filename,
        size: imageData.size
      })

      console.log(`✅ Successfully processed image ${i + 1}: ${imageData.filename} (${imageData.size} bytes)`)

      // Add small delay between downloads to be respectful
      if (i < urlsToProcess.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }

    } catch (error) {
      result.failed++
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      result.errors.push(`Image ${i + 1}: ${errorMessage}`)
      
      console.error(`❌ Failed to process image ${i + 1}:`, errorMessage)
    }
  }

  console.log(`Image processing complete for business ${businessId}: ${result.success} success, ${result.failed} failed`)
  
  return result
}

/**
 * Get file extension from content type
 */
function getExtensionFromContentType(contentType: string): string {
  const typeMap: { [key: string]: string } = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp',
    'image/gif': 'gif'
  }
  
  return typeMap[contentType.toLowerCase()] || 'jpg'
}

/**
 * Cleanup uploaded images if business creation fails
 */
export async function cleanupBusinessImages(businessId: string): Promise<void> {
  if (!supabaseAdmin) {
    return
  }

  try {
    // List all files for this business
    const { data: files, error: listError } = await supabaseAdmin.storage
      .from('business-portfolios')
      .list(businessId)

    if (listError || !files) {
      console.warn('Could not list files for cleanup:', listError?.message)
      return
    }

    if (files.length === 0) {
      return
    }

    // Delete all files
    const filePaths = files.map(file => `${businessId}/${file.name}`)
    const { error: deleteError } = await supabaseAdmin.storage
      .from('business-portfolios')
      .remove(filePaths)

    if (deleteError) {
      console.warn('Could not delete files during cleanup:', deleteError.message)
    } else {
      console.log(`Cleaned up ${files.length} images for business ${businessId}`)
    }
  } catch (error) {
    console.warn('Error during image cleanup:', error)
  }
}
