-- Storage Policies for business-portfolios bucket
-- Run this AFTER creating the bucket via Supabase Dashboard

-- ===== DROP EXISTING POLICIES (if any) =====
DROP POLICY IF EXISTS "Authenticated users can upload business portfolio images" ON storage.objects;
DROP POLICY IF EXISTS "Public can view business portfolio images" ON storage.objects;
DROP POLICY IF EXISTS "Business owners can update their portfolio images" ON storage.objects;
DROP POLICY IF EXISTS "Business owners can delete their portfolio images" ON storage.objects;

-- ===== CREATE STORAGE POLICIES =====

-- Policy 1: Allow authenticated users to upload images
CREATE POLICY "Allow authenticated uploads to business-portfolios"
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- Policy 2: Allow public access to view images
CREATE POLICY "Allow public access to business-portfolios"
ON storage.objects FOR SELECT 
USING (bucket_id = 'business-portfolios');

-- Policy 3: Allow authenticated users to update images
CREATE POLICY "Allow authenticated updates to business-portfolios"
ON storage.objects FOR UPDATE 
USING (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- Policy 4: Allow authenticated users to delete images
CREATE POLICY "Allow authenticated deletes from business-portfolios"
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'business-portfolios' AND
  auth.role() = 'authenticated'
);

-- ===== VERIFY BUCKET EXISTS =====
DO $$
DECLARE
  bucket_count INTEGER;
BEGIN
  -- Check if bucket exists
  SELECT COUNT(*) INTO bucket_count 
  FROM storage.buckets 
  WHERE id = 'business-portfolios';
  
  IF bucket_count > 0 THEN
    RAISE NOTICE '✅ SUCCESS: business-portfolios bucket found!';
    RAISE NOTICE 'Storage policies have been created.';
  ELSE
    RAISE NOTICE '❌ ERROR: business-portfolios bucket not found!';
    RAISE NOTICE 'Please create the bucket via Supabase Dashboard first.';
  END IF;
END $$;

-- ===== CHECK POLICIES =====
SELECT 
  policyname,
  cmd,
  permissive,
  roles
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND policyname LIKE '%business-portfolios%'
ORDER BY policyname;
