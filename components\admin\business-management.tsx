"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  MoreHorizontal,
  Eye,
  CheckCircle,
  XCircle,
  Crown,
  Star,
  MapPin,
  Phone,
  Mail,
  Calendar,
} from "lucide-react"

// Mock business data
const mockBusinesses = [
  {
    id: "1",
    name: "AZ Suds Power Washing",
    owner: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Scottsdale, AZ",
    status: "approved",
    subscription: "premium",
    rating: 4.8,
    reviewCount: 47,
    joinDate: "2024-05-20",
    lastActive: "2024-07-18",
  },
  {
    id: "2",
    name: "Aqua Clean Services",
    owner: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Mesa, AZ",
    status: "approved",
    subscription: "free",
    rating: 4.6,
    reviewCount: 32,
    joinDate: "2024-04-10",
    lastActive: "2024-07-19",
  },
  {
    id: "3",
    name: "Phoenix Pressure Pro",
    owner: "Mike Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Phoenix, AZ",
    status: "pending",
    subscription: "free",
    rating: 0,
    reviewCount: 0,
    joinDate: "2024-07-15",
    lastActive: "2024-07-16",
  },
  {
    id: "4",
    name: "Desert Clean Co",
    owner: "Tom Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Tempe, AZ",
    status: "suspended",
    subscription: "premium",
    rating: 3.2,
    reviewCount: 18,
    joinDate: "2024-03-15",
    lastActive: "2024-07-05",
  },
  {
    id: "5",
    name: "Elite Wash Solutions",
    owner: "Jennifer Davis",
    email: "<EMAIL>",
    phone: "(*************",
    location: "Chandler, AZ",
    status: "approved",
    subscription: "premium",
    rating: 4.9,
    reviewCount: 63,
    joinDate: "2024-02-28",
    lastActive: "2024-07-19",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "approved":
      return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Approved</Badge>
    case "pending":
      return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Pending</Badge>
    case "suspended":
      return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Suspended</Badge>
    case "rejected":
      return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Rejected</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{status}</Badge>
  }
}

const getSubscriptionBadge = (subscription: string) => {
  switch (subscription) {
    case "premium":
      return (
        <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">
          <Crown className="h-3 w-3 mr-1" />
          Premium
        </Badge>
      )
    case "free":
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Free</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{subscription}</Badge>
  }
}

export function BusinessManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterSubscription, setFilterSubscription] = useState("all")

  const filteredBusinesses = mockBusinesses.filter((business) => {
    const matchesSearch =
      business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      business.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
      business.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === "all" || business.status === filterStatus
    const matchesSubscription = filterSubscription === "all" || business.subscription === filterSubscription

    return matchesSearch && matchesStatus && matchesSubscription
  })

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Business Management</h1>
          <p className="text-neutral-400">Manage business listings and approvals</p>
        </div>
      </div>

          {/* Search and Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
              <Input
                placeholder="Search businesses by name, owner, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
              />
            </div>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-40 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-900 border-neutral-800">
                <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Status</SelectItem>
                <SelectItem value="approved" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Approved</SelectItem>
                <SelectItem value="pending" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Pending</SelectItem>
                <SelectItem value="suspended" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Suspended</SelectItem>
                <SelectItem value="rejected" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterSubscription} onValueChange={setFilterSubscription}>
              <SelectTrigger className="w-40 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
                <SelectValue placeholder="Subscription" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-900 border-neutral-800">
                <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Plans</SelectItem>
                <SelectItem value="free" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Free</SelectItem>
                <SelectItem value="premium" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Premium</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Businesses Table */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">All Businesses ({filteredBusinesses.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-neutral-800">
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Business</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Subscription</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Rating</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Location</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Join Date</th>
                      <th className="text-left py-3 px-4 font-medium text-neutral-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBusinesses.map((business) => (
                      <tr key={business.id} className="border-b border-gray-700 hover:bg-gray-750">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium text-white">{business.name}</p>
                            <p className="text-sm text-gray-400">Owner: {business.owner}</p>
                            <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                              <span className="flex items-center">
                                <Mail className="h-3 w-3 mr-1" />
                                {business.email}
                              </span>
                              <span className="flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {business.phone}
                              </span>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">{getStatusBadge(business.status)}</td>
                        <td className="py-3 px-4">{getSubscriptionBadge(business.subscription)}</td>
                        <td className="py-3 px-4">
                          {business.rating > 0 ? (
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-400 mr-1" />
                              <span className="text-white">{business.rating}</span>
                              <span className="text-gray-400 text-sm ml-1">({business.reviewCount})</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">No reviews</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center text-gray-300">
                            <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                            {business.location}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center text-gray-300">
                            <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                            {new Date(business.joinDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                              <DropdownMenuItem className="text-gray-300 hover:text-white hover:bg-gray-700">
                                <Eye className="h-4 w-4 mr-2" />
                                View Business
                              </DropdownMenuItem>
                              {business.status === "pending" && (
                                <>
                                  <DropdownMenuItem className="text-green-400 hover:text-green-300 hover:bg-gray-700">
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-red-400 hover:text-red-300 hover:bg-gray-700">
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                              {business.status === "approved" && (
                                <DropdownMenuItem className="text-yellow-400 hover:text-yellow-300 hover:bg-gray-700">
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Suspend
                                </DropdownMenuItem>
                              )}
                              {business.status === "suspended" && (
                                <DropdownMenuItem className="text-green-400 hover:text-green-300 hover:bg-gray-700">
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Reactivate
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
    </>
  )
}
