-- Database Status Check
-- Run this script to see what tables exist and their structure

-- ===== CHECK WHICH TABLES EXIST =====

SELECT 
  table_name,
  CASE 
    WHEN table_name IN ('businesses', 'profiles', 'locations', 'services', 'business_services', 'portfolio_images', 'reviews', 'message_threads', 'messages', 'business_members') THEN 'Core Table'
    WHEN table_name IN ('leads', 'lead_activities') THEN 'Leads System'
    WHEN table_name IN ('subscriptions') THEN 'Subscription System'
    ELSE 'Other'
  END as table_category
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_category, table_name;

-- ===== CHECK BUSINESSES TABLE ID TYPE =====

SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'businesses' 
  AND column_name = 'id';

-- ===== CHECK IF LEADS TABLE EXISTS AND ITS STRUCTURE =====

DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'leads'
  ) THEN
    RAISE NOTICE 'LEADS TABLE: EXISTS';
    
    -- Check the business_id column type
    PERFORM column_name
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
      AND table_name = 'leads' 
      AND column_name = 'business_id';
      
    IF FOUND THEN
      RAISE NOTICE 'LEADS TABLE: Has business_id column';
    ELSE
      RAISE NOTICE 'LEADS TABLE: Missing business_id column';
    END IF;
  ELSE
    RAISE NOTICE 'LEADS TABLE: DOES NOT EXIST';
  END IF;
END $$;

-- ===== CHECK IF SUBSCRIPTIONS TABLE EXISTS =====

DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'subscriptions'
  ) THEN
    RAISE NOTICE 'SUBSCRIPTIONS TABLE: EXISTS';
    
    -- Check the business_id column type
    PERFORM column_name
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
      AND table_name = 'subscriptions' 
      AND column_name = 'business_id';
      
    IF FOUND THEN
      RAISE NOTICE 'SUBSCRIPTIONS TABLE: Has business_id column';
    ELSE
      RAISE NOTICE 'SUBSCRIPTIONS TABLE: Missing business_id column';
    END IF;
  ELSE
    RAISE NOTICE 'SUBSCRIPTIONS TABLE: DOES NOT EXIST';
  END IF;
END $$;

-- ===== CHECK COLUMN TYPES FOR COMPATIBILITY =====

SELECT 
  t.table_name,
  c.column_name,
  c.data_type,
  c.udt_name
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
WHERE t.table_schema = 'public' 
  AND t.table_type = 'BASE TABLE'
  AND c.table_schema = 'public'
  AND c.column_name IN ('id', 'business_id', 'owner_id', 'user_id')
  AND t.table_name IN ('businesses', 'leads', 'subscriptions', 'profiles')
ORDER BY t.table_name, c.column_name;

-- ===== CHECK RLS POLICIES =====

SELECT 
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename IN ('businesses', 'leads', 'subscriptions')
ORDER BY tablename, policyname;

-- ===== SUMMARY MESSAGE =====

DO $$
BEGIN
  RAISE NOTICE '=== DATABASE STATUS CHECK COMPLETE ===';
  RAISE NOTICE 'Review the output above to understand your current database state';
  RAISE NOTICE '';
  RAISE NOTICE 'If leads or subscriptions tables are missing, run: 07-create-missing-tables.sql';
  RAISE NOTICE 'If tables exist but have wrong ID types, you may need a more complex migration';
END $$;
