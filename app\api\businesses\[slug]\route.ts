import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, updateBusiness, deleteBusiness } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const updateBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(100, 'Business name must be less than 100 characters').optional(),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)\.]+$/, 'Invalid phone number format').optional(),
  website_url: z.string().url('Invalid website URL').optional().or(z.literal(''))
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params
  
  const { business, error } = await getBusinessBySlug(slug)
  
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
  
  if (!business) {
    return NextResponse.json({ error: 'Business not found' }, { status: 404 })
  }
  
  return NextResponse.json({ business })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const body = await request.json()
    
    // Validate input
    const validation = updateBusinessSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    // First, get the business to verify ownership
    const { business: existingBusiness } = await getBusinessBySlug(slug)
    
    if (!existingBusiness) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner or admin (this will be enforced by RLS, but good to check)
    if (existingBusiness.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const updateData = validation.data
    // Convert empty string to undefined for website_url
    if (updateData.website_url === '') {
      updateData.website_url = undefined
    }
    
    const { business, error } = await updateBusiness(existingBusiness.id, updateData)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ business })
  } catch (error) {
    console.error('Error updating business:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    
    // First, get the business to verify ownership
    const { business: existingBusiness } = await getBusinessBySlug(slug)
    
    if (!existingBusiness) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will be enforced by RLS, but good to check)
    if (existingBusiness.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const { error } = await deleteBusiness(existingBusiness.id)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}