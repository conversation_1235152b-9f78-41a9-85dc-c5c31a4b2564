"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Users, 
  Phone, 
  Mail, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Plus,
  Eye,
  Edit,
  Trash2
} from "lucide-react"
import { supabase } from "@/lib/supabase"
import { useUser } from "@/hooks/use-user"
import type { Business } from "@/lib/types"

interface Lead {
  id: string
  business_id: string
  name: string
  email?: string
  phone?: string
  service_type?: string
  property_address?: string
  city?: string
  state?: string
  zip_code?: string
  status: 'new' | 'contacted' | 'quoted' | 'scheduled' | 'completed' | 'lost'
  source: 'website' | 'referral' | 'google' | 'facebook' | 'phone' | 'other'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  estimated_value?: number
  notes?: string
  last_contact_date?: string
  next_follow_up_date?: string
  created_at: string
  updated_at: string
}

interface LeadsOverviewProps {
  business: Business
}

export function LeadsOverview({ business }: LeadsOverviewProps) {
  const { user } = useUser()
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")

  useEffect(() => {
    if (business?.id) {
      fetchLeads()
    }
  }, [business?.id])

  const fetchLeads = async () => {
    if (!business?.id || !supabase) return

    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .eq('business_id', business.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching leads:', error)
      } else {
        setLeads(data || [])
      }
    } catch (error) {
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: Lead['status']) => {
    switch (status) {
      case 'new': return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
      case 'contacted': return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'quoted': return 'bg-purple-500/10 text-purple-400 border-purple-500/20'
      case 'scheduled': return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'completed': return 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20'
      case 'lost': return 'bg-red-500/10 text-red-400 border-red-500/20'
      default: return 'bg-neutral-500/10 text-neutral-400 border-neutral-500/20'
    }
  }

  const getPriorityColor = (priority: Lead['priority']) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500/10 text-red-400 border-red-500/20'
      case 'high': return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
      case 'medium': return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'low': return 'bg-green-500/10 text-green-400 border-green-500/20'
      default: return 'bg-neutral-500/10 text-neutral-400 border-neutral-500/20'
    }
  }

  const filterLeads = (status?: string) => {
    if (!status || status === 'all') return leads
    return leads.filter(lead => lead.status === status)
  }

  const getLeadStats = () => {
    const totalLeads = leads.length
    const newLeads = leads.filter(l => l.status === 'new').length
    const activeLeads = leads.filter(l => ['contacted', 'quoted', 'scheduled'].includes(l.status)).length
    const completedLeads = leads.filter(l => l.status === 'completed').length
    const totalValue = leads
      .filter(l => l.estimated_value)
      .reduce((sum, l) => sum + (l.estimated_value || 0), 0)

    return { totalLeads, newLeads, activeLeads, completedLeads, totalValue }
  }

  const stats = getLeadStats()
  const filteredLeads = filterLeads(activeTab)

  if (loading) {
    return (
      <Card className="bg-neutral-900 border-neutral-800">
        <CardContent className="p-6">
          <div className="text-center text-neutral-400">Loading leads...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-400" />
              <div>
                <p className="text-sm text-neutral-400">Total Leads</p>
                <p className="text-2xl font-bold text-white">{stats.totalLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Plus className="h-4 w-4 text-green-400" />
              <div>
                <p className="text-sm text-neutral-400">New Leads</p>
                <p className="text-2xl font-bold text-white">{stats.newLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-yellow-400" />
              <div>
                <p className="text-sm text-neutral-400">Active</p>
                <p className="text-2xl font-bold text-white">{stats.activeLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-emerald-400" />
              <div>
                <p className="text-sm text-neutral-400">Completed</p>
                <p className="text-2xl font-bold text-white">{stats.completedLeads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <div>
                <p className="text-sm text-neutral-400">Total Value</p>
                <p className="text-2xl font-bold text-white">${stats.totalValue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Leads List */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">Leads Management</CardTitle>
              <CardDescription>Track and manage your potential customers</CardDescription>
            </div>
            <Button className="bg-blue-gradient-hover">
              <Plus className="h-4 w-4 mr-2" />
              Add Lead
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="bg-neutral-800 border-blue-500/20">
              <TabsTrigger value="all" className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400">
                All ({leads.length})
              </TabsTrigger>
              <TabsTrigger value="new" className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400">
                New ({stats.newLeads})
              </TabsTrigger>
              <TabsTrigger value="contacted" className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400">
                Contacted
              </TabsTrigger>
              <TabsTrigger value="quoted" className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400">
                Quoted
              </TabsTrigger>
              <TabsTrigger value="scheduled" className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400">
                Scheduled
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-4">
              {filteredLeads.length === 0 ? (
                <div className="text-center py-8 text-neutral-400">
                  <Users className="h-12 w-12 mx-auto mb-4 text-neutral-600" />
                  <p>No leads found for this status.</p>
                  <Button className="mt-4 bg-blue-gradient-hover">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Lead
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredLeads.map((lead) => (
                    <Card key={lead.id} className="bg-neutral-800 border-neutral-700">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-medium text-white">{lead.name}</h3>
                              <Badge className={getStatusColor(lead.status)}>
                                {lead.status}
                              </Badge>
                              <Badge className={getPriorityColor(lead.priority)}>
                                {lead.priority}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                              {lead.email && (
                                <div className="flex items-center space-x-2 text-neutral-400">
                                  <Mail className="h-4 w-4" />
                                  <span>{lead.email}</span>
                                </div>
                              )}
                              {lead.phone && (
                                <div className="flex items-center space-x-2 text-neutral-400">
                                  <Phone className="h-4 w-4" />
                                  <span>{lead.phone}</span>
                                </div>
                              )}
                              {lead.service_type && (
                                <div className="text-neutral-400">
                                  <span className="font-medium">Service:</span> {lead.service_type}
                                </div>
                              )}
                              {lead.estimated_value && (
                                <div className="flex items-center space-x-2 text-green-400">
                                  <DollarSign className="h-4 w-4" />
                                  <span>${lead.estimated_value.toLocaleString()}</span>
                                </div>
                              )}
                            </div>

                            {lead.property_address && (
                              <p className="text-sm text-neutral-400 mt-2">
                                📍 {lead.property_address}, {lead.city}, {lead.state} {lead.zip_code}
                              </p>
                            )}

                            {lead.notes && (
                              <p className="text-sm text-neutral-300 mt-2 bg-neutral-900 p-2 rounded">
                                {lead.notes}
                              </p>
                            )}
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            <Button variant="ghost" size="sm" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white hover:bg-neutral-700">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300 hover:bg-red-500/10">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
