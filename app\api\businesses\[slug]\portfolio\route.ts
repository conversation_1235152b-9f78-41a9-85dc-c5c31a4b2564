import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, addPortfolioImage, deletePortfolioImage } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const addPortfolioImageSchema = z.object({
  image_url: z.string().url('Invalid image URL').min(1, 'Image URL is required'),
  caption: z.string().max(200, 'Caption must be less than 200 characters').optional(),
  display_order: z.number().int().min(0, 'Display order must be a non-negative integer').optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const body = await request.json()
    
    // Validate input
    const validation = addPortfolioImageSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will also be enforced by RLS)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const { image_url, caption, display_order } = validation.data
    
    const { image, error } = await addPortfolioImage(business.id, {
      image_url,
      caption,
      display_order: display_order || 0
    })
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ image }, { status: 201 })
  } catch (error) {
    console.error('Error adding portfolio image:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const { searchParams } = new URL(request.url)
    const imageId = searchParams.get('imageId')
    
    if (!imageId) {
      return NextResponse.json({ error: 'imageId is required' }, { status: 400 })
    }
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will also be enforced by RLS)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const { error } = await deletePortfolioImage(imageId)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}