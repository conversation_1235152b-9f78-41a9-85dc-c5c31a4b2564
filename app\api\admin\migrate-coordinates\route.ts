import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

/**
 * Migration endpoint to add coordinates to locations table
 * This should be run once to add latitude/longitude columns and populate existing data
 */

export async function POST(request: NextRequest) {
  if (!supabaseAdmin) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 500 }
    )
  }

  try {
    console.log('Starting coordinates migration...')

    // Since we can't add columns via API, let's work with what we have
    // For now, we'll store coordinates in a separate table or use the geocoding service
    console.log('Checking existing locations...')

    // Get all locations to see what we're working with
    const { data: allLocations, error: fetchError } = await supabaseAdmin
      .from('locations')
      .select('*')

    if (fetchError) {
      console.error('Error fetching locations:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch locations', details: fetchError },
        { status: 500 }
      )
    }

    console.log(`Found ${allLocations?.length || 0} locations`)

    // For now, let's just return the current state since we can't modify the schema
    // The coordinates will need to be added via database migration or Supabase dashboard
    const { error: updateError } = await supabaseAdmin
      .from('locations')
      .update({
        latitude: 33.4484,
        longitude: -112.0740
      })
      .eq('city', 'Phoenix')
      .eq('state', 'AZ')
      .is('latitude', null)

    if (updateError) {
      console.error('Error updating Phoenix coordinates:', updateError)
    } else {
      console.log('Updated Phoenix coordinates')
    }

    // Update other Arizona cities
    const arizonaCities = [
      { city: 'Scottsdale', lat: 33.4942, lng: -111.9261 },
      { city: 'Tempe', lat: 33.4255, lng: -111.9400 },
      { city: 'Mesa', lat: 33.4152, lng: -111.8315 },
      { city: 'Chandler', lat: 33.3062, lng: -111.8413 },
      { city: 'Glendale', lat: 33.5387, lng: -112.1860 },
      { city: 'Peoria', lat: 33.5806, lng: -112.2374 },
      { city: 'Gilbert', lat: 33.3528, lng: -111.7890 },
      { city: 'Surprise', lat: 33.6292, lng: -112.3679 },
      { city: 'Avondale', lat: 33.4356, lng: -112.3496 }
    ]

    for (const cityData of arizonaCities) {
      const { error } = await supabaseAdmin
        .from('locations')
        .update({
          latitude: cityData.lat,
          longitude: cityData.lng
        })
        .eq('city', cityData.city)
        .eq('state', 'AZ')
        .is('latitude', null)

      if (error) {
        console.error(`Error updating ${cityData.city} coordinates:`, error)
      } else {
        console.log(`Updated ${cityData.city} coordinates`)
      }
    }

    // Step 4: Get count of locations with coordinates
    const { data: coordCount, error: countError } = await supabaseAdmin
      .from('locations')
      .select('id', { count: 'exact' })
      .not('latitude', 'is', null)
      .not('longitude', 'is', null)

    if (countError) {
      console.error('Error counting coordinates:', countError)
    }

    // Step 5: Get total locations count
    const { data: totalCount, error: totalError } = await supabaseAdmin
      .from('locations')
      .select('id', { count: 'exact' })

    if (totalError) {
      console.error('Error counting total locations:', totalError)
    }

    return NextResponse.json({
      success: true,
      message: 'Coordinates migration completed',
      stats: {
        locationsWithCoordinates: coordCount?.length || 0,
        totalLocations: totalCount?.length || 0
      }
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json(
      { error: 'Migration failed', details: error },
      { status: 500 }
    )
  }
}

// GET endpoint to check migration status
export async function GET() {
  if (!supabaseAdmin) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 500 }
    )
  }

  try {
    // Check if columns exist
    const { data: columns, error: columnError } = await supabaseAdmin
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'locations')
      .eq('table_schema', 'public')
      .in('column_name', ['latitude', 'longitude'])

    if (columnError) {
      console.error('Error checking columns:', columnError)
    }

    const hasLatitude = columns?.some(col => col.column_name === 'latitude') || false
    const hasLongitude = columns?.some(col => col.column_name === 'longitude') || false

    // Get count of locations with coordinates
    const { data: coordCount, error: countError } = await supabaseAdmin
      .from('locations')
      .select('id', { count: 'exact' })
      .not('latitude', 'is', null)
      .not('longitude', 'is', null)

    // Get total locations count
    const { data: totalCount, error: totalError } = await supabaseAdmin
      .from('locations')
      .select('id', { count: 'exact' })

    return NextResponse.json({
      status: {
        hasLatitudeColumn: hasLatitude,
        hasLongitudeColumn: hasLongitude,
        locationsWithCoordinates: coordCount?.length || 0,
        totalLocations: totalCount?.length || 0,
        migrationNeeded: !hasLatitude || !hasLongitude || (coordCount?.length || 0) === 0
      }
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Status check failed', details: error },
      { status: 500 }
    )
  }
}
