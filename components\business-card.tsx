"use client"

import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import type { BusinessWithDetails } from "@/lib/types"
import { formatDistance } from "@/lib/geocoding"
import { Star, MapPin, Phone, ExternalLink, Crown, Navigation } from "lucide-react"

interface BusinessCardProps {
  business: BusinessWithDetails & { distance?: number }
  isHovered?: boolean
  onHover?: (businessId: string | null) => void
}

export function BusinessCard({ business, isHovered, onHover }: BusinessCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-current" : "text-neutral-600"}`}
      />
    ))
  }

  const primaryImage = business.gallery?.[0]?.image_url

  const handleMouseEnter = () => {
    onHover?.(business.id)
  }

  const handleMouseLeave = () => {
    onHover?.(null)
  }

  return (
    <div
      className={`
        bg-neutral-900 border rounded-xl p-5 transition-all duration-200 cursor-pointer
        ${isHovered
          ? 'border-blue-500/50 shadow-lg shadow-blue-500/20 glow-blue'
          : 'border-neutral-800 hover:border-blue-500/30 hover:shadow-lg hover:shadow-blue-500/10 card-hover-blue'
        }
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex gap-4">
        {/* Business Logo/Image */}
        <div className="flex-shrink-0">
          <div className="w-14 h-14 bg-neutral-800 rounded-xl overflow-hidden border-2 border-neutral-700">
            {primaryImage ? (
              <Image
                src={primaryImage || "/placeholder.svg"}
                alt={business.name}
                width={56}
                height={56}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                {/* Logo placeholder with business initials */}
                <div className="bg-blue-gradient text-white font-bold text-base rounded-lg w-10 h-10 flex items-center justify-center">
                  {business.name.split(' ').map(word => word[0]).join('').slice(0, 2).toUpperCase()}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Business Info */}
        <div className="flex-1 min-w-0">
          {/* Header with name and premium badge */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2 flex-1">
              <h3 className="text-xl font-semibold text-white truncate">{business.name}</h3>
              {business.is_premium && (
                <div className="flex items-center gap-1 bg-yellow-500/10 text-yellow-400 px-2 py-1 rounded-full text-xs font-medium">
                  <Crown className="h-3 w-3" />
                  Premium
                </div>
              )}
            </div>
          </div>

          {/* Rating and Reviews */}
          <div className="flex items-center gap-3 mb-3">
            <div className="flex items-center gap-2">
              <div className="flex">{renderStars(business.avg_rating || 0)}</div>
              <span className="text-sm font-medium text-white">
                {business.avg_rating ? business.avg_rating.toFixed(1) : "New"}
              </span>
            </div>
            {business.review_count > 0 && (
              <span className="text-sm text-neutral-400">
                ({business.review_count} review{business.review_count !== 1 ? 's' : ''})
              </span>
            )}
          </div>

          {/* Location and Distance */}
          <div className="flex items-center gap-4 mb-3">
            {(business.location?.city || business.location?.state) && (
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4 text-neutral-500" />
                <span className="text-sm text-neutral-400">
                  {business.location.city}
                  {business.location.city && business.location.state && ", "}
                  {business.location.state}
                </span>
              </div>
            )}
            {business.distance && (
              <div className="flex items-center gap-1">
                <Navigation className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-400 font-medium">{formatDistance(business.distance)} away</span>
              </div>
            )}
          </div>

          {/* Description */}
          {business.description && (
            <p className="text-sm text-neutral-300 mb-3 line-clamp-2 leading-relaxed">
              {business.description}
            </p>
          )}

          {/* Services */}
          {business.services && business.services.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {business.services.slice(0, 3).map((businessService, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="bg-blue-500/10 text-blue-400 border-blue-500/20 text-xs font-medium"
                >
                  {businessService.service?.name || 'Service'}
                </Badge>
              ))}
              {business.services.length > 3 && (
                <Badge variant="secondary" className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs">
                  +{business.services.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Contact Info */}
          <div className="flex items-center gap-4 mb-3 text-sm">
            {business.phone_number && (
              <div className="flex items-center gap-1">
                <Phone className="h-4 w-4 text-blue-400" />
                <span className="text-neutral-300">{business.phone_number}</span>
              </div>
            )}
            {business.website_url && (
              <a
                href={business.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Website</span>
              </a>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <Button asChild className="bg-blue-gradient-hover font-medium px-6">
              <Link href={`/business/${business.slug}`}>View Profile</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10 hover:border-blue-500/50 bg-transparent font-medium px-6"
            >
              <Link href={`/business/${business.slug}#quote`}>Get Quote</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
