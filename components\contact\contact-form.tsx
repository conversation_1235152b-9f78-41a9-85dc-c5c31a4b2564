"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Send, CheckCircle } from "lucide-react"

export function ContactForm() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    category: "",
    message: "",
  })

  const categories = [
    { value: "general", label: "General Question" },
    { value: "business", label: "Business Account Support" },
    { value: "customer", label: "Customer Support" },
    { value: "technical", label: "Technical Issue" },
    { value: "billing", label: "Billing & Payments" },
    { value: "feedback", label: "Feedback & Suggestions" },
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    // Simulate form submission
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setSubmitted(true)
      toast({
        title: "Message Sent!",
        description: "We've received your message and will respond within 24 hours.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }))
  }

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      category: value,
    }))
  }

  if (submitted) {
    return (
      <Card className="bg-neutral-900 border-neutral-800">
        <CardContent className="p-8 text-center">
          <div className="bg-green-500/10 border border-green-500/20 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-2">Message Sent Successfully!</h3>
          <p className="text-neutral-400 mb-6">
            Thank you for contacting us. We've received your message and will respond within 24 hours.
          </p>
          <div className="bg-neutral-800 rounded-lg p-4 mb-6">
            <p className="text-neutral-400 text-sm">
              <strong className="text-white">What's next?</strong>
              <br />
              Our support team will review your message and respond to <strong>{formData.email}</strong>. Please check
              your spam folder if you don't see our response.
            </p>
          </div>
          <Button
            onClick={() => {
              setSubmitted(false)
              setFormData({ name: "", email: "", subject: "", category: "", message: "" })
            }}
            variant="outline"
            className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
          >
            Send Another Message
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Send Us a Message</CardTitle>
        <p className="text-neutral-400">Fill out the form below and we'll get back to you as soon as possible.</p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-white mb-2">
                Your Name *
              </label>
              <Input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                placeholder="Jane Doe"
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                Your Email *
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-white mb-2">
              Category
            </label>
            <Select onValueChange={handleSelectChange}>
              <SelectTrigger className="bg-neutral-800 border-neutral-700 text-white">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-900 border-neutral-800">
                {categories.map((category) => (
                  <SelectItem
                    key={category.value}
                    value={category.value}
                    className="text-neutral-300 hover:text-white hover:bg-blue-500/10"
                  >
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-white mb-2">
              Subject
            </label>
            <Input
              id="subject"
              name="subject"
              type="text"
              value={formData.subject}
              onChange={handleChange}
              className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
              placeholder="Brief description of your inquiry"
            />
          </div>

          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-white mb-2">
              Message *
            </label>
            <Textarea
              id="message"
              name="message"
              required
              value={formData.message}
              onChange={handleChange}
              className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
              placeholder="Please provide as much detail as possible about your question or issue..."
              rows={6}
            />
            <p className="text-neutral-500 text-xs mt-1">
              Include your account email if you're asking about a specific business listing
            </p>
          </div>

          {/* Submit Button */}
          <Button type="submit" disabled={loading} className="w-full bg-blue-gradient-hover">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Sending Message...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Message
              </>
            )}
          </Button>

          {/* Privacy Notice */}
          <div className="bg-neutral-800 rounded-lg p-4">
            <p className="text-neutral-400 text-xs">
              🔒 <strong className="text-white">Privacy Notice:</strong> Your information is secure and will only be
              used to respond to your inquiry. We never share your contact details with third parties.
            </p>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
