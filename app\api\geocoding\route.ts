import { NextRequest, NextResponse } from 'next/server'

/**
 * Server-side geocoding API endpoint
 * Keeps the Google Maps API key secure on the server
 */

interface GeocodingResponse {
  success: boolean
  data?: {
    coordinates: {
      lat: number
      lng: number
    }
    formattedAddress: string
    addressComponents: {
      streetNumber?: string
      route?: string
      city?: string
      state?: string
      zipCode?: string
      country?: string
    }
    placeId?: string
  }
  error?: string
}

export async function POST(request: NextRequest) {
  try {
    const { address } = await request.json()

    if (!address || typeof address !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Address is required' },
        { status: 400 }
      )
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // Call Google Maps Geocoding API
    const encodedAddress = encodeURIComponent(address.trim())
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${apiKey}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Google Maps API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0]
      const location = result.geometry.location

      // Parse address components
      const addressComponents: any = {}

      result.address_components?.forEach((component: any) => {
        const types = component.types

        if (types.includes('street_number')) {
          addressComponents.streetNumber = component.long_name
        } else if (types.includes('route')) {
          addressComponents.route = component.long_name
        } else if (types.includes('locality')) {
          addressComponents.city = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          addressComponents.state = component.short_name
        } else if (types.includes('postal_code')) {
          addressComponents.zipCode = component.long_name
        } else if (types.includes('country')) {
          addressComponents.country = component.short_name
        }
      })

      const geocodingResponse: GeocodingResponse = {
        success: true,
        data: {
          coordinates: {
            lat: location.lat,
            lng: location.lng
          },
          formattedAddress: result.formatted_address,
          addressComponents,
          placeId: result.place_id
        }
      }

      return NextResponse.json(geocodingResponse)
    } else {
      // Handle different Google Maps API error statuses
      let errorMessage = 'Geocoding failed'
      
      switch (data.status) {
        case 'ZERO_RESULTS':
          errorMessage = 'No results found for the provided address'
          break
        case 'OVER_QUERY_LIMIT':
          errorMessage = 'Geocoding quota exceeded'
          break
        case 'REQUEST_DENIED':
          errorMessage = 'Geocoding request denied'
          break
        case 'INVALID_REQUEST':
          errorMessage = 'Invalid geocoding request'
          break
        default:
          errorMessage = `Geocoding failed: ${data.status}`
      }

      return NextResponse.json(
        { success: false, error: errorMessage },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Geocoding API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint for reverse geocoding
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const lat = searchParams.get('lat')
    const lng = searchParams.get('lng')

    if (!lat || !lng) {
      return NextResponse.json(
        { success: false, error: 'Latitude and longitude are required' },
        { status: 400 }
      )
    }

    const latitude = parseFloat(lat)
    const longitude = parseFloat(lng)

    if (isNaN(latitude) || isNaN(longitude)) {
      return NextResponse.json(
        { success: false, error: 'Invalid coordinates' },
        { status: 400 }
      )
    }

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // Call Google Maps Reverse Geocoding API
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`Google Maps API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0]

      // Parse address components
      const addressComponents: any = {}

      result.address_components?.forEach((component: any) => {
        const types = component.types

        if (types.includes('street_number')) {
          addressComponents.streetNumber = component.long_name
        } else if (types.includes('route')) {
          addressComponents.route = component.long_name
        } else if (types.includes('locality')) {
          addressComponents.city = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          addressComponents.state = component.short_name
        } else if (types.includes('postal_code')) {
          addressComponents.zipCode = component.long_name
        } else if (types.includes('country')) {
          addressComponents.country = component.short_name
        }
      })

      const geocodingResponse: GeocodingResponse = {
        success: true,
        data: {
          coordinates: {
            lat: latitude,
            lng: longitude
          },
          formattedAddress: result.formatted_address,
          addressComponents,
          placeId: result.place_id
        }
      }

      return NextResponse.json(geocodingResponse)
    } else {
      return NextResponse.json(
        { success: false, error: 'No results found for the provided coordinates' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Reverse geocoding API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
