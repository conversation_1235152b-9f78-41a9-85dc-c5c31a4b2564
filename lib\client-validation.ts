"use client"

import { z } from 'zod'
import { useState, useCallback, useEffect } from 'react'

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string[]>
  hasErrors: boolean
}

// Field validation state
export interface FieldValidation {
  value: any
  error: string | null
  touched: boolean
  isValidating: boolean
}

// Form validation hook
export function useFormValidation<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  initialValues: Partial<T> = {},
  options: {
    validateOnChange?: boolean
    validateOnBlur?: boolean
    debounceMs?: number
  } = {}
) {
  const {
    validateOnChange = true,
    validateOnBlur = true,
    debounceMs = 300
  } = options

  const [values, setValues] = useState<Partial<T>>(initialValues)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isValidating, setIsValidating] = useState(false)
  const [isValid, setIsValid] = useState(false)

  // Debounced validation
  const [validationTimeout, setValidationTimeout] = useState<NodeJS.Timeout | null>(null)

  const validateField = useCallback(async (fieldName: string, value: any) => {
    try {
      // Create a partial schema for the specific field
      const fieldSchema = schema.pick({ [fieldName]: true } as any)
      await fieldSchema.parseAsync({ [fieldName]: value })
      
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[fieldName]
        return newErrors
      })
      
      return null
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors.find(err => err.path.includes(fieldName))
        const errorMessage = fieldError?.message || 'Invalid value'
        
        setErrors(prev => ({
          ...prev,
          [fieldName]: errorMessage
        }))
        
        return errorMessage
      }
      return 'Validation error'
    }
  }, [schema])

  const validateForm = useCallback(async (formValues: Partial<T> = values) => {
    setIsValidating(true)
    
    try {
      await schema.parseAsync(formValues)
      setErrors({})
      setIsValid(true)
      return { isValid: true, errors: {} }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Record<string, string> = {}
        
        error.errors.forEach(err => {
          const fieldName = err.path.join('.')
          formErrors[fieldName] = err.message
        })
        
        setErrors(formErrors)
        setIsValid(false)
        return { isValid: false, errors: formErrors }
      }
      
      setIsValid(false)
      return { isValid: false, errors: { form: 'Validation failed' } }
    } finally {
      setIsValidating(false)
    }
  }, [schema, values])

  const setValue = useCallback((fieldName: string, value: any) => {
    setValues(prev => ({ ...prev, [fieldName]: value }))
    
    if (validateOnChange) {
      // Clear existing timeout
      if (validationTimeout) {
        clearTimeout(validationTimeout)
      }
      
      // Set new timeout for debounced validation
      const timeout = setTimeout(() => {
        validateField(fieldName, value)
      }, debounceMs)
      
      setValidationTimeout(timeout)
    }
  }, [validateOnChange, validateField, debounceMs, validationTimeout])

  const setFieldTouched = useCallback((fieldName: string, isTouched: boolean = true) => {
    setTouched(prev => ({ ...prev, [fieldName]: isTouched }))
    
    if (validateOnBlur && isTouched) {
      validateField(fieldName, values[fieldName])
    }
  }, [validateOnBlur, validateField, values])

  const resetForm = useCallback((newValues: Partial<T> = {}) => {
    setValues(newValues)
    setErrors({})
    setTouched({})
    setIsValid(false)
  }, [])

  const getFieldProps = useCallback((fieldName: string) => ({
    value: values[fieldName] || '',
    error: touched[fieldName] ? errors[fieldName] : undefined,
    onChange: (value: any) => setValue(fieldName, value),
    onBlur: () => setFieldTouched(fieldName, true),
    touched: touched[fieldName] || false
  }), [values, errors, touched, setValue, setFieldTouched])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (validationTimeout) {
        clearTimeout(validationTimeout)
      }
    }
  }, [validationTimeout])

  return {
    values,
    errors,
    touched,
    isValidating,
    isValid,
    setValue,
    setFieldTouched,
    validateField,
    validateForm,
    resetForm,
    getFieldProps,
    hasErrors: Object.keys(errors).length > 0
  }
}

// Real-time validation hook for individual fields
export function useFieldValidation<T>(
  schema: z.ZodSchema<T>,
  initialValue: T,
  options: {
    debounceMs?: number
    validateOnMount?: boolean
  } = {}
) {
  const { debounceMs = 300, validateOnMount = false } = options
  
  const [value, setValue] = useState<T>(initialValue)
  const [error, setError] = useState<string | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const [touched, setTouched] = useState(false)

  const validate = useCallback(async (valueToValidate: T) => {
    setIsValidating(true)
    
    try {
      await schema.parseAsync(valueToValidate)
      setError(null)
      return null
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        const errorMessage = validationError.errors[0]?.message || 'Invalid value'
        setError(errorMessage)
        return errorMessage
      }
      setError('Validation error')
      return 'Validation error'
    } finally {
      setIsValidating(false)
    }
  }, [schema])

  // Debounced validation effect
  useEffect(() => {
    if (!touched && !validateOnMount) return

    const timeout = setTimeout(() => {
      validate(value)
    }, debounceMs)

    return () => clearTimeout(timeout)
  }, [value, validate, debounceMs, touched, validateOnMount])

  const updateValue = useCallback((newValue: T) => {
    setValue(newValue)
    if (!touched) setTouched(true)
  }, [touched])

  return {
    value,
    error,
    isValidating,
    touched,
    setValue: updateValue,
    setTouched,
    validate: () => validate(value),
    isValid: error === null && touched
  }
}

// Async validation for server-side checks
export function useAsyncValidation<T>(
  validator: (value: T) => Promise<string | null>,
  debounceMs: number = 500
) {
  const [isValidating, setIsValidating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const validate = useCallback(async (value: T) => {
    setIsValidating(true)
    
    try {
      const result = await validator(value)
      setError(result)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Validation failed'
      setError(errorMessage)
      return errorMessage
    } finally {
      setIsValidating(false)
    }
  }, [validator])

  const debouncedValidate = useCallback(
    debounce(validate, debounceMs),
    [validate, debounceMs]
  )

  return {
    validate: debouncedValidate,
    isValidating,
    error,
    isValid: error === null && !isValidating
  }
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Validation utilities
export const validationUtils = {
  // Check if email is available (example async validation)
  checkEmailAvailability: async (email: string): Promise<string | null> => {
    if (!email) return null
    
    try {
      const response = await fetch('/api/auth/check-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        return data.error || 'Email validation failed'
      }
      
      return data.available ? null : 'Email is already taken'
    } catch {
      return 'Unable to verify email availability'
    }
  },

  // Check if business slug is available
  checkSlugAvailability: async (slug: string): Promise<string | null> => {
    if (!slug) return null
    
    try {
      const response = await fetch('/api/businesses/check-slug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ slug })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        return data.error || 'Slug validation failed'
      }
      
      return data.available ? null : 'This URL is already taken'
    } catch {
      return 'Unable to verify URL availability'
    }
  },

  // Validate file upload
  validateFile: (
    file: File,
    options: {
      maxSize?: number
      allowedTypes?: string[]
      maxWidth?: number
      maxHeight?: number
    } = {}
  ): Promise<string | null> => {
    const {
      maxSize = 5 * 1024 * 1024, // 5MB
      allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
      maxWidth = 2000,
      maxHeight = 2000
    } = options

    return new Promise((resolve) => {
      // Check file size
      if (file.size > maxSize) {
        resolve(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`)
        return
      }

      // Check file type
      if (!allowedTypes.includes(file.type)) {
        resolve(`File type must be one of: ${allowedTypes.join(', ')}`)
        return
      }

      // Check image dimensions (for images only)
      if (file.type.startsWith('image/')) {
        const img = new Image()
        img.onload = () => {
          if (img.width > maxWidth || img.height > maxHeight) {
            resolve(`Image dimensions must be less than ${maxWidth}x${maxHeight}px`)
          } else {
            resolve(null)
          }
        }
        img.onerror = () => resolve('Invalid image file')
        img.src = URL.createObjectURL(file)
      } else {
        resolve(null)
      }
    })
  }
}
