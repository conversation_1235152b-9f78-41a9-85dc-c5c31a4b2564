/**
 * Google Maps Geocoding Service
 * Provides accurate geocoding using Google Maps Geocoding API
 */

export interface GoogleGeocodingResult {
  coordinates: {
    lat: number
    lng: number
  }
  formattedAddress: string
  addressComponents: {
    streetNumber?: string
    route?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  placeId?: string
}

export interface GoogleGeocodingError {
  status: string
  message: string
}

/**
 * Geocode an address using Google Maps Geocoding API
 */
export async function geocodeAddressGoogle(address: string): Promise<GoogleGeocodingResult | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  
  if (!apiKey) {
    console.warn('Google Maps API key not found, falling back to alternative geocoding')
    return null
  }

  try {
    const encodedAddress = encodeURIComponent(address.trim())
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${apiKey}`
    
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0]
      const location = result.geometry.location
      
      // Parse address components
      const addressComponents: GoogleGeocodingResult['addressComponents'] = {}
      
      result.address_components?.forEach((component: any) => {
        const types = component.types
        
        if (types.includes('street_number')) {
          addressComponents.streetNumber = component.long_name
        } else if (types.includes('route')) {
          addressComponents.route = component.long_name
        } else if (types.includes('locality')) {
          addressComponents.city = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          addressComponents.state = component.short_name
        } else if (types.includes('postal_code')) {
          addressComponents.zipCode = component.long_name
        } else if (types.includes('country')) {
          addressComponents.country = component.short_name
        }
      })
      
      return {
        coordinates: {
          lat: location.lat,
          lng: location.lng
        },
        formattedAddress: result.formatted_address,
        addressComponents,
        placeId: result.place_id
      }
    } else {
      console.warn('Google Geocoding API returned no results for:', address)
      return null
    }
  } catch (error) {
    console.error('Google geocoding failed:', error)
    return null
  }
}

/**
 * Reverse geocode coordinates to get address information
 */
export async function reverseGeocodeGoogle(lat: number, lng: number): Promise<GoogleGeocodingResult | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  
  if (!apiKey) {
    console.warn('Google Maps API key not found')
    return null
  }

  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
    
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0]
      
      // Parse address components
      const addressComponents: GoogleGeocodingResult['addressComponents'] = {}
      
      result.address_components?.forEach((component: any) => {
        const types = component.types
        
        if (types.includes('street_number')) {
          addressComponents.streetNumber = component.long_name
        } else if (types.includes('route')) {
          addressComponents.route = component.long_name
        } else if (types.includes('locality')) {
          addressComponents.city = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          addressComponents.state = component.short_name
        } else if (types.includes('postal_code')) {
          addressComponents.zipCode = component.long_name
        } else if (types.includes('country')) {
          addressComponents.country = component.short_name
        }
      })
      
      return {
        coordinates: {
          lat,
          lng
        },
        formattedAddress: result.formatted_address,
        addressComponents,
        placeId: result.place_id
      }
    } else {
      console.warn('Google Reverse Geocoding API returned no results for:', lat, lng)
      return null
    }
  } catch (error) {
    console.error('Google reverse geocoding failed:', error)
    return null
  }
}

/**
 * Get place details using Google Places API
 */
export async function getPlaceDetails(placeId: string): Promise<any | null> {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  
  if (!apiKey) {
    console.warn('Google Maps API key not found')
    return null
  }

  try {
    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${apiKey}`
    
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (data.status === 'OK' && data.result) {
      return data.result
    } else {
      console.warn('Google Places API returned no results for place ID:', placeId)
      return null
    }
  } catch (error) {
    console.error('Google Places API failed:', error)
    return null
  }
}

/**
 * Batch geocode multiple addresses
 */
export async function batchGeocodeGoogle(addresses: string[]): Promise<(GoogleGeocodingResult | null)[]> {
  const results: (GoogleGeocodingResult | null)[] = []
  
  // Process in batches to respect rate limits
  const batchSize = 10
  const delay = 100 // ms between requests
  
  for (let i = 0; i < addresses.length; i += batchSize) {
    const batch = addresses.slice(i, i + batchSize)
    
    const batchPromises = batch.map(async (address, index) => {
      // Add delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, index * delay))
      return geocodeAddressGoogle(address)
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // Add delay between batches
    if (i + batchSize < addresses.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  return results
}

/**
 * Validate if coordinates are within a reasonable range
 */
export function validateCoordinates(lat: number, lng: number): boolean {
  return (
    lat >= -90 && lat <= 90 &&
    lng >= -180 && lng <= 180 &&
    !isNaN(lat) && !isNaN(lng)
  )
}

/**
 * Calculate distance between two points using Haversine formula
 * Returns distance in miles
 */
export function calculateDistanceGoogle(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}
