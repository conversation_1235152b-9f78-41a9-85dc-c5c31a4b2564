"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  Send, 
  Paperclip, 
  X, 
  FileText, 
  Image as ImageIcon,
  Loader2
} from "lucide-react"

interface MessageComposerProps {
  onSendMessage: (content: string, attachments?: string[]) => Promise<void>
  disabled?: boolean
  placeholder?: string
}

export function MessageComposer({ 
  onSendMessage, 
  disabled = false, 
  placeholder = "Type your message..." 
}: MessageComposerProps) {
  const [message, setMessage] = useState("")
  const [attachments, setAttachments] = useState<string[]>([])
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return

    try {
      await onSendMessage(message.trim(), attachments)
      setMessage("")
      setAttachments([])
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setUploading(true)
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Create FormData for file upload
        const formData = new FormData()
        formData.append('file', file)

        // Upload to a generic file upload endpoint
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData
        })

        if (!response.ok) {
          throw new Error('Upload failed')
        }

        const data = await response.json()
        return data.url
      })

      const uploadedUrls = await Promise.all(uploadPromises)
      setAttachments(prev => [...prev, ...uploadedUrls])
    } catch (error) {
      console.error('Error uploading files:', error)
    } finally {
      setUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const getFileIcon = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase()
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return ImageIcon
    }
    return FileText
  }

  const getFileName = (url: string) => {
    return url.split('/').pop() || 'Unknown file'
  }

  return (
    <div className="p-4">
      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {attachments.map((attachment, index) => {
            const FileIcon = getFileIcon(attachment)
            const fileName = getFileName(attachment)

            return (
              <Badge
                key={index}
                variant="secondary"
                className="bg-neutral-800 text-neutral-300 pr-1 max-w-[200px]"
              >
                <FileIcon className="h-3 w-3 mr-1 flex-shrink-0" />
                <span className="truncate text-xs">{fileName}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 hover:bg-neutral-700"
                  onClick={() => removeAttachment(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}

      {/* Message Input */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={disabled ? placeholder : placeholder}
            disabled={disabled}
            className="min-h-[60px] max-h-[120px] bg-neutral-800 border-neutral-700 text-white resize-none"
            rows={2}
          />
        </div>

        <div className="flex flex-col gap-2">
          {/* File Upload Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || uploading}
            className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
          >
            {uploading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Paperclip className="h-4 w-4" />
            )}
          </Button>

          {/* Send Button */}
          <Button
            onClick={handleSend}
            disabled={disabled || (!message.trim() && attachments.length === 0)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf,.doc,.docx,.txt"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Helper Text */}
      <p className="text-xs text-neutral-500 mt-2">
        Press Enter to send, Shift+Enter for new line
      </p>
    </div>
  )
}
