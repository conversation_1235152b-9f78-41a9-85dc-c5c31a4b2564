"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Send, 
  Paperclip, 
  MoreVertical, 
  Building2, 
  User,
  CheckCircle2,
  Clock,
  XCircle
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { MessageComposer } from "./message-composer"
import { MessageBubble } from "./message-bubble"
import type { MessageThreadWithDetails, Message } from "@/lib/types"

interface ConversationViewProps {
  thread: MessageThreadWithDetails
  onNewMessage: () => void
}

export function ConversationView({ thread, onNewMessage }: ConversationViewProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    fetchMessages()
  }, [thread.id])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const fetchMessages = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/messages/threads/${thread.id}`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages || [])
      }
    } catch (error) {
      console.error('Error fetching messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSendMessage = async (content: string, attachments?: string[]) => {
    if (!content.trim()) return

    setSending(true)
    try {
      const response = await fetch(`/api/messages/threads/${thread.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content, attachments })
      })

      if (response.ok) {
        await fetchMessages()
        onNewMessage()
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setSending(false)
    }
  }

  const getThreadStatus = () => {
    switch (thread.status) {
      case 'active':
        return { icon: CheckCircle2, color: 'text-green-400', label: 'Active' }
      case 'closed':
        return { icon: XCircle, color: 'text-red-400', label: 'Closed' }
      case 'archived':
        return { icon: Clock, color: 'text-neutral-400', label: 'Archived' }
      default:
        return { icon: CheckCircle2, color: 'text-green-400', label: 'Active' }
    }
  }

  const status = getThreadStatus()

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-neutral-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={thread.business?.owner?.avatar_url || thread.user?.avatar_url} />
              <AvatarFallback className="bg-neutral-700 text-white">
                {thread.business ? (
                  <Building2 className="h-5 w-5" />
                ) : (
                  <User className="h-5 w-5" />
                )}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-white">
                {thread.business?.name || thread.user?.full_name || 'Unknown'}
              </h3>
              <p className="text-sm text-neutral-400">
                {thread.subject || 'No subject'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              className={`${status.color} border-current`}
            >
              <status.icon className="h-3 w-3 mr-1" />
              {status.label}
            </Badge>
            <Button variant="ghost" size="sm" className="text-neutral-400">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex gap-3">
                <div className="w-8 h-8 bg-neutral-700 rounded-full animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-4 bg-neutral-700 rounded mb-2 animate-pulse"></div>
                  <div className="h-16 bg-neutral-700 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-neutral-400 mb-2">No messages yet</p>
            <p className="text-neutral-500 text-sm">Start the conversation below</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isOwn={message.author_id === thread.user_id}
              />
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Message Composer */}
      <div className="border-t border-neutral-800">
        <MessageComposer
          onSendMessage={handleSendMessage}
          disabled={sending || thread.status === 'closed'}
          placeholder={
            thread.status === 'closed' 
              ? 'This conversation is closed' 
              : 'Type your message...'
          }
        />
      </div>
    </div>
  )
}
