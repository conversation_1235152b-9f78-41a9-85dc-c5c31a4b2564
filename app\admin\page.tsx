import type { Metada<PERSON> } from "next"
import { Suspense } from "react"
import { AdminDashboardContent } from "@/components/admin/admin-dashboard-content"
import { AdminLoading } from "@/components/admin/admin-loading"

export const metadata: Metadata = {
  title: "Admin Dashboard - PressureWash Pro",
  description: "Administrative control panel for managing users, businesses, and platform analytics",
}

export default function AdminPage() {
  return (
    <Suspense fallback={<AdminLoading type="dashboard" />}>
      <AdminDashboardContent />
    </Suspense>
  )
}
