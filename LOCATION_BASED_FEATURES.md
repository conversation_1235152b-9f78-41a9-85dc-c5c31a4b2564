# Location-Based Featured Businesses

## Overview

The homepage now features location detection to show the top 9 pressure washing businesses near the user's location, providing a personalized experience that highlights relevant local services.

## 🎯 Features

### **Location Detection**
- **Browser geolocation API** for precise location detection
- **Reverse geocoding** to get city/state information
- **Privacy-focused** - only requests location when user clicks "Use My Location"
- **Graceful fallback** to featured businesses if location is denied/unavailable

### **Smart Business Display**
- **Top 9 businesses** sorted by rating within user's area
- **Distance calculation** showing miles from user location
- **Real business data** from the database with portfolio images
- **Fallback to curated list** if no local businesses found

### **User Experience**
- **Location prompt** with clear explanation and benefits
- **Skip option** for users who prefer not to share location
- **Loading states** during location detection and business fetching
- **Error handling** with informative messages

## 📁 Implementation

### **Files Created**
```
hooks/use-location.ts              # Location detection hook
components/featured-businesses.tsx # Location-aware business display
```

### **Files Modified**
```
app/page.tsx                      # Updated to use new component
```

## 🔧 Technical Details

### **Location Hook (`use-location.ts`)**
```typescript
interface LocationData {
  latitude: number
  longitude: number
  city?: string
  state?: string
  zipCode?: string
}

const { location, loading, error, requestLocation } = useLocation()
```

**Features:**
- Geolocation API integration with error handling
- Reverse geocoding using BigDataCloud API (free tier)
- Local storage for user preferences
- Timeout and accuracy configuration

### **Featured Businesses Component**
```typescript
<FeaturedBusinesses fallbackBusinesses={[...]} />
```

**States:**
1. **Location Prompt** - Asks user for location permission
2. **Loading** - Shows skeleton cards while fetching
3. **Location-Based** - Displays nearby businesses with distance
4. **Fallback** - Shows curated businesses if location fails
5. **Empty State** - Handles no businesses found scenario

### **API Integration**
Uses existing search API with location parameters:
```
GET /api/search?latitude={lat}&longitude={lng}&limit=9&sortBy=rating
```

## 🎨 User Interface

### **Location Prompt**
- **Clean card design** with location icon
- **Clear value proposition** explaining benefits
- **Two-button choice** - "Use My Location" or "Skip for Now"
- **Loading indicator** during location detection

### **Business Cards**
- **Portfolio image** as card header (if available)
- **Business name** and rating with star display
- **Review count** and distance from user
- **Description** and location information
- **Hover effects** consistent with site design

### **Loading States**
- **Skeleton cards** matching final layout
- **Smooth transitions** between states
- **Progress indicators** for location detection

## 🌍 Location Features

### **Geolocation Options**
```typescript
{
  enableHighAccuracy: true,    // GPS if available
  timeout: 10000,             // 10 second timeout
  maximumAge: 300000          // 5 minute cache
}
```

### **Reverse Geocoding**
- **Free service** from BigDataCloud
- **No API key required** for basic usage
- **Fallback gracefully** if geocoding fails
- **City/state display** in business header

### **Privacy Considerations**
- **User-initiated** location requests only
- **No automatic tracking** on page load
- **Clear messaging** about location usage
- **Easy to skip** for privacy-conscious users

## 📊 Business Logic

### **Business Selection**
1. **Location-based search** within reasonable radius
2. **Sort by rating** to show highest quality first
3. **Limit to 9 businesses** for clean 3x3 grid
4. **Include distance** for user context

### **Fallback Strategy**
```
User Location → Nearby Businesses → Fallback List → Empty State
```

### **Error Handling**
- **Location denied** → Show fallback businesses
- **Location timeout** → Show fallback with error message
- **API failure** → Show fallback businesses
- **No businesses found** → Show empty state with action

## 🚀 Benefits

### **For Users**
- **Relevant results** based on actual location
- **Distance context** for decision making
- **Local businesses** they can actually use
- **Faster discovery** of nearby services

### **For Businesses**
- **Local visibility** for nearby customers
- **Geographic targeting** without paid ads
- **Quality-based ranking** rewards good service
- **Equal opportunity** for all locations

### **For Platform**
- **Higher engagement** with relevant content
- **Better conversion** from local relevance
- **Improved user experience** with personalization
- **Competitive advantage** over generic directories

## 🔄 User Flow

### **First Visit**
1. User lands on homepage
2. Sees location prompt in featured section
3. Clicks "Use My Location" or "Skip for Now"
4. If location granted → Shows nearby businesses
5. If skipped → Shows curated featured businesses

### **Return Visit**
1. Location preference remembered
2. Automatic display based on previous choice
3. Option to change location preference
4. Consistent experience across sessions

## 📈 Future Enhancements

### **Potential Improvements**
- **Radius selection** (5, 10, 25 miles)
- **Location search** by city/zip code
- **Saved locations** for multiple areas
- **Business density indicators**
- **Map view integration**
- **Location-based notifications**

### **Advanced Features**
- **Service area mapping** for businesses
- **Travel time estimates** using routing APIs
- **Weather-based recommendations**
- **Seasonal service highlighting**
- **Local market insights**

The location-based featured businesses provide a personalized, relevant experience that connects users with local pressure washing services while maintaining privacy and providing graceful fallbacks for all scenarios.
