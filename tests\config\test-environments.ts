/**
 * Test Environment Configuration
 * Manages different testing environments and their configurations
 */

export interface TestEnvironment {
  name: string
  supabaseUrl: string
  supabaseAnonKey: string
  supabaseServiceKey: string
  databaseUrl?: string
  isLocal: boolean
}

export const TEST_ENVIRONMENTS = {
  local: {
    name: 'local',
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321',
    supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-anon-key',
    supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-key',
    databaseUrl: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:54322/postgres',
    isLocal: true,
  },
  staging: {
    name: 'staging',
    supabaseUrl: process.env.STAGING_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    supabaseAnonKey: process.env.STAGING_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    supabaseServiceKey: process.env.STAGING_SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY || '',
    isLocal: false,
  },
  production: {
    name: 'production',
    supabaseUrl: process.env.PROD_SUPABASE_URL || '',
    supabaseAnonKey: process.env.PROD_SUPABASE_ANON_KEY || '',
    supabaseServiceKey: process.env.PROD_SUPABASE_SERVICE_KEY || '',
    isLocal: false,
  },
} as const

export type EnvironmentName = keyof typeof TEST_ENVIRONMENTS

export function getTestEnvironment(envName?: string): TestEnvironment {
  const env = (envName || process.env.TEST_ENV || 'local') as EnvironmentName
  
  if (!TEST_ENVIRONMENTS[env]) {
    throw new Error(`Unknown test environment: ${env}`)
  }
  
  const environment = TEST_ENVIRONMENTS[env]
  
  // Validate required environment variables
  if (!environment.supabaseUrl || !environment.supabaseAnonKey || !environment.supabaseServiceKey) {
    throw new Error(`Missing required environment variables for ${env} environment`)
  }
  
  return environment
}

export function validateEnvironment(env: TestEnvironment): void {
  const required = ['supabaseUrl', 'supabaseAnonKey', 'supabaseServiceKey']
  
  for (const key of required) {
    if (!env[key as keyof TestEnvironment]) {
      throw new Error(`Missing required environment variable: ${key}`)
    }
  }
}