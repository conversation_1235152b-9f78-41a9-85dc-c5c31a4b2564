---
inclusion: always
---

# Product Context

PressureWash Pro Directory is a specialized two-sided marketplace connecting homeowners with pressure washing service providers. Focus on niche-specific features over generic marketplace functionality.

## User Roles & Capabilities

### Homeowners (Customers)
- Browse and search businesses by location and services
- View detailed business profiles, reviews, and pricing
- Send messages and request quotes
- Leave reviews after service completion
- **No subscription required** - all features free

### Business Owners (Service Providers)
- **Free Tier**: Basic profile, limited photos, standard search visibility
- **Premium Tier**: Enhanced profiles, unlimited photos, priority placement, lead notifications
- Manage services, pricing, and availability
- Respond to customer inquiries and quote requests
- Request and manage customer reviews

### Admin Users
- Full CRUD operations on all entities
- User management and content moderation
- Analytics and business intelligence access

## Feature Prioritization Guidelines

1. **Location-first**: All features must consider geographic relevance
2. **Mobile-optimized**: Homeowners primarily search on mobile devices
3. **Trust signals**: Reviews, verification badges, and business credentials are critical
4. **Niche specialization**: Pressure washing-specific filters and content over generic options
5. **Conversion-focused**: Streamline path from search to business contact

## Business Logic Rules

### Subscription & Feature Gating
- Free businesses get basic visibility and functionality
- Premium features require active subscription
- Graceful degradation when subscriptions expire (don't hide, just limit)

### Review System
- Only verified customers can leave reviews
- Businesses can respond to reviews but not edit them
- Review aggregation affects search ranking

### Search & Discovery
- Location radius is primary filter
- Service type and specialization are secondary filters
- Premium businesses get boosted visibility
- Inactive businesses (>90 days) get reduced visibility

## Data & Architecture Implications

### Multi-tenant Design
- All data isolated by business ownership
- Row-level security enforces data access
- UUID-based relationships prevent enumeration attacks

### Performance Considerations
- Denormalized rating/review counts for fast search
- Location-based indexing for geographic queries
- Image optimization for mobile performance

### Integration Points
- Messaging system connects homeowners and businesses
- Review system integrates with business profiles
- Search functionality spans multiple entity types