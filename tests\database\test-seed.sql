-- Test Data Seeding Script for PressureWash Pro Directory
-- This script creates realistic test data for comprehensive testing

-- Clear existing test data (in reverse dependency order)
DELETE FROM messages WHERE thread_id IN (SELECT id FROM message_threads WHERE created_at > NOW() - INTERVAL '1 day');
DELETE FROM message_threads WHERE created_at > NOW() - INTERVAL '1 day';
DELETE FROM reviews WHERE business_id IN (SELECT id FROM businesses WHERE name LIKE 'Test%');
DELETE FROM portfolio_images WHERE business_id IN (SELECT id FROM businesses WHERE name LIKE 'Test%');
DELETE FROM business_services WHERE business_id IN (SELECT id FROM businesses WHERE name LIKE 'Test%');
DELETE FROM business_members WHERE business_id IN (SELECT id FROM businesses WHERE name LIKE 'Test%');
DELETE FROM locations WHERE business_id IN (SELECT id FROM businesses WHERE name LIKE 'Test%');
DELETE FROM businesses WHERE name LIKE 'Test%';
DELETE FROM profiles WHERE email LIKE '%<EMAIL>';

-- Insert test user profiles
INSERT INTO profiles (id, email, full_name, phone, created_at, updated_at) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Sarah Johnson', '******-0101', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Mike Thompson', '******-0102', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Test Administrator', '******-0103', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'John Davis', '******-0104', NOW(), NOW()),
  ('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Lisa Wilson', '******-0105', NOW(), NOW());

-- Insert test businesses
INSERT INTO businesses (id, name, slug, description, phone, email, website, status, subscription_tier, avg_rating, review_count, created_at, updated_at) VALUES
  ('660e8400-e29b-41d4-a716-446655440001', 'Test Power Clean Pro', 'test-power-clean-pro', 'Professional pressure washing services for residential and commercial properties. We specialize in house washing, driveway cleaning, and deck restoration.', '******-0201', '<EMAIL>', 'https://testpowerclean.com', 'active', 'premium', 4.8, 15, NOW(), NOW()),
  ('660e8400-e29b-41d4-a716-446655440002', 'Test Sparkle Wash', 'test-sparkle-wash', 'Family-owned pressure washing business serving the local community for over 10 years. Eco-friendly cleaning solutions and competitive pricing.', '******-0202', '<EMAIL>', 'https://testsparklewash.com', 'active', 'free', 4.5, 8, NOW(), NOW()),
  ('660e8400-e29b-41d4-a716-446655440003', 'Test Elite Pressure Services', 'test-elite-pressure-services', 'High-end pressure washing and soft washing services. Commercial and residential. Fully insured and bonded.', '******-0203', '<EMAIL>', NULL, 'active', 'premium', 4.9, 22, NOW(), NOW());

-- Insert business members (owners)
INSERT INTO business_members (id, business_id, profile_id, role, created_at, updated_at) VALUES
  ('770e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'owner', NOW(), NOW()),
  ('770e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'owner', NOW(), NOW()),
  ('770e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', 'owner', NOW(), NOW());

-- Insert business locations
INSERT INTO locations (id, business_id, address, city, state, zip_code, latitude, longitude, is_primary, created_at, updated_at) VALUES
  ('880e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '123 Main Street', 'Austin', 'TX', '78701', 30.2672, -97.7431, true, NOW(), NOW()),
  ('880e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '456 Oak Avenue', 'Austin', 'TX', '78702', 30.2500, -97.7300, true, NOW(), NOW()),
  ('880e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', '789 Pine Road', 'Austin', 'TX', '78703', 30.2800, -97.7500, true, NOW(), NOW());

-- Insert business services (link to existing services)
INSERT INTO business_services (id, business_id, service_id, price_range, description, created_at, updated_at) VALUES
  ('990e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', (SELECT id FROM services WHERE name = 'House Washing' LIMIT 1), '$200-400', 'Complete exterior house washing with eco-friendly detergents', NOW(), NOW()),
  ('990e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', (SELECT id FROM services WHERE name = 'Driveway Cleaning' LIMIT 1), '$100-200', 'Professional driveway and walkway pressure washing', NOW(), NOW()),
  ('990e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', (SELECT id FROM services WHERE name = 'House Washing' LIMIT 1), '$150-300', 'Affordable house washing for families', NOW(), NOW()),
  ('990e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440003', (SELECT id FROM services WHERE name = 'Commercial Cleaning' LIMIT 1), '$500-1500', 'Large-scale commercial pressure washing', NOW(), NOW());

-- Insert portfolio images
INSERT INTO portfolio_images (id, business_id, image_url, caption, display_order, created_at, updated_at) VALUES
  ('aa0e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '/placeholder.jpg', 'Before and after house washing', 1, NOW(), NOW()),
  ('aa0e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', '/placeholder.jpg', 'Clean driveway results', 2, NOW(), NOW()),
  ('aa0e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', '/placeholder.jpg', 'Satisfied customer home', 1, NOW(), NOW());

-- Insert test reviews
INSERT INTO reviews (id, business_id, customer_id, rating, title, content, created_at, updated_at) VALUES
  ('bb0e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 5, 'Excellent Service!', 'Mike and his team did an amazing job cleaning our house exterior. Very professional and the results exceeded our expectations.', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),
  ('bb0e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440004', 4, 'Great Results', 'Our driveway looks brand new! Fair pricing and showed up on time.', NOW() - INTERVAL '10 days', NOW() - INTERVAL '10 days'),
  ('bb0e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 5, 'Highly Recommend', 'Family business that really cares about quality. Will definitely use again.', NOW() - INTERVAL '15 days', NOW() - INTERVAL '15 days');

-- Insert test message threads
INSERT INTO message_threads (id, business_id, customer_id, subject, status, created_at, updated_at) VALUES
  ('cc0e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Quote Request for House Washing', 'active', NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day'),
  ('cc0e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'Driveway Cleaning Inquiry', 'active', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day');

-- Insert test messages
INSERT INTO messages (id, thread_id, sender_id, content, created_at, updated_at) VALUES
  ('dd0e8400-e29b-41d4-a716-446655440001', 'cc0e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Hi, I would like to get a quote for washing the exterior of my 2-story house. The house is about 2,500 sq ft.', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days'),
  ('dd0e8400-e29b-41d4-a716-446655440002', 'cc0e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Thanks for reaching out! I would be happy to provide a quote. Could you please share your address so I can give you an accurate estimate?', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day'),
  ('dd0e8400-e29b-41d4-a716-446655440003', 'cc0e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'Hello, I need my driveway cleaned. It has some oil stains. Can you handle that?', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day');

-- Update business ratings based on reviews
UPDATE businesses SET 
  avg_rating = (
    SELECT ROUND(AVG(rating::numeric), 1) 
    FROM reviews 
    WHERE business_id = businesses.id
  ),
  review_count = (
    SELECT COUNT(*) 
    FROM reviews 
    WHERE business_id = businesses.id
  )
WHERE id IN (
  '660e8400-e29b-41d4-a716-446655440001',
  '660e8400-e29b-41d4-a716-446655440002',
  '660e8400-e29b-41d4-a716-446655440003'
);