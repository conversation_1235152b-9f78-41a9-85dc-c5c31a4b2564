-- Review Reports Table for Moderation
-- This table stores reports about inappropriate reviews

CREATE TABLE IF NOT EXISTS public.review_reports (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL REFERENCES public.reviews(id) ON DELETE CASCADE,
  reporter_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  reason TEXT NOT NULL CHECK (reason IN ('spam', 'inappropriate', 'fake', 'offensive', 'other')),
  description TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
  admin_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_review_reports_review_id ON public.review_reports(review_id);
CREATE INDEX IF NOT EXISTS idx_review_reports_reporter_id ON public.review_reports(reporter_id);
CREATE INDEX IF NOT EXISTS idx_review_reports_status ON public.review_reports(status);
CREATE INDEX IF NOT EXISTS idx_review_reports_created_at ON public.review_reports(created_at);

-- Add RLS policies
ALTER TABLE public.review_reports ENABLE ROW LEVEL SECURITY;

-- Users can only see their own reports
CREATE POLICY "Users can view their own reports" ON public.review_reports
  FOR SELECT USING (auth.uid() = reporter_id);

-- Users can create reports
CREATE POLICY "Users can create reports" ON public.review_reports
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- Admins can view and manage all reports
CREATE POLICY "Admins can manage all reports" ON public.review_reports
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_review_reports_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_review_reports_updated_at
  BEFORE UPDATE ON public.review_reports
  FOR EACH ROW
  EXECUTE FUNCTION update_review_reports_updated_at();

-- Add comments
COMMENT ON TABLE public.review_reports IS 'Reports about inappropriate or problematic reviews for moderation.';
COMMENT ON COLUMN public.review_reports.reason IS 'Reason for reporting: spam, inappropriate, fake, offensive, other';
COMMENT ON COLUMN public.review_reports.status IS 'Report status: pending, reviewed, resolved, dismissed';

-- Add function to automatically update business ratings when reviews change
CREATE OR REPLACE FUNCTION update_business_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the business rating for the affected business
  UPDATE public.businesses 
  SET 
    avg_rating = (
      SELECT COALESCE(ROUND(AVG(rating)::numeric, 1), 0)
      FROM public.reviews 
      WHERE business_id = COALESCE(NEW.business_id, OLD.business_id)
    ),
    review_count = (
      SELECT COUNT(*)
      FROM public.reviews 
      WHERE business_id = COALESCE(NEW.business_id, OLD.business_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.business_id, OLD.business_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update business ratings
DROP TRIGGER IF EXISTS trigger_update_business_rating_on_insert ON public.reviews;
CREATE TRIGGER trigger_update_business_rating_on_insert
  AFTER INSERT ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_business_rating();

DROP TRIGGER IF EXISTS trigger_update_business_rating_on_update ON public.reviews;
CREATE TRIGGER trigger_update_business_rating_on_update
  AFTER UPDATE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_business_rating();

DROP TRIGGER IF EXISTS trigger_update_business_rating_on_delete ON public.reviews;
CREATE TRIGGER trigger_update_business_rating_on_delete
  AFTER DELETE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_business_rating();

-- Add constraint to prevent duplicate reviews from same user for same business
ALTER TABLE public.reviews 
ADD CONSTRAINT unique_user_business_review 
UNIQUE (author_id, business_id);

COMMENT ON CONSTRAINT unique_user_business_review ON public.reviews IS 'Prevents users from leaving multiple reviews for the same business';
