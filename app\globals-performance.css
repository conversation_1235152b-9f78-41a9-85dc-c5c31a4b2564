/* Performance Optimizations */

/* Enable hardware acceleration for smooth animations */
.admin-layout {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimize sidebar transitions */
.admin-sidebar {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize navigation links */
.admin-nav-link {
  transform: translateZ(0);
  transition: all 0.15s ease-out;
  will-change: background-color, border-color;
}

.admin-nav-link:hover {
  transform: translateZ(0);
}

/* Optimize card hover effects */
.card-hover-blue {
  transform: translateZ(0);
  transition: all 0.2s ease-out;
  will-change: border-color, box-shadow;
}

/* Optimize table rendering */
.admin-table {
  contain: layout style paint;
  transform: translateZ(0);
}

.admin-table-row {
  contain: layout style;
  will-change: background-color;
}

/* Optimize modal animations */
.modal-content {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize scroll areas */
.scroll-area {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize chart containers */
.chart-container {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Reduce paint operations */
.admin-header {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize button interactions */
.admin-button {
  transform: translateZ(0);
  will-change: transform, background-color;
  transition: all 0.15s ease-out;
}

.admin-button:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Optimize loading states */
.loading-skeleton {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize form inputs */
.admin-input {
  contain: layout style;
  will-change: border-color, box-shadow;
}

/* Optimize dropdown menus */
.dropdown-content {
  contain: layout style paint;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize badge animations */
.admin-badge {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize icon rendering */
.admin-icon {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize search results */
.search-results {
  contain: layout style paint;
}

.search-result-item {
  contain: layout style;
  will-change: background-color;
}

/* Optimize messaging interface */
.message-thread {
  contain: layout style;
  will-change: background-color;
}

.message-bubble {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize bulk import interface */
.import-progress {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize dashboard metrics */
.metric-card {
  contain: layout style paint;
  transform: translateZ(0);
  will-change: border-color, box-shadow;
}

/* Optimize data tables */
.data-table {
  contain: layout style paint;
}

.data-table-cell {
  contain: layout style;
}

/* Optimize pagination */
.pagination-controls {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize filter panels */
.filter-panel {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize status indicators */
.status-indicator {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize progress bars */
.progress-bar {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Optimize tooltip rendering */
.tooltip {
  contain: layout style paint;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize mobile responsiveness */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%) translateZ(0);
    transition: transform 0.3s ease-out;
  }
  
  .admin-sidebar.open {
    transform: translateX(0) translateZ(0);
  }
}

/* Optimize print styles */
@media print {
  .admin-sidebar,
  .admin-header {
    display: none;
  }
  
  .admin-content {
    margin: 0;
    padding: 0;
  }
}

/* Optimize reduced motion */
@media (prefers-reduced-motion: reduce) {
  .admin-nav-link,
  .card-hover-blue,
  .admin-button {
    transition: none;
  }
  
  .admin-button:hover {
    transform: none;
  }
}

/* Optimize high contrast mode */
@media (prefers-contrast: high) {
  .admin-nav-link {
    border: 2px solid transparent;
  }
  
  .admin-nav-link:focus {
    border-color: currentColor;
  }
}

/* Optimize dark mode (already default) */
@media (prefers-color-scheme: dark) {
  /* Already optimized for dark mode */
}

/* GPU acceleration for smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
  transform: translateZ(0);
}

/* Optimize focus indicators */
.focus-visible {
  outline: 2px solid hsl(217 91% 60%);
  outline-offset: 2px;
}

/* Optimize selection styles */
::selection {
  background-color: hsl(217 91% 60% / 0.3);
  color: white;
}

/* Optimize scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(0 0% 14.9%);
}

::-webkit-scrollbar-thumb {
  background: hsl(0 0% 25%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(0 0% 35%);
}
