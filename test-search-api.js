// Test script for search API functionality
const { searchBusinesses } = require('./lib/database.ts')

async function testSearchFunctionality() {
  console.log('Testing search functionality...\n')
  
  // Test 1: Basic search without location
  console.log('Test 1: Basic search')
  try {
    const result1 = await searchBusinesses({
      query: 'pressure',
      limit: 5
    })
    console.log('✓ Basic search successful')
    console.log(`Found ${result1.businesses.length} businesses`)
    console.log(`Total: ${result1.total}`)
  } catch (error) {
    console.log('✗ Basic search failed:', error.message)
  }
  
  // Test 2: Location-based search
  console.log('\nTest 2: Location-based search')
  try {
    const result2 = await searchBusinesses({
      latitude: 33.4484,
      longitude: -112.0740,
      radius: 10,
      limit: 5
    })
    console.log('✓ Location-based search successful')
    console.log(`Found ${result2.businesses.length} businesses within 10 miles`)
    result2.businesses.forEach(business => {
      console.log(`- ${business.name}: ${business.distance ? business.distance.toFixed(1) + ' mi' : 'N/A'}`)
    })
  } catch (error) {
    console.log('✗ Location-based search failed:', error.message)
  }
  
  // Test 3: Filter by rating
  console.log('\nTest 3: Filter by rating')
  try {
    const result3 = await searchBusinesses({
      minRating: 4.5,
      limit: 5
    })
    console.log('✓ Rating filter successful')
    console.log(`Found ${result3.businesses.length} businesses with rating >= 4.5`)
    result3.businesses.forEach(business => {
      console.log(`- ${business.name}: ${business.avg_rating} stars`)
    })
  } catch (error) {
    console.log('✗ Rating filter failed:', error.message)
  }
  
  // Test 4: Sort by different criteria
  console.log('\nTest 4: Sort by reviews')
  try {
    const result4 = await searchBusinesses({
      sortBy: 'reviews',
      sortOrder: 'desc',
      limit: 5
    })
    console.log('✓ Sort by reviews successful')
    console.log(`Found ${result4.businesses.length} businesses sorted by review count`)
    result4.businesses.forEach(business => {
      console.log(`- ${business.name}: ${business.review_count} reviews`)
    })
  } catch (error) {
    console.log('✗ Sort by reviews failed:', error.message)
  }
  
  // Test 5: Pagination
  console.log('\nTest 5: Pagination')
  try {
    const result5a = await searchBusinesses({
      limit: 2,
      offset: 0
    })
    const result5b = await searchBusinesses({
      limit: 2,
      offset: 2
    })
    console.log('✓ Pagination successful')
    console.log(`Page 1: ${result5a.businesses.length} businesses`)
    console.log(`Page 2: ${result5b.businesses.length} businesses`)
    console.log(`Total available: ${result5a.total}`)
  } catch (error) {
    console.log('✗ Pagination failed:', error.message)
  }
  
  console.log('\nSearch functionality tests completed!')
}

// Run the tests
testSearchFunctionality().catch(console.error)