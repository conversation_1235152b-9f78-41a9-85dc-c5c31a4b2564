"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area"
import { 
  MessageSquare, 
  Users, 
  AlertTriangle, 
  TrendingUp,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Ban,
  CheckCircle2
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import type { MessageThreadWithDetails } from "@/lib/types"

export function AdminMessagingDashboard() {
  const [threads, setThreads] = useState<MessageThreadWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  useEffect(() => {
    fetchThreads()
  }, [])

  const fetchThreads = async () => {
    try {
      const response = await fetch('/api/admin/messages/threads')
      if (response.ok) {
        const data = await response.json()
        setThreads(data.threads || [])
      }
    } catch (error) {
      console.error('Error fetching threads:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredThreads = threads.filter(thread => {
    const matchesSearch = !searchQuery || 
      thread.subject?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      thread.business?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      thread.user?.full_name?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || thread.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    total: threads.length,
    active: threads.filter(t => t.status === 'active').length,
    closed: threads.filter(t => t.status === 'closed').length,
    flagged: threads.filter(t => t.status === 'flagged').length
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'closed': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'archived': return 'bg-neutral-500/20 text-neutral-400 border-neutral-500/30'
      case 'flagged': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default: return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Total Conversations</CardTitle>
            <MessageSquare className="h-4 w-4 text-neutral-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{stats.total}</div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Active</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">{stats.active}</div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Closed</CardTitle>
            <Ban className="h-4 w-4 text-red-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-400">{stats.closed}</div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Flagged</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-400">{stats.flagged}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white">Message Threads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-neutral-800 border-neutral-700 text-white"
                />
              </div>
            </div>
            <div className="flex gap-2">
              {['all', 'active', 'closed', 'flagged'].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter(status)}
                  className={statusFilter === status 
                    ? "bg-blue-600 text-white" 
                    : "border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                  }
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Button>
              ))}
            </div>
          </div>

          {/* Thread List */}
          <ScrollArea className="h-[600px]">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="p-4 border border-neutral-800 rounded-lg animate-pulse">
                    <div className="h-4 bg-neutral-700 rounded mb-2"></div>
                    <div className="h-3 bg-neutral-700 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            ) : filteredThreads.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-neutral-600 mx-auto mb-3" />
                <p className="text-neutral-400">No conversations found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredThreads.map((thread) => (
                  <div
                    key={thread.id}
                    className="p-4 border border-neutral-800 rounded-lg hover:bg-neutral-800/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-white truncate">
                            {thread.subject || 'No subject'}
                          </h3>
                          <Badge className={`text-xs ${getStatusColor(thread.status)}`}>
                            {thread.status || 'active'}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-neutral-400 mb-2">
                          <span>Business: {thread.business?.name || 'Unknown'}</span>
                          <span>Customer: {thread.user?.full_name || 'Unknown'}</span>
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-neutral-500">
                          <span>
                            {thread.messages?.length || 0} messages
                          </span>
                          <span>
                            Updated {formatDistanceToNow(new Date(thread.updated_at), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-neutral-400 hover:text-white"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-neutral-400 hover:text-white"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
