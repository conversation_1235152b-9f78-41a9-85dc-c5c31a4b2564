import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await requireAuth()
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not configured' }, { status: 500 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    let query = supabaseAdmin
      .from('review_reports')
      .select(`
        *,
        review:reviews(
          *,
          business:businesses(id, name, slug),
          profile:profiles(id, full_name, email)
        ),
        reporter:profiles(id, full_name, email)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching review reports:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ reports: data })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    await requireAuth()
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not configured' }, { status: 500 })
    }

    const body = await request.json()
    const { reportId, status, adminNotes, action } = body

    if (!reportId) {
      return NextResponse.json({ error: 'Report ID is required' }, { status: 400 })
    }

    let updateData: any = { updated_at: new Date().toISOString() }

    if (status) {
      updateData.status = status
    }

    if (adminNotes) {
      updateData.admin_notes = adminNotes
    }

    // Handle specific actions
    if (action === 'approve_review') {
      updateData.status = 'dismissed'
      updateData.admin_notes = (adminNotes || '') + ' [Review approved by admin]'
    } else if (action === 'remove_review') {
      // First get the report to find the review
      const { data: report, error: reportError } = await supabaseAdmin
        .from('review_reports')
        .select('review_id')
        .eq('id', reportId)
        .single()

      if (reportError || !report) {
        return NextResponse.json({ error: 'Report not found' }, { status: 404 })
      }

      // Delete the review
      const { error: deleteError } = await supabaseAdmin
        .from('reviews')
        .delete()
        .eq('id', report.review_id)

      if (deleteError) {
        console.error('Error deleting review:', deleteError)
        return NextResponse.json({ error: 'Failed to delete review' }, { status: 500 })
      }

      updateData.status = 'resolved'
      updateData.admin_notes = (adminNotes || '') + ' [Review removed by admin]'
    }

    const { data, error } = await supabaseAdmin
      .from('review_reports')
      .update(updateData)
      .eq('id', reportId)
      .select(`
        *,
        review:reviews(
          *,
          business:businesses(id, name, slug),
          profile:profiles(id, full_name, email)
        ),
        reporter:profiles(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error updating review report:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ report: data })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}
