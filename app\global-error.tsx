"use client"

import { useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface GlobalErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    // Log critical error
    console.error('Critical application error:', error)
    
    // In production, send to monitoring service immediately
    if (process.env.NODE_ENV === 'production') {
      try {
        const errorData = {
          message: error.message,
          stack: error.stack,
          digest: error.digest,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          type: 'global_error'
        }
        
        // Send to monitoring service
        // fetch('/api/errors', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(errorData)
        // }).catch(console.error)
        
        console.error('Critical error logged:', errorData)
      } catch (loggingError) {
        console.error('Failed to log critical error:', loggingError)
      }
    }
  }, [error])

  return (
    <html>
      <body className="bg-neutral-950 text-white">
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="bg-neutral-900 border-neutral-800 max-w-2xl w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-20 w-20 text-red-400" />
              </div>
              <CardTitle className="text-white text-3xl mb-2">
                Critical Error
              </CardTitle>
              <p className="text-neutral-400 text-lg">
                A critical error occurred that prevented the application from working properly.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Error information */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h3 className="text-red-400 font-semibold mb-2 flex items-center">
                  <Bug className="h-4 w-4 mr-2" />
                  What happened?
                </h3>
                <p className="text-neutral-300 text-sm">
                  The application encountered a critical error that couldn't be recovered from automatically. 
                  This is usually caused by a severe JavaScript error or a problem with the application's core functionality.
                </p>
              </div>

              {/* Recovery steps */}
              <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
                <h3 className="text-blue-400 font-semibold mb-3">Recovery Steps:</h3>
                <ol className="text-neutral-300 text-sm space-y-2 list-decimal list-inside">
                  <li>Try refreshing the page to reload the application</li>
                  <li>Clear your browser cache and cookies</li>
                  <li>Disable browser extensions that might interfere</li>
                  <li>Try using a different browser or incognito mode</li>
                  <li>Check your internet connection</li>
                </ol>
              </div>

              {/* Error details for development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
                  <h3 className="text-red-400 font-semibold mb-2">Error Details (Development)</h3>
                  <div className="text-sm text-neutral-300 space-y-2">
                    <div>
                      <strong>Message:</strong> {error.message}
                    </div>
                    {error.digest && (
                      <div>
                        <strong>Digest:</strong> {error.digest}
                      </div>
                    )}
                    {error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-xs bg-neutral-900 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={reset}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try to Recover
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </Button>
                <Button
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                  className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {/* Support information */}
              <div className="text-center text-sm text-neutral-500 border-t border-neutral-800 pt-4">
                <p className="mb-2">
                  If this error persists, please contact our technical support team.
                </p>
                <p>
                  <strong>Error ID:</strong> {error.digest || 'N/A'} | 
                  <strong> Time:</strong> {new Date().toLocaleString()}
                </p>
                <p className="mt-2">
                  <a 
                    href="/contact" 
                    className="text-blue-400 hover:text-blue-300 underline"
                    onClick={(e) => {
                      e.preventDefault()
                      window.location.href = '/contact'
                    }}
                  >
                    Contact Support
                  </a>
                  {' | '}
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    Email Support
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </body>
    </html>
  )
}
