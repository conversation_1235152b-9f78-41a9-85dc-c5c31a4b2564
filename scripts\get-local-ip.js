const os = require('os');

function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  
  console.log('🌐 Available Network Interfaces:');
  console.log('================================');
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        console.log(`📡 ${name}: ${interface.address}`);
        
        // This is likely your main local IP
        if (name.toLowerCase().includes('wi-fi') || 
            name.toLowerCase().includes('wireless') || 
            name.toLowerCase().includes('ethernet') ||
            name.toLowerCase().includes('en0') ||
            name.toLowerCase().includes('wlan')) {
          console.log(`✅ Primary IP (use this): ${interface.address}`);
          return interface.address;
        }
      }
    }
  }
  
  console.log('\n💡 Usage Instructions:');
  console.log('1. Run: npm run dev:network');
  console.log('2. Access from other devices using: http://[IP_ADDRESS]:3000');
  console.log('3. Make sure your firewall allows connections on port 3000');
  
  return null;
}

// Run the function
const localIP = getLocalIPAddress();

if (localIP) {
  console.log(`\n🚀 Your app will be available at: http://${localIP}:3000`);
} else {
  console.log('\n❌ Could not determine primary local IP address');
  console.log('Try running: ipconfig (Windows) or ifconfig (Mac/Linux)');
}
