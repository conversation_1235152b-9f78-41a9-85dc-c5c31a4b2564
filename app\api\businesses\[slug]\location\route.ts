import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, upsertLocation } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const locationSchema = z.object({
  street_address: z.string().min(1, 'Street address is required').max(200, 'Street address must be less than 200 characters'),
  city: z.string().min(1, 'City is required').max(100, 'City must be less than 100 characters'),
  state: z.string().min(2, 'State is required').max(50, 'State must be less than 50 characters'),
  zip_code: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format').optional()
})

// Simple geocoding function using a free service
async function geocodeAddress(address: string): Promise<{ latitude?: number; longitude?: number }> {
  try {
    // Using Nominatim (OpenStreetMap) for free geocoding
    const encodedAddress = encodeURIComponent(address)
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=1`,
      {
        headers: {
          'User-Agent': 'PressureWash-Directory/1.0'
        }
      }
    )
    
    if (!response.ok) {
      console.warn('Geocoding service unavailable')
      return {}
    }
    
    const data = await response.json()
    
    if (data && data.length > 0) {
      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon)
      }
    }
    
    return {}
  } catch (error) {
    console.warn('Geocoding failed:', error)
    return {}
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug } = await params
    const body = await request.json()
    
    // Validate input
    const validation = locationSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    // First get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner (this will also be enforced by RLS)
    if (business.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }
    
    const { street_address, city, state, zip_code } = validation.data
    
    // Attempt to geocode the address if we have enough information
    let coordinates = {}
    if (street_address && city && state) {
      const fullAddress = `${street_address}, ${city}, ${state}${zip_code ? ' ' + zip_code : ''}`
      coordinates = await geocodeAddress(fullAddress)
    }
    
    const { location, error } = await upsertLocation(business.id, {
      street_address,
      city,
      state,
      zip_code,
      ...coordinates
    })
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ location })
  } catch (error) {
    console.error('Error updating location:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}