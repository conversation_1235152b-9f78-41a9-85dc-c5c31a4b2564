"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import type { Business, Review } from "@/lib/types"
import { Star, MessageSquare } from "lucide-react"

interface ReviewsTabProps {
  business: Business & { slug: string }
}

export function ReviewsTab({ business }: ReviewsTabProps) {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    averageRating: 0,
    totalReviews: 0,
    ratingBreakdown: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  })

  useEffect(() => {
    fetchReviews()
  }, [business.id])

  const fetchReviews = async () => {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .select(`
          *,
          profile:profiles(full_name)
        `)
        .eq("business_id", business.id)
        .order("created_at", { ascending: false })

      if (error) throw error

      const reviewsData = data || []
      setReviews(reviewsData)

      // Calculate stats
      if (reviewsData.length > 0) {
        const totalRating = reviewsData.reduce((sum, review) => sum + review.rating, 0)
        const averageRating = totalRating / reviewsData.length

        const ratingBreakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
        reviewsData.forEach((review) => {
          ratingBreakdown[review.rating as keyof typeof ratingBreakdown]++
        })

        setStats({
          averageRating,
          totalReviews: reviewsData.length,
          ratingBreakdown,
        })
      }
    } catch (error) {
      console.error("Error fetching reviews:", error)
    } finally {
      setLoading(false)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-current" : "text-neutral-600"}`} />
    ))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Star className="h-5 w-5" />
            Review Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats.totalReviews === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
              <p className="text-neutral-400">No reviews yet</p>
              <p className="text-neutral-500 text-sm">
                Reviews will appear here once customers start rating your service
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Average Rating */}
              <div className="text-center">
                <div className="text-4xl font-bold text-white mb-2">{stats.averageRating.toFixed(1)}</div>
                <div className="flex justify-center mb-2">{renderStars(Math.round(stats.averageRating))}</div>
                <p className="text-neutral-400">
                  Based on {stats.totalReviews} review{stats.totalReviews !== 1 ? "s" : ""}
                </p>
              </div>

              {/* Rating Breakdown */}
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-2">
                    <span className="text-white text-sm w-8">{rating} ★</span>
                    <div className="flex-1 bg-neutral-800 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${stats.totalReviews > 0 ? (stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown] / stats.totalReviews) * 100 : 0}%`,
                        }}
                      />
                    </div>
                    <span className="text-neutral-400 text-sm w-8">
                      {stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reviews List */}
      {reviews.length > 0 && (
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <CardTitle className="text-white">Customer Reviews</CardTitle>
            <CardDescription className="text-neutral-400">Recent reviews from your customers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="bg-neutral-800 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-white font-medium">{review.profile?.full_name || "Anonymous"}</span>
                        <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">{review.rating} ★</Badge>
                      </div>
                      <div className="flex mb-2">{renderStars(review.rating)}</div>
                    </div>
                    <span className="text-neutral-500 text-sm">{formatDate(review.created_at)}</span>
                  </div>

                  {review.content && <p className="text-neutral-300">{review.content}</p>}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
