"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Shield, Eye, Lock, FileText } from "lucide-react"

export function PrivacyContent() {
  const lastUpdated = "January 19, 2025"

  const sections = [
    {
      id: "introduction",
      title: "1. Introduction",
      content: `This Privacy Policy describes how PressureWash Pro ("we," "us," or "our") collects, uses, and shares your personal information when you use our website and services.

We are committed to protecting your privacy and ensuring the security of your personal information. This policy explains your privacy rights and how the law protects you.`,
    },
    {
      id: "information-collect",
      title: "2. Information We Collect",
      content: `We collect information you provide directly to us, such as when you:
• Create an account or business profile
• Contact us for support
• Subscribe to our newsletter
• Leave reviews or ratings
• Use our search and filtering features

This may include your name, email address, phone number, business information, and payment details for premium subscriptions.`,
    },
    {
      id: "automatic-collection",
      title: "3. Information Collected Automatically",
      content: `When you use our Platform, we automatically collect certain information, including:
• IP address and location data
• Browser type and version
• Device information
• Usage patterns and preferences
• Cookies and similar tracking technologies

This information helps us improve our services and provide a better user experience.`,
    },
    {
      id: "how-we-use",
      title: "4. How We Use Your Information",
      content: `We use your information to:
• Provide and maintain our services
• Process transactions and manage subscriptions
• Send you updates and marketing communications
• Respond to your inquiries and provide support
• Improve our Platform and develop new features
• Comply with legal obligations
• Prevent fraud and ensure security`,
    },
    {
      id: "information-sharing",
      title: "5. Information Sharing",
      content: `We do not sell your personal information. We may share your information in the following circumstances:
• With your consent
• To comply with legal obligations
• To protect our rights and prevent fraud
• With service providers who assist in our operations
• In connection with a business transfer or merger

Business listings and reviews are publicly visible as part of our directory service.`,
    },
    {
      id: "data-security",
      title: "6. Data Security",
      content: `We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

However, no method of transmission over the internet or electronic storage is 100% secure. While we strive to protect your information, we cannot guarantee absolute security.`,
    },
    {
      id: "your-rights",
      title: "7. Your Privacy Rights",
      content: `Depending on your location, you may have the following rights:
• Access to your personal information
• Correction of inaccurate information
• Deletion of your information
• Restriction of processing
• Data portability
• Objection to processing

To exercise these rights, please contact us using the information provided below.`,
    },
    {
      id: "cookies",
      title: "8. Cookies and Tracking",
      content: `We use cookies and similar technologies to:
• Remember your preferences
• Analyze website traffic
• Provide personalized content
• Enable social media features

You can control cookies through your browser settings, but disabling cookies may affect your experience on our Platform.`,
    },
    {
      id: "third-party",
      title: "9. Third-Party Services",
      content: `Our Platform may contain links to third-party websites or integrate with third-party services. We are not responsible for the privacy practices of these third parties.

We encourage you to read the privacy policies of any third-party services you use in connection with our Platform.`,
    },
    {
      id: "children-privacy",
      title: "10. Children's Privacy",
      content: `Our Platform is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.

If you are a parent or guardian and believe your child has provided us with personal information, please contact us immediately.`,
    },
    {
      id: "changes-policy",
      title: "11. Changes to This Policy",
      content: `We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last Updated" date.

We encourage you to review this Privacy Policy periodically for any changes.`,
    },
    {
      id: "contact-us",
      title: "12. Contact Us",
      content: `If you have any questions about this Privacy Policy or our privacy practices, please contact us at:

Email: <EMAIL>
Phone: (*************
Address: 123 Business Rd, Suite 100, Phoenix, AZ 85001

For general inquiries, please use our contact form <NAME_EMAIL>.`,
    },
  ]

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          Privacy <span className="bg-blue-gradient bg-clip-text text-white">Policy</span>
        </h1>
        <div className="flex items-center justify-center gap-2 mb-6">
          <Calendar className="h-4 w-4 text-neutral-400" />
          <span className="text-neutral-400">Last Updated: {lastUpdated}</span>
        </div>
        <p className="text-neutral-400 max-w-3xl mx-auto">
          Your privacy is important to us. This policy explains how we collect, use, and protect your personal
          information when you use PressureWash Pro.
        </p>
      </div>

      {/* Privacy Commitment */}
      <Card className="bg-gradient-to-r from-blue-500/10 to-green-500/10 border-blue-500/20 mb-8">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-white font-semibold mb-2">Our Privacy Commitment</h3>
              <p className="text-neutral-300 text-sm">
                We are committed to protecting your privacy and being transparent about how we handle your data. We
                never sell your personal information and only use it to provide and improve our services.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Summary */}
      <Card className="bg-neutral-900 border-neutral-800 mb-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Privacy at a Glance
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-medium mb-1">Secure</h3>
              <p className="text-neutral-400 text-sm">Your data is protected with industry-standard security</p>
            </div>
            <div className="text-center">
              <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-medium mb-1">Private</h3>
              <p className="text-neutral-400 text-sm">We never sell your personal information to third parties</p>
            </div>
            <div className="text-center">
              <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-white font-medium mb-1">Transparent</h3>
              <p className="text-neutral-400 text-sm">Clear information about what we collect and why</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table of Contents */}
      <Card className="bg-neutral-900 border-neutral-800 mb-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Table of Contents
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
              >
                {section.title}
              </a>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Privacy Policy Content */}
      <div className="max-w-4xl mx-auto space-y-8">
        {sections.map((section) => (
          <Card key={section.id} id={section.id} className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-4">{section.title}</h2>
              <div className="prose prose-invert max-w-none">
                {section.content.split("\n").map((paragraph, index) => {
                  if (paragraph.trim() === "") return null
                  if (paragraph.startsWith("•")) {
                    return (
                      <li key={index} className="text-neutral-300 ml-4 mb-2">
                        {paragraph.substring(1).trim()}
                      </li>
                    )
                  }
                  return (
                    <p key={index} className="text-neutral-300 mb-4 leading-relaxed">
                      {paragraph}
                    </p>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Footer Notice */}
      <Card className="bg-neutral-800 border-neutral-700 mt-12">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-3">
            <Shield className="h-5 w-5 text-blue-400" />
            <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Privacy Notice</Badge>
          </div>
          <p className="text-neutral-400 text-sm">
            This Privacy Policy is effective as of {lastUpdated}. For questions about your privacy or to exercise your
            rights, please{" "}
            <a href="/contact" className="text-blue-400 hover:text-blue-300 transition-colors">
              contact our privacy team
            </a>
            .
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
