"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { getServices } from "@/lib/database"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import type { Business, Service } from "@/lib/types"
import { Save, Wrench } from "lucide-react"

interface ServicesTabProps {
  business: Business & { slug: string }
}

export function ServicesTab({ business }: ServicesTabProps) {
  const { toast } = useToast()
  const [services, setServices] = useState<Service[]>([])
  const [selectedServices, setSelectedServices] = useState<number[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchServices()
    fetchBusinessServices()
  }, [business.id])

  const fetchServices = async () => {
    try {
      const { services, error } = await getServices()
      if (!error && services) {
        setServices(services)
      }
    } catch (error) {
      console.error("Error fetching services:", error)
    }
  }

  const fetchBusinessServices = async () => {
    try {
      const { data, error } = await supabase
        .from("business_services")
        .select("service_id")
        .eq("business_id", business.id)

      if (error) throw error
      setSelectedServices(data?.map((bs) => bs.service_id) || [])
    } catch (error) {
      console.error("Error fetching business services:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleServiceToggle = (serviceId: number) => {
    setSelectedServices((prev) =>
      prev.includes(serviceId) ? prev.filter((id) => id !== serviceId) : [...prev, serviceId],
    )
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Use the API endpoint to update services
      const response = await fetch(`/api/businesses/${business.slug}/services`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ serviceIds: selectedServices })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update services')
      }

      toast({
        title: "Services Updated!",
        description: "Your service offerings have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating services:", error)
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update services. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          Services Offered
        </CardTitle>
        <CardDescription className="text-neutral-400">
          Select the pressure washing services you offer to help customers find you.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <p className="text-neutral-400">Loading services...</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {services.map((service) => (
                <div key={service.id} className="flex items-center space-x-3 p-3 bg-neutral-800 rounded-lg">
                  <Checkbox
                    id={`service-${service.id}`}
                    checked={selectedServices.includes(service.id)}
                    onCheckedChange={() => handleServiceToggle(service.id)}
                    className="border-neutral-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <label htmlFor={`service-${service.id}`} className="text-white cursor-pointer flex-1">
                    {service.name}
                  </label>
                </div>
              ))}
            </div>

            <div className="pt-4 border-t border-neutral-800">
              <Button onClick={handleSave} disabled={saving} className="bg-blue-gradient-hover">
                <Save className="h-4 w-4 mr-2" />
                {saving ? "Saving..." : "Save Services"}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
