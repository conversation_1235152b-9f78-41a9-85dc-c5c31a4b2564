import { NextRequest, NextResponse } from 'next/server'
import { searchBusinesses } from '@/lib/database'
import { geocodeAddress } from '@/lib/geocoding'
import { z } from 'zod'

// Helper function to check if a string is a state name
function isStateName(str: string): boolean {
  const stateNames = [
    'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut',
    'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa',
    'kansas', 'kentucky', 'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan',
    'minnesota', 'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 'new hampshire',
    'new jersey', 'new mexico', 'new york', 'north carolina', 'north dakota', 'ohio',
    'oklahoma', 'oregon', 'pennsylvania', 'rhode island', 'south carolina', 'south dakota',
    'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 'west virginia',
    'wisconsin', 'wyoming'
  ]
  return stateNames.includes(str.toLowerCase())
}

const searchSchema = z.object({
  query: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  radius: z.number().min(1).max(100).default(25), // miles
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  serviceIds: z.array(z.number()).optional(),
  minRating: z.number().min(0).max(5).optional(),
  sortBy: z.enum(['distance', 'rating', 'reviews', 'name']).default('rating'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse and validate search parameters
    let params = {
      query: searchParams.get('query') || undefined,
      latitude: searchParams.get('latitude') ? parseFloat(searchParams.get('latitude')!) : undefined,
      longitude: searchParams.get('longitude') ? parseFloat(searchParams.get('longitude')!) : undefined,
      radius: searchParams.get('radius') ? parseInt(searchParams.get('radius')!) : 25,
      city: searchParams.get('city') || undefined,
      state: searchParams.get('state') || undefined,
      zipCode: searchParams.get('zipCode') || undefined,
      serviceIds: searchParams.get('serviceIds') ?
        searchParams.get('serviceIds')!.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) :
        undefined,
      minRating: searchParams.get('minRating') ? parseFloat(searchParams.get('minRating')!) : undefined,
      sortBy: (searchParams.get('sortBy') as 'distance' | 'rating' | 'reviews' | 'name') || 'rating',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0
    }

    // Enhanced location parameter processing
    const location = searchParams.get('location')
    if (location && !params.latitude && !params.longitude) {
      const locationTrimmed = location.trim()

      // Check if it's a ZIP code (5 digits)
      if (/^\d{5}$/.test(locationTrimmed)) {
        params.zipCode = locationTrimmed
        // Try to geocode ZIP code for map centering
        const geocoded = await geocodeAddress(locationTrimmed)
        if (geocoded) {
          params.latitude = geocoded.coordinates.lat
          params.longitude = geocoded.coordinates.lng
          if (geocoded.city) params.city = geocoded.city
          if (geocoded.state) params.state = geocoded.state
        }
      }
      // Check if it's a state abbreviation (2 letters)
      else if (/^[A-Za-z]{2}$/.test(locationTrimmed)) {
        params.state = locationTrimmed.toUpperCase()
        // Try to geocode state for map centering
        const geocoded = await geocodeAddress(locationTrimmed)
        if (geocoded) {
          params.latitude = geocoded.coordinates.lat
          params.longitude = geocoded.coordinates.lng
        }
      }
      // Check if it's a full state name
      else if (isStateName(locationTrimmed)) {
        params.state = locationTrimmed
        // Try to geocode state for map centering
        const geocoded = await geocodeAddress(locationTrimmed)
        if (geocoded) {
          params.latitude = geocoded.coordinates.lat
          params.longitude = geocoded.coordinates.lng
        }
      }
      // Otherwise, try to geocode as a full address/city
      else {
        const geocoded = await geocodeAddress(location)
        if (geocoded) {
          params.latitude = geocoded.coordinates.lat
          params.longitude = geocoded.coordinates.lng
          // Also set city/state if available
          if (geocoded.city) params.city = geocoded.city
          if (geocoded.state) params.state = geocoded.state
          if (geocoded.zipCode) params.zipCode = geocoded.zipCode
        } else {
          // If geocoding fails, try to parse as "City, State" format
          const parts = locationTrimmed.split(',').map(p => p.trim())
          if (parts.length >= 1) {
            params.city = parts[0] // Use first part as city name
            if (parts.length >= 2) {
              params.state = parts[1] // Use second part as state if available
            }
          }
        }
      }
    }

    const validation = searchSchema.safeParse(params)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Invalid search parameters', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    const searchOptions = validation.data

    // Debug logging for search improvements
    console.log('Search API - Enhanced search options:', {
      originalLocation: location,
      parsedOptions: {
        city: searchOptions.city,
        state: searchOptions.state,
        zipCode: searchOptions.zipCode,
        latitude: searchOptions.latitude,
        longitude: searchOptions.longitude
      }
    })

    const { businesses, total, error } = await searchBusinesses(searchOptions)
    
    if (error) {
      console.error('Search error:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    
    return NextResponse.json({ 
      businesses,
      total,
      pagination: {
        limit: searchOptions.limit,
        offset: searchOptions.offset,
        hasMore: (searchOptions.offset + searchOptions.limit) < total
      }
    })
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}