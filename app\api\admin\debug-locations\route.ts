import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

/**
 * Debug endpoint to check current locations data
 */

export async function GET() {
  if (!supabaseAdmin) {
    return NextResponse.json(
      { error: 'Database not configured' },
      { status: 500 }
    )
  }

  try {
    // Get all locations with their business info
    const { data: locations, error: locationsError } = await supabaseAdmin
      .from('locations')
      .select(`
        *,
        business:businesses(id, name, slug)
      `)

    if (locationsError) {
      console.error('Error fetching locations:', locationsError)
      return NextResponse.json(
        { error: 'Failed to fetch locations', details: locationsError },
        { status: 500 }
      )
    }

    // Get table schema info
    const { data: columns, error: schemaError } = await supabaseAdmin
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'locations')
      .eq('table_schema', 'public')

    if (schemaError) {
      console.error('Error fetching schema:', schemaError)
    }

    return NextResponse.json({
      locations: locations || [],
      schema: columns || [],
      stats: {
        totalLocations: locations?.length || 0,
        locationsWithCoordinates: locations?.filter(loc => loc.latitude && loc.longitude).length || 0,
        uniqueCities: locations ? [...new Set(locations.map(loc => loc.city).filter(Boolean))] : [],
        uniqueStates: locations ? [...new Set(locations.map(loc => loc.state).filter(Boolean))] : []
      }
    })

  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json(
      { error: 'Debug failed', details: error },
      { status: 500 }
    )
  }
}
