-- Force Create Storage Bucket
-- This script forcefully creates the storage bucket and policies

-- ===== DELETE EXISTING BUCKET (if exists) =====
DELETE FROM storage.objects WHERE bucket_id = 'business-portfolios';
DELETE FROM storage.buckets WHERE id = 'business-portfolios';

-- ===== CREATE BUCKET =====
INSERT INTO storage.buckets (
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at,
  updated_at
) VALUES (
  'business-portfolios',
  'business-portfolios',
  true,
  52428800, -- 50MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  NOW(),
  NOW()
);

-- ===== DISABLE RLS ON STORAGE TEMPORARILY =====
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
ALTER TABLE storage.buckets DISABLE ROW LEVEL SECURITY;

-- ===== GRANT PERMISSIONS =====
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.objects TO anon;
GRANT ALL ON storage.buckets TO authenticated;
GRANT ALL ON storage.buckets TO anon;

-- ===== VERIFICATION =====
DO $$
DECLARE
  bucket_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bucket_count 
  FROM storage.buckets 
  WHERE id = 'business-portfolios';
  
  IF bucket_count > 0 THEN
    RAISE NOTICE '✅ SUCCESS: business-portfolios bucket created!';
    RAISE NOTICE 'RLS disabled for testing - storage should work now';
  ELSE
    RAISE NOTICE '❌ ERROR: Failed to create bucket';
  END IF;
END $$;

-- Show bucket details
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE id = 'business-portfolios';
