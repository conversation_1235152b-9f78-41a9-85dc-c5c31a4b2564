"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Shield, AlertTriangle, FileText } from "lucide-react"

export function TermsContent() {
  const lastUpdated = "January 19, 2025"

  const sections = [
    {
      id: "introduction",
      title: "1. Introduction",
      content: `Welcome to PressureWash Pro, a directory platform connecting homeowners with professional pressure washing services. By accessing or using our website and services, you agree to be bound by these Terms of Service ("Terms"). Please read them carefully.

These Terms constitute a legally binding agreement between you and PressureWash Pro. If you do not agree to these Terms, you may not access or use our services.`,
    },
    {
      id: "definitions",
      title: "2. Definitions",
      content: `For the purposes of these Terms:
• "Platform" refers to the PressureWash Pro website and all related services
• "User" refers to any individual or entity using our Platform
• "Business" refers to pressure washing service providers listed on our Platform
• "Customer" refers to individuals seeking pressure washing services
• "Content" refers to all information, data, text, images, and other materials on our Platform`,
    },
    {
      id: "user-accounts",
      title: "3. User Accounts",
      content: `When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account.

You are responsible for safeguarding the password and for all activities that occur under your account. You agree not to disclose your password to any third party and to notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.

We reserve the right to refuse service, terminate accounts, or cancel orders at our sole discretion.`,
    },
    {
      id: "business-listings",
      title: "4. Business Listings and Content",
      content: `Businesses are responsible for the accuracy and completeness of their listings. By creating a business profile, you represent and warrant that:
• All information provided is accurate and up-to-date
• You have the legal right to operate your business
• You possess all necessary licenses and insurance
• You will maintain professional standards in your service delivery

We do not endorse any business listed on our Platform and are not responsible for the quality of services provided. We reserve the right to remove any listing that violates these Terms or our community guidelines.`,
    },
    {
      id: "reviews-ratings",
      title: "5. Reviews and Ratings",
      content: `Our Platform allows customers to leave reviews and ratings for businesses. By submitting a review, you agree that:
• Your review is based on genuine experience with the business
• You will not post false, misleading, or defamatory content
• You will not attempt to manipulate ratings or reviews
• You grant us the right to use your review content

We reserve the right to remove reviews that violate our guidelines or are suspected of being fraudulent.`,
    },
    {
      id: "payment-billing",
      title: "6. Payment and Billing",
      content: `Premium business subscriptions are billed monthly or annually as selected. All fees are non-refundable except as required by law or as specifically stated in our refund policy.

We use third-party payment processors and do not store your payment information. By providing payment information, you authorize us to charge the applicable fees to your chosen payment method.

Failure to pay fees may result in suspension or termination of premium features.`,
    },
    {
      id: "prohibited-uses",
      title: "7. Prohibited Uses",
      content: `You may not use our Platform for any unlawful purpose or to solicit others to perform unlawful acts. Prohibited activities include but are not limited to:
• Posting false or misleading information
• Attempting to gain unauthorized access to our systems
• Using automated tools to scrape or collect data
• Harassing or threatening other users
• Violating any applicable laws or regulations
• Impersonating another person or entity`,
    },
    {
      id: "intellectual-property",
      title: "8. Intellectual Property Rights",
      content: `The Platform and its original content, features, and functionality are and will remain the exclusive property of PressureWash Pro and its licensors. The Platform is protected by copyright, trademark, and other laws.

You may not reproduce, distribute, modify, create derivative works of, publicly display, publicly perform, republish, download, store, or transmit any of the material on our Platform without our prior written consent.`,
    },
    {
      id: "privacy-data",
      title: "9. Privacy and Data Protection",
      content: `Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our Platform. By using our services, you agree to the collection and use of information in accordance with our Privacy Policy.

We implement appropriate security measures to protect your personal information, but cannot guarantee absolute security of data transmitted over the internet.`,
    },
    {
      id: "limitation-liability",
      title: "10. Limitation of Liability",
      content: `In no event shall PressureWash Pro, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the Platform.

Our total liability to you for all claims arising from or relating to these Terms or your use of the Platform shall not exceed the amount you paid us in the twelve months preceding the claim.`,
    },
    {
      id: "indemnification",
      title: "11. Indemnification",
      content: `You agree to defend, indemnify, and hold harmless PressureWash Pro and its licensee and licensors, and their employees, contractors, agents, officers and directors, from and against any and all claims, damages, obligations, losses, liabilities, costs or debt, and expenses (including but not limited to attorney's fees).`,
    },
    {
      id: "termination",
      title: "12. Termination",
      content: `We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.

Upon termination, your right to use the Platform will cease immediately. If you wish to terminate your account, you may simply discontinue using the Platform or contact us to request account deletion.`,
    },
    {
      id: "governing-law",
      title: "13. Governing Law",
      content: `These Terms shall be interpreted and governed by the laws of the State of Arizona, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights.`,
    },
    {
      id: "changes-terms",
      title: "14. Changes to Terms",
      content: `We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.

By continuing to access or use our Platform after those revisions become effective, you agree to be bound by the revised terms.`,
    },
    {
      id: "contact-information",
      title: "15. Contact Information",
      content: `If you have any questions about these Terms of Service, please contact us at:

Email: <EMAIL>
Phone: (*************
Address: 123 Business Rd, Suite 100, Phoenix, AZ 85001

For general support inquiries, please use our contact form <NAME_EMAIL>.`,
    },
  ]

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          Terms of <span className="bg-blue-gradient bg-clip-text text-white">Service</span>
        </h1>
        <div className="flex items-center justify-center gap-2 mb-6">
          <Calendar className="h-4 w-4 text-neutral-400" />
          <span className="text-neutral-400">Last Updated: {lastUpdated}</span>
        </div>
        <p className="text-neutral-400 max-w-3xl mx-auto">
          Please read these Terms of Service carefully before using our platform. These terms govern your use of
          PressureWash Pro and constitute a legal agreement between you and us.
        </p>
      </div>

      {/* Important Notice */}
      <Card className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-yellow-500/20 mb-8">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-white font-semibold mb-2">Important Legal Agreement</h3>
              <p className="text-neutral-300 text-sm">
                By using PressureWash Pro, you agree to these terms. If you don't agree with any part of these terms,
                you may not use our service. These terms may be updated from time to time, and continued use constitutes
                acceptance of any changes.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table of Contents */}
      <Card className="bg-neutral-900 border-neutral-800 mb-8">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Table of Contents
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
              >
                {section.title}
              </a>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Terms Content */}
      <div className="max-w-4xl mx-auto space-y-8">
        {sections.map((section) => (
          <Card key={section.id} id={section.id} className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-4">{section.title}</h2>
              <div className="prose prose-invert max-w-none">
                {section.content.split("\n").map((paragraph, index) => {
                  if (paragraph.trim() === "") return null
                  if (paragraph.startsWith("•")) {
                    return (
                      <li key={index} className="text-neutral-300 ml-4 mb-2">
                        {paragraph.substring(1).trim()}
                      </li>
                    )
                  }
                  return (
                    <p key={index} className="text-neutral-300 mb-4 leading-relaxed">
                      {paragraph}
                    </p>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Footer Notice */}
      <Card className="bg-neutral-800 border-neutral-700 mt-12">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center gap-2 mb-3">
            <Shield className="h-5 w-5 text-blue-400" />
            <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Legal Notice</Badge>
          </div>
          <p className="text-neutral-400 text-sm">
            These Terms of Service are effective as of {lastUpdated}. For questions about these terms, please{" "}
            <a href="/contact" className="text-blue-400 hover:text-blue-300 transition-colors">
              contact our legal team
            </a>
            .
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
