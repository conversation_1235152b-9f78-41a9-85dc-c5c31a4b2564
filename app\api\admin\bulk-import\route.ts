import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import {
  processBusinessData,
  validateBusinessData,
  parseCSV,
  type ImportedBusiness
} from '@/lib/bulk-import-utils'
import { processBusinessImages, cleanupBusinessImages } from '@/lib/image-utils'
import { z } from 'zod'

// Validation schema for Google Places API data structure
const GooglePlacesBusinessSchema = z.object({
  name: z.string().min(1),
  address: z.string().min(1),
  rating: z.number().min(0).max(5),
  totalRatings: z.number().min(0),
  status: z.string().optional(),
  placeId: z.string().optional(),
  types: z.array(z.string()).optional(),
  latitude: z.number(),
  longitude: z.number(),
  plusCode: z.string().optional(),
  businessStatus: z.string().optional(),
  priceLevelText: z.string().optional(),
  phoneNumber: z.string().optional(),
  internationalPhoneNumber: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  googleMapsUrl: z.string().url().optional(),
  weekdayText: z.array(z.string()).optional(),
  photoUrls: z.array(z.string()).optional(),
  iconUrl: z.string().url().optional(),
  reviews: z.array(z.object({
    author_name: z.string(),
    author_url: z.string().optional(),
    language: z.string().optional(),
    profile_photo_url: z.string().optional(),
    rating: z.number().min(1).max(5),
    relative_time_description: z.string(),
    text: z.string(),
    time: z.number()
  })).optional(),
  recentReviewsText: z.string().optional(),
  averageRecentRating: z.number().min(0).max(5).optional()
})

type GooglePlacesBusiness = z.infer<typeof GooglePlacesBusinessSchema>

interface ImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    index: number
    business: string
    error: string
  }>
}

// Helper function to parse address
function parseAddress(address: string) {
  const parts = address.split(',').map(part => part.trim())
  
  if (parts.length >= 3) {
    const streetAddress = parts[0]
    const city = parts[1]
    const stateZip = parts[2].split(' ')
    const state = stateZip[0]
    const zipCode = stateZip[1]
    
    return {
      street_address: streetAddress,
      city,
      state,
      zip_code: zipCode
    }
  }
  
  return {
    street_address: address,
    city: '',
    state: '',
    zip_code: ''
  }
}

// Simplified import function using utilities

async function importBusiness(business: ImportedBusiness, ownerId: string): Promise<void> {
  if (!supabaseAdmin) {
    throw new Error('Supabase not configured')
  }

  // Process business data using utilities
  const processedData = processBusinessData(business, ownerId)

  // Insert business
  const { data: businessData, error: businessError } = await supabaseAdmin
    .from('businesses')
    .insert(processedData.business)
    .select()
    .single()

  if (businessError) {
    throw new Error(`Failed to insert business: ${businessError.message}`)
  }

  const businessId = businessData.id

  // Insert location
  const { error: locationError } = await supabaseAdmin
    .from('locations')
    .insert({
      business_id: businessId,
      ...processedData.location
    })

  if (locationError) {
    throw new Error(`Failed to insert location: ${locationError.message}`)
  }

  // Insert business services
  for (const serviceId of processedData.services) {
    await supabaseAdmin
      .from('business_services')
      .insert({
        business_id: businessId,
        service_id: serviceId
      })
  }

  // Insert Google reviews if available
  if (business.reviews && business.reviews.length > 0) {
    for (const review of business.reviews) {
      await supabaseAdmin
        .from('reviews')
        .insert({
          business_id: businessId,
          author_id: null, // No user profile for Google reviews
          author_name: review.author_name,
          rating: review.rating,
          title: `Review by ${review.author_name}`,
          content: review.text,
          review_source: 'google',
          google_review_id: (review as any).author_url || null,
          created_at: new Date(review.time * 1000).toISOString()
        })
    }
  }

  // Process and upload images if available
  if (business.photoUrls && business.photoUrls.length > 0) {
    try {
      console.log(`Processing ${business.photoUrls.length} images for business: ${business.name}`)
      const imageResult = await processBusinessImages(business.photoUrls, businessId)

      if (imageResult.success > 0) {
        console.log(`✅ Successfully uploaded ${imageResult.success} images for ${business.name}`)
      }

      if (imageResult.failed > 0) {
        console.warn(`⚠️ Failed to upload ${imageResult.failed} images for ${business.name}:`, imageResult.errors)
      }
    } catch (error) {
      // Don't fail the entire import if image processing fails
      console.error(`❌ Image processing failed for ${business.name}:`, error)
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 500 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const ownerId = formData.get('ownerId') as string

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!ownerId) {
      return NextResponse.json(
        { error: 'Owner ID required' },
        { status: 400 }
      )
    }

    const fileContent = await file.text()
    let businesses: any[]

    // Parse file based on type
    if (file.name.endsWith('.json')) {
      try {
        businesses = JSON.parse(fileContent)
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid JSON format' },
          { status: 400 }
        )
      }
    } else if (file.name.endsWith('.csv')) {
      try {
        businesses = parseCSV(fileContent)
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid CSV format' },
          { status: 400 }
        )
      }
    } else {
      return NextResponse.json(
        { error: 'Unsupported file format. Please use JSON or CSV.' },
        { status: 400 }
      )
    }

    if (!Array.isArray(businesses)) {
      return NextResponse.json(
        { error: 'File must contain an array of businesses' },
        { status: 400 }
      )
    }

    const result: ImportResult = {
      success: true,
      imported: 0,
      failed: 0,
      errors: []
    }

    // Process each business
    for (let i = 0; i < businesses.length; i++) {
      try {
        // Validate business data
        const validationErrors = validateBusinessData(businesses[i])
        if (validationErrors.length > 0) {
          throw new Error(validationErrors.join(', '))
        }

        // Try to validate with schema, but allow partial data
        let validatedBusiness: ImportedBusiness
        try {
          validatedBusiness = GooglePlacesBusinessSchema.parse(businesses[i])
        } catch {
          // If schema validation fails, use the business as-is if basic validation passed
          validatedBusiness = businesses[i] as ImportedBusiness
        }

        await importBusiness(validatedBusiness, ownerId)
        result.imported++
      } catch (error) {
        result.failed++
        result.errors.push({
          index: i,
          business: businesses[i]?.name || `Business ${i + 1}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    if (result.failed > 0) {
      result.success = false
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('Bulk import error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
