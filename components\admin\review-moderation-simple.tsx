"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { 
  Star, 
  Search, 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Trash2
} from "lucide-react"
import { cn } from "@/lib/utils"

export function ReviewModeration() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterReason, setFilterReason] = useState("all")
  const [reports, setReports] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchReports()
  }, [])

  const fetchReports = async () => {
    try {
      const response = await fetch('/api/admin/reviews/reports')
      if (response.ok) {
        const data = await response.json()
        setReports(data.reports || [])
      }
    } catch (error) {
      console.error('Error fetching review reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredReports = reports.filter((report) => {
    const matchesSearch = !searchTerm || 
      report.review?.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.review?.business?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.review?.profile?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.reporter?.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === "all" || report.status === filterStatus
    const matchesReason = filterReason === "all" || report.reason === filterReason

    return matchesSearch && matchesStatus && matchesReason
  })

  const handleReportAction = async (reportId: string, action: string, adminNotes?: string) => {
    try {
      const response = await fetch('/api/admin/reviews/reports', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportId,
          action,
          adminNotes
        })
      })

      if (response.ok) {
        await fetchReports()
      }
    } catch (error) {
      console.error('Error handling report action:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    const colors = {
      pending: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      resolved: 'bg-green-500/20 text-green-400 border-green-500/30',
      dismissed: 'bg-red-500/20 text-red-400 border-red-500/30',
      reviewed: 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }
    return (
      <Badge className={colors[status as keyof typeof colors] || colors.pending}>
        {status}
      </Badge>
    )
  }

  const getReportReasonBadge = (reason: string) => {
    const colors = {
      spam: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
      inappropriate: 'bg-red-500/20 text-red-400 border-red-500/30',
      fake: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      offensive: 'bg-red-600/20 text-red-300 border-red-600/30',
      other: 'bg-neutral-500/20 text-neutral-400 border-neutral-500/30'
    }
    return (
      <Badge className={colors[reason as keyof typeof colors] || colors.other}>
        {reason}
      </Badge>
    )
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={cn(
          "h-4 w-4",
          index < rating 
            ? "text-yellow-400 fill-current" 
            : "text-neutral-600"
        )}
      />
    ))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Review Moderation</h1>
          <p className="text-neutral-400">Moderate reviews and handle content reports</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
          <Input
            placeholder="Search reviews by content, business, or reviewer..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40"
          />
        </div>

        <div className="flex space-x-3">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-900 border-neutral-800">
              <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Status</SelectItem>
              <SelectItem value="pending" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Pending</SelectItem>
              <SelectItem value="reviewed" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Reviewed</SelectItem>
              <SelectItem value="resolved" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Resolved</SelectItem>
              <SelectItem value="dismissed" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Dismissed</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterReason} onValueChange={setFilterReason}>
            <SelectTrigger className="w-48 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
              <SelectValue placeholder="Report Reason" />
            </SelectTrigger>
            <SelectContent className="bg-neutral-900 border-neutral-800">
              <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Reasons</SelectItem>
              <SelectItem value="spam" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Spam</SelectItem>
              <SelectItem value="fake" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Fake Review</SelectItem>
              <SelectItem value="inappropriate" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Inappropriate Content</SelectItem>
              <SelectItem value="offensive" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Offensive Language</SelectItem>
              <SelectItem value="other" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reports Grid */}
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-neutral-400">Total Reports</p>
                  <p className="text-2xl font-bold text-white">{filteredReports.length}</p>
                </div>
                <Star className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-neutral-400">Pending</p>
                  <p className="text-2xl font-bold text-yellow-400">
                    {filteredReports.filter(r => r.status === 'pending').length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-neutral-400">Resolved</p>
                  <p className="text-2xl font-bold text-green-400">
                    {filteredReports.filter(r => r.status === 'resolved').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-neutral-400">Dismissed</p>
                  <p className="text-2xl font-bold text-red-400">
                    {filteredReports.filter(r => r.status === 'dismissed').length}
                  </p>
                </div>
                <Trash2 className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reports List */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              Review Reports ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="p-6 bg-neutral-800 rounded-xl animate-pulse">
                    <div className="h-4 bg-neutral-700 rounded mb-2"></div>
                    <div className="h-3 bg-neutral-700 rounded w-3/4"></div>
                  </div>
                ))}
              </div>
            ) : filteredReports.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-neutral-600 mx-auto mb-3" />
                <p className="text-neutral-400">No review reports found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <div key={report.id} className="p-6 bg-neutral-800 rounded-xl border border-neutral-700 hover:border-blue-500/20 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        {/* Header with rating and badges */}
                        <div className="flex items-center space-x-3 mb-3">
                          {report.review && renderStars(report.review.rating)}
                          {getStatusBadge(report.status)}
                          {getReportReasonBadge(report.reason)}
                        </div>

                        {/* Review content */}
                        {report.review?.content && (
                          <p className="text-neutral-300 mb-4 leading-relaxed">
                            "{report.review.content}"
                          </p>
                        )}

                        {/* Business and reviewer info */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-neutral-400">Business</p>
                            <p className="text-white font-medium">{report.review?.business?.name || 'Unknown'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-neutral-400">Reviewer</p>
                            <p className="text-white font-medium">{report.review?.profile?.full_name || 'Anonymous'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-neutral-400">Reported by</p>
                            <p className="text-white font-medium">{report.reporter?.full_name || 'Anonymous'}</p>
                          </div>
                          <div>
                            <p className="text-sm text-neutral-400">Report Date</p>
                            <p className="text-white font-medium">{new Date(report.created_at).toLocaleDateString()}</p>
                          </div>
                        </div>

                        {/* Report reason */}
                        <div className="mb-4">
                          <p className="text-sm text-neutral-400 mb-1">Report Reason</p>
                          <p className="text-neutral-300">{report.reason}</p>
                          {report.description && (
                            <p className="text-neutral-400 text-sm mt-1">{report.description}</p>
                          )}
                        </div>

                        {/* Action buttons */}
                        <div className="flex items-center space-x-3">
                          <Button
                            size="sm"
                            onClick={() => handleReportAction(report.id, 'approve_review')}
                            className="bg-green-500/10 text-green-400 border border-green-500/20 hover:bg-green-500/20 hover:text-green-300"
                            variant="outline"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve Review
                          </Button>

                          <Button
                            size="sm"
                            onClick={() => handleReportAction(report.id, 'remove_review')}
                            className="bg-red-500/10 text-red-400 border border-red-500/20 hover:bg-red-500/20 hover:text-red-300"
                            variant="outline"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remove Review
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
