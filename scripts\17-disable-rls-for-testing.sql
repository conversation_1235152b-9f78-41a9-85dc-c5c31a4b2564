-- Disable R<PERSON> for Testing
-- TEMPORARY: This disables all Row Level Security for testing purposes
-- DO NOT USE IN PRODUCTION

-- ===== DISABLE RLS ON ALL TABLES =====
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.businesses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.services DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_services DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolio_images DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.leads DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_activities DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_threads DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages DISABLE ROW LEVEL SECURITY;

-- ===== DISABLE RLS ON STORAGE =====
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
ALTER TABLE storage.buckets DISABLE ROW LEVEL SECURITY;

-- ===== GRANT ALL PERMISSIONS =====
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant to anon for testing
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Storage permissions
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.objects TO anon;
GRANT ALL ON storage.buckets TO authenticated;
GRANT ALL ON storage.buckets TO anon;

-- ===== VERIFICATION =====
DO $$
DECLARE
  rls_enabled_count INTEGER;
BEGIN
  -- Count tables with RLS still enabled
  SELECT COUNT(*) INTO rls_enabled_count
  FROM pg_tables 
  WHERE schemaname = 'public' 
    AND rowsecurity = true;
  
  RAISE NOTICE '=== RLS DISABLED FOR TESTING ===';
  RAISE NOTICE 'Tables with RLS still enabled: %', rls_enabled_count;
  RAISE NOTICE 'All permissions granted to authenticated and anon roles';
  RAISE NOTICE '⚠️  WARNING: This is for testing only - re-enable RLS for production!';
END $$;
