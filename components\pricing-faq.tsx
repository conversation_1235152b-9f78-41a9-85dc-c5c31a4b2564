"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronDown, ChevronUp } from "lucide-react"

export function PricingFAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: "Can I switch plans at any time?",
      answer:
        "Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing adjustments.",
    },
    {
      question: "What happens if I cancel my Premium subscription?",
      answer:
        "Your account will revert to the Basic plan at the end of your current billing period. You'll keep all your data, but lose Premium features like featured placement and advanced analytics.",
    },
    {
      question: "Do you offer refunds?",
      answer:
        "We offer a 30-day money-back guarantee for Premium subscriptions. If you're not satisfied within the first 30 days, we'll provide a full refund.",
    },
    {
      question: "How does the featured placement work?",
      answer:
        "Premium businesses appear at the top of search results and have a special 'Featured' badge. This increases visibility and typically results in 3x more customer inquiries.",
    },
    {
      question: "Can I try Premium features before upgrading?",
      answer:
        "We offer a 14-day free trial of Premium features for new businesses. You can experience all Premium benefits before committing to a paid plan.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards (Visa, MasterCard, American Express, Discover) and PayPal. All payments are processed securely through Stripe.",
    },
    {
      question: "Is there a setup fee?",
      answer:
        "No setup fees! You only pay the monthly or yearly subscription fee. We believe in transparent pricing with no hidden costs.",
    },
    {
      question: "Can I get help setting up my profile?",
      answer:
        "Premium customers get priority support, and we offer free profile optimization consultations to help you get the most out of the platform.",
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          <p className="text-neutral-400 max-w-2xl mx-auto">
            Got questions? We've got answers. If you can't find what you're looking for, feel free to contact us.
          </p>
        </div>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-neutral-800/50 transition-colors"
                >
                  <h3 className="text-white font-medium pr-4">{faq.question}</h3>
                  {openIndex === index ? (
                    <ChevronUp className="h-5 w-5 text-blue-400 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-neutral-400 flex-shrink-0" />
                  )}
                </button>
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <p className="text-neutral-400 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center mt-12">
          <p className="text-neutral-400 mb-4">Still have questions?</p>
          <a
            href="/contact"
            className="inline-flex items-center px-6 py-3 bg-blue-gradient-hover text-white font-medium rounded-lg"
          >
            Contact Support
          </a>
        </div>
      </div>
    </section>
  )
}
