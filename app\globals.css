@tailwind base;
@tailwind components;
@tailwind utilities;

@import './globals-performance.css';

:root {
  --background: 0 0% 0%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 217 91% 60%;
  --blue-gradient-start: 217 91% 70%;
  --blue-gradient-end: 217 91% 45%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Custom Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(0 0% 10%); /* Darker background for the track */
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, hsl(var(--blue-gradient-start) / 0.8), hsl(var(--blue-gradient-end) / 0.8));
  }
}

@layer utilities {
  .bg-blue-gradient {
    background: linear-gradient(135deg, hsl(var(--blue-gradient-start)), hsl(var(--blue-gradient-end)));
  }

  .bg-blue-gradient-hover {
    @apply bg-blue-gradient transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5;
    box-shadow: 0 4px 15px hsl(217 91% 60% / 0.3);
  }

  .glow-blue {
    box-shadow: 0 0 20px hsl(217 91% 60% / 0.3);
  }

  .glow-blue-strong {
    box-shadow: 0 0 30px hsl(217 91% 60% / 0.5);
  }

  .card-hover-blue {
    @apply transition-all duration-300 hover:border-blue-500/40;
    box-shadow: 0 8px 25px -5px hsl(217 91% 60% / 0.1);
  }
}
