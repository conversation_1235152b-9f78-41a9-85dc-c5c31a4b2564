import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

const resetPasswordSchema = z.object({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = resetPasswordSchema.parse(body)
    const { password } = validatedData
    
    const supabase = await createServerClient()
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }
    
    // Update user password
    const { data, error } = await supabase.auth.updateUser({
      password: password,
    })
    
    if (error) {
      let errorMessage = error.message
      if (error.message.includes('session_not_found') || error.message.includes('invalid_token')) {
        errorMessage = 'Password reset link has expired or is invalid. Please request a new one.'
      }
      return NextResponse.json({ error: errorMessage }, { status: 400 })
    }
    
    if (!data.user) {
      return NextResponse.json(
        { error: 'User not found or session expired' },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      user: data.user,
      message: 'Password updated successfully!'
    })
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Reset password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}