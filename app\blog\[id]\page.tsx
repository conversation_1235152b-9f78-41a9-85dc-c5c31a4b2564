import { Header } from "@/components/header"
import { BlogArticle } from "@/components/blog/blog-article"

interface BlogArticlePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function BlogArticlePage({ params }: BlogArticlePageProps) {
  const { id } = await params
  return (
    <div className="min-h-screen bg-black">
      <Header />
      <BlogArticle articleId={id} />
    </div>
  )
}
