-- Fix RLS Policies for Production
-- This script fixes authentication and RLS issues

-- ===== DROP EXISTING PROBLEMATIC POLICIES =====

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view all businesses" ON public.businesses;
DROP POLICY IF EXISTS "Business owners can manage their businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can view business memberships" ON public.business_members;
DROP POLICY IF EXISTS "Business owners can manage memberships" ON public.business_members;
DROP POLICY IF EXISTS "Users can view all business locations" ON public.locations;
DROP POLICY IF EXISTS "Business owners can manage their locations" ON public.locations;
DROP POLICY IF EXISTS "Users can view all business services" ON public.business_services;
DROP POLICY IF EXISTS "Business owners can manage their services" ON public.business_services;
DROP POLICY IF EXISTS "Users can view all portfolio images" ON public.portfolio_images;
DROP POLICY IF EXISTS "Business owners can manage their portfolio" ON public.portfolio_images;
DROP POLICY IF EXISTS "Users can view all reviews" ON public.reviews;
DROP POLICY IF EXISTS "Users can create reviews" ON public.reviews;
DROP POLICY IF EXISTS "Users can update their own reviews" ON public.reviews;
DROP POLICY IF EXISTS "Business owners can view their subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Business owners can manage their subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Business owners can manage their leads" ON public.leads;
DROP POLICY IF EXISTS "Business owners can manage their lead activities" ON public.lead_activities;
DROP POLICY IF EXISTS "Users can view their message threads" ON public.message_threads;
DROP POLICY IF EXISTS "Users can create message threads" ON public.message_threads;
DROP POLICY IF EXISTS "Business owners can manage their threads" ON public.message_threads;
DROP POLICY IF EXISTS "Users can view messages in their threads" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages in their threads" ON public.messages;

-- ===== CREATE SIMPLIFIED RLS POLICIES =====

-- Profiles policies
CREATE POLICY "Enable read access for all users" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on id" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Business policies (more permissive)
CREATE POLICY "Enable read access for all users" ON public.businesses
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.businesses
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Enable update for business owners" ON public.businesses
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Enable delete for business owners" ON public.businesses
  FOR DELETE USING (auth.uid() = owner_id);

-- Business services policies
CREATE POLICY "Enable read access for all users" ON public.business_services
  FOR SELECT USING (true);

CREATE POLICY "Enable all operations for business owners" ON public.business_services
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Portfolio images policies
CREATE POLICY "Enable read access for all users" ON public.portfolio_images
  FOR SELECT USING (true);

CREATE POLICY "Enable all operations for business owners" ON public.portfolio_images
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Reviews policies
CREATE POLICY "Enable read access for all users" ON public.reviews
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users" ON public.reviews
  FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Enable update for review authors" ON public.reviews
  FOR UPDATE USING (auth.uid() = author_id);

-- Subscriptions policies
CREATE POLICY "Enable all operations for business owners" ON public.subscriptions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Leads policies
CREATE POLICY "Enable all operations for business owners" ON public.leads
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Lead activities policies
CREATE POLICY "Enable all operations for business owners" ON public.lead_activities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.leads l
      JOIN public.businesses b ON l.business_id = b.id
      WHERE l.id = lead_id AND b.owner_id = auth.uid()
    )
  );

-- Locations policies
CREATE POLICY "Enable read access for all users" ON public.locations
  FOR SELECT USING (true);

CREATE POLICY "Enable all operations for business owners" ON public.locations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Business members policies
CREATE POLICY "Enable read for business owners and members" ON public.business_members
  FOR SELECT USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

CREATE POLICY "Enable all operations for business owners" ON public.business_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Message threads policies
CREATE POLICY "Enable read for thread participants" ON public.message_threads
  FOR SELECT USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

CREATE POLICY "Enable insert for authenticated users" ON public.message_threads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for thread participants" ON public.message_threads
  FOR UPDATE USING (
    auth.uid() = user_id OR 
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id AND owner_id = auth.uid()
    )
  );

-- Messages policies
CREATE POLICY "Enable read for thread participants" ON public.messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.message_threads mt
      WHERE mt.id = thread_id 
      AND (
        mt.user_id = auth.uid() OR 
        EXISTS (
          SELECT 1 FROM public.businesses 
          WHERE id = mt.business_id AND owner_id = auth.uid()
        )
      )
    )
  );

CREATE POLICY "Enable insert for thread participants" ON public.messages
  FOR INSERT WITH CHECK (
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM public.message_threads mt
      WHERE mt.id = thread_id 
      AND (
        mt.user_id = auth.uid() OR 
        EXISTS (
          SELECT 1 FROM public.businesses 
          WHERE id = mt.business_id AND owner_id = auth.uid()
        )
      )
    )
  );

-- ===== GRANT PERMISSIONS =====
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ===== VERIFICATION =====
DO $$
DECLARE
  policy_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies 
  WHERE schemaname = 'public';
  
  RAISE NOTICE '=== RLS POLICIES UPDATED ===';
  RAISE NOTICE 'Total policies created: %', policy_count;
  RAISE NOTICE 'RLS should now work with authenticated users';
END $$;
