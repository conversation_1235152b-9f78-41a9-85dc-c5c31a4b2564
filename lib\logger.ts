// Logging and monitoring utilities

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  context?: Record<string, any>
  error?: Error
  userId?: string
  sessionId?: string
  requestId?: string
  url?: string
  userAgent?: string
  ip?: string
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  bufferSize: number
  flushInterval: number
  enablePerformanceTracking: boolean
}

class Logger {
  private config: LoggerConfig
  private buffer: LogEntry[] = []
  private flushTimer: NodeJS.Timeout | null = null
  private performanceMarks: Map<string, number> = new Map()

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
      enableConsole: true,
      enableRemote: process.env.NODE_ENV === 'production',
      remoteEndpoint: '/api/logs',
      bufferSize: 100,
      flushInterval: 30000, // 30 seconds
      enablePerformanceTracking: true,
      ...config
    }

    // Start flush timer
    if (this.config.enableRemote) {
      this.startFlushTimer()
    }

    // Handle page unload to flush remaining logs
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush()
      })
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      error
    }

    // Add browser context if available
    if (typeof window !== 'undefined') {
      entry.url = window.location.href
      entry.userAgent = navigator.userAgent
    }

    // Add user context if available
    const userId = this.getUserId()
    if (userId) {
      entry.userId = userId
    }

    const sessionId = this.getSessionId()
    if (sessionId) {
      entry.sessionId = sessionId
    }

    return entry
  }

  private getUserId(): string | undefined {
    // Try to get user ID from various sources
    if (typeof window !== 'undefined') {
      // From localStorage
      const user = localStorage.getItem('user')
      if (user) {
        try {
          return JSON.parse(user).id
        } catch {}
      }

      // From cookies
      const userCookie = document.cookie
        .split('; ')
        .find(row => row.startsWith('userId='))
      if (userCookie) {
        return userCookie.split('=')[1]
      }
    }

    return undefined
  }

  private getSessionId(): string | undefined {
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('sessionId')
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        sessionStorage.setItem('sessionId', sessionId)
      }
      return sessionId
    }
    return undefined
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return

    const { level, message, context, error } = entry
    const timestamp = new Date(entry.timestamp).toLocaleTimeString()
    const prefix = `[${timestamp}] [${LogLevel[level]}]`

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, context, error)
        break
      case LogLevel.INFO:
        console.info(prefix, message, context, error)
        break
      case LogLevel.WARN:
        console.warn(prefix, message, context, error)
        break
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(prefix, message, context, error)
        break
    }
  }

  private addToBuffer(entry: LogEntry): void {
    this.buffer.push(entry)

    // Flush if buffer is full
    if (this.buffer.length >= this.config.bufferSize) {
      this.flush()
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  private async flush(): Promise<void> {
    if (!this.config.enableRemote || this.buffer.length === 0) return

    const logsToSend = [...this.buffer]
    this.buffer = []

    try {
      await fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ logs: logsToSend })
      })
    } catch (error) {
      // If remote logging fails, log to console
      console.error('Failed to send logs to remote endpoint:', error)
      
      // Put logs back in buffer for retry
      this.buffer.unshift(...logsToSend)
    }
  }

  public debug(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return

    const entry = this.createLogEntry(LogLevel.DEBUG, message, context)
    this.logToConsole(entry)
    this.addToBuffer(entry)
  }

  public info(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.INFO)) return

    const entry = this.createLogEntry(LogLevel.INFO, message, context)
    this.logToConsole(entry)
    this.addToBuffer(entry)
  }

  public warn(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.WARN)) return

    const entry = this.createLogEntry(LogLevel.WARN, message, context)
    this.logToConsole(entry)
    this.addToBuffer(entry)
  }

  public error(message: string, error?: Error, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.ERROR)) return

    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error)
    this.logToConsole(entry)
    this.addToBuffer(entry)
  }

  public critical(message: string, error?: Error, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.CRITICAL, message, context, error)
    this.logToConsole(entry)
    this.addToBuffer(entry)
    
    // Immediately flush critical errors
    this.flush()
  }

  // Performance tracking
  public startPerformanceTracking(name: string): void {
    if (!this.config.enablePerformanceTracking) return
    this.performanceMarks.set(name, performance.now())
  }

  public endPerformanceTracking(name: string, context?: Record<string, any>): void {
    if (!this.config.enablePerformanceTracking) return
    
    const startTime = this.performanceMarks.get(name)
    if (startTime) {
      const duration = performance.now() - startTime
      this.performanceMarks.delete(name)
      
      this.info(`Performance: ${name}`, {
        duration: Math.round(duration),
        ...context
      })
    }
  }

  // User action tracking
  public trackUserAction(action: string, context?: Record<string, any>): void {
    this.info(`User Action: ${action}`, {
      type: 'user_action',
      action,
      ...context
    })
  }

  // API call tracking
  public trackApiCall(
    method: string,
    url: string,
    status: number,
    duration: number,
    context?: Record<string, any>
  ): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO
    const message = `API ${method} ${url} - ${status} (${duration}ms)`
    
    const entry = this.createLogEntry(level, message, {
      type: 'api_call',
      method,
      url,
      status,
      duration,
      ...context
    })
    
    this.logToConsole(entry)
    this.addToBuffer(entry)
  }

  // Business event tracking
  public trackBusinessEvent(event: string, context?: Record<string, any>): void {
    this.info(`Business Event: ${event}`, {
      type: 'business_event',
      event,
      ...context
    })
  }

  // Error boundary integration
  public logReactError(error: Error, errorInfo: any): void {
    this.error('React Error Boundary', error, {
      type: 'react_error',
      componentStack: errorInfo.componentStack
    })
  }

  // Cleanup
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = null
    }
    this.flush()
  }
}

// Create singleton logger instance
export const logger = new Logger()

// Convenience functions
export const log = {
  debug: (message: string, context?: Record<string, any>) => logger.debug(message, context),
  info: (message: string, context?: Record<string, any>) => logger.info(message, context),
  warn: (message: string, context?: Record<string, any>) => logger.warn(message, context),
  error: (message: string, error?: Error, context?: Record<string, any>) => logger.error(message, error, context),
  critical: (message: string, error?: Error, context?: Record<string, any>) => logger.critical(message, error, context),
  
  // Performance tracking
  startTimer: (name: string) => logger.startPerformanceTracking(name),
  endTimer: (name: string, context?: Record<string, any>) => logger.endPerformanceTracking(name, context),
  
  // Event tracking
  trackAction: (action: string, context?: Record<string, any>) => logger.trackUserAction(action, context),
  trackApi: (method: string, url: string, status: number, duration: number, context?: Record<string, any>) => 
    logger.trackApiCall(method, url, status, duration, context),
  trackEvent: (event: string, context?: Record<string, any>) => logger.trackBusinessEvent(event, context),
  
  // React integration
  reactError: (error: Error, errorInfo: any) => logger.logReactError(error, errorInfo)
}

// API endpoint for receiving logs (server-side)
export async function handleLogRequest(request: Request): Promise<Response> {
  try {
    const { logs } = await request.json()
    
    // Process logs (save to database, send to monitoring service, etc.)
    for (const logEntry of logs) {
      // In production, you would save to database or send to monitoring service
      console.log('Received log:', logEntry)
    }
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error processing logs:', error)
    return new Response(JSON.stringify({ error: 'Failed to process logs' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}
