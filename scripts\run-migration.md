# Database Schema Migration Guide

## Problem
You're experiencing 404 errors when trying to access `leads` and `subscriptions` tables because:

1. **Schema Inconsistency**: Different migration scripts use different ID types (INTEGER vs UUID)
2. **Missing Tables**: The `leads` and `subscriptions` tables may not exist in your current database
3. **Type Mismatch**: Your application expects UUID IDs but the database might have INTEGER IDs

## Solution
Run the migration script `06-fix-schema-migration.sql` to fix these issues.

## How to Run the Migration

### Option 1: Supabase Dashboard (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy the contents of `scripts/06-fix-schema-migration.sql`
4. Paste it into the SQL Editor
5. Click **Run** to execute the migration

### Option 2: Command Line (if you have psql access)
```bash
# If you have direct database access
psql "your-database-connection-string" -f scripts/06-fix-schema-migration.sql
```

### Option 3: Supabase CLI
```bash
# If you have Supabase CLI installed
supabase db reset
# Then run your migration
supabase db push
```

## What This Migration Does

### 1. **Checks Existing Schema**
- Detects if tables exist and what ID types they use
- Safely handles both new installations and existing databases

### 2. **Creates/Migrates Tables**
- **businesses**: Main business profiles (UUID IDs)
- **business_members**: User-business relationships
- **locations**: Business location data
- **services**: Available service types
- **business_services**: Business-service relationships
- **portfolio_images**: Business portfolio images
- **reviews**: Customer reviews and ratings
- **subscriptions**: Premium subscription management
- **leads**: Lead tracking and management
- **lead_activities**: Lead interaction history
- **message_threads**: Customer-business messaging
- **messages**: Individual messages

### 3. **Sets Up Security**
- Enables Row Level Security (RLS) on all tables
- Creates appropriate policies for data access
- Ensures users can only access their own data

### 4. **Optimizes Performance**
- Creates indexes for common queries
- Sets up triggers for automatic data updates
- Maintains denormalized fields (rating counts, etc.)

### 5. **Adds Sample Data**
- Inserts common pressure washing services
- Provides foundation for business service selection

## After Running the Migration

### 1. **Verify Tables Exist**
Check in Supabase Dashboard > Table Editor that all tables are present:
- businesses
- leads
- subscriptions
- (and all other tables listed above)

### 2. **Test the Application**
- The 404 errors should be resolved
- Leads and subscriptions features should work
- Business dashboard should load properly

### 3. **Create Sample Data (Optional)**
You can create a test business to verify everything works:

```sql
-- Insert a test business (replace with your user ID)
INSERT INTO public.businesses (name, slug, description, owner_id)
VALUES (
  'Test Pressure Washing Co',
  'test-pressure-washing-co',
  'A test business for verification',
  'your-user-id-here'
);
```

## Troubleshooting

### If You Get Permission Errors
Make sure you're running the script as a database admin or with sufficient privileges.

### If Tables Already Exist
The script uses `CREATE TABLE IF NOT EXISTS` so it's safe to run multiple times.

### If You Have Existing Data
The script includes logic to handle existing data, but for production databases, always backup first.

## Next Steps

After the migration:
1. Test the leads functionality in your dashboard
2. Test the subscriptions functionality
3. Verify that business creation works properly
4. Check that all relationships between tables work correctly

The migration is designed to be idempotent (safe to run multiple times) and should resolve all the 404 errors you're experiencing.
