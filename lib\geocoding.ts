// Geocoding utilities for location-based search

export interface Coordinates {
  lat: number
  lng: number
}

export interface GeocodingResult {
  coordinates: Coordinates
  formattedAddress: string
  city?: string
  state?: string
  zipCode?: string
}

// Simple city coordinates lookup (in production, use a geocoding service like Google Maps API)
const CITY_COORDINATES: Record<string, Coordinates> = {
  // Arizona
  'Phoenix,AZ': { lat: 33.4484, lng: -112.0740 },
  'Scottsdale,AZ': { lat: 33.4942, lng: -111.9261 },
  'Tempe,AZ': { lat: 33.4255, lng: -111.9400 },
  'Mesa,AZ': { lat: 33.4152, lng: -111.8315 },
  'Chandler,AZ': { lat: 33.3062, lng: -111.8413 },
  'Glendale,AZ': { lat: 33.5387, lng: -112.1860 },
  'Tucson,AZ': { lat: 32.2226, lng: -110.9747 },
  
  // California
  'Los Angeles,CA': { lat: 34.0522, lng: -118.2437 },
  'San Diego,CA': { lat: 32.7157, lng: -117.1611 },
  'San Francisco,CA': { lat: 37.7749, lng: -122.4194 },
  'Sacramento,CA': { lat: 38.5816, lng: -121.4944 },
  'San Jose,CA': { lat: 37.3382, lng: -121.8863 },
  'Fresno,CA': { lat: 36.7378, lng: -119.7871 },
  
  // Texas
  'Austin,TX': { lat: 30.2672, lng: -97.7431 },
  'Dallas,TX': { lat: 32.7767, lng: -96.7970 },
  'Houston,TX': { lat: 29.7604, lng: -95.3698 },
  'San Antonio,TX': { lat: 29.4241, lng: -98.4936 },
  'Fort Worth,TX': { lat: 32.7555, lng: -97.3308 },
  
  // Florida
  'Miami,FL': { lat: 25.7617, lng: -80.1918 },
  'Orlando,FL': { lat: 28.5383, lng: -81.3792 },
  'Tampa,FL': { lat: 27.9506, lng: -82.4572 },
  'Jacksonville,FL': { lat: 30.3322, lng: -81.6557 },
  'Fort Lauderdale,FL': { lat: 26.1224, lng: -80.1373 },
  
  // Other major cities
  'Atlanta,GA': { lat: 33.7490, lng: -84.3880 },
  'Denver,CO': { lat: 39.7392, lng: -104.9903 },
  'Seattle,WA': { lat: 47.6062, lng: -122.3321 },
  'Portland,OR': { lat: 45.5152, lng: -122.6784 },
  'Las Vegas,NV': { lat: 36.1699, lng: -115.1398 },
  'Chicago,IL': { lat: 41.8781, lng: -87.6298 },
  'New York,NY': { lat: 40.7128, lng: -74.0060 },
  'Boston,MA': { lat: 42.3601, lng: -71.0589 },
  'Philadelphia,PA': { lat: 39.9526, lng: -75.1652 },
  'Nashville,TN': { lat: 36.1627, lng: -86.7816 },
  'Charlotte,NC': { lat: 35.2271, lng: -80.8431 },
  'Raleigh,NC': { lat: 35.7796, lng: -78.6382 }
}

// ZIP code to coordinates lookup (sample data - in production use a comprehensive database)
const ZIP_COORDINATES: Record<string, Coordinates> = {
  // Arizona ZIP codes
  '85001': { lat: 33.4484, lng: -112.0740 }, // Phoenix
  '85002': { lat: 33.4734, lng: -112.0596 }, // Phoenix
  '85003': { lat: 33.4734, lng: -112.0596 }, // Phoenix
  '85251': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
  '85281': { lat: 33.4255, lng: -111.9400 }, // Tempe
  '85201': { lat: 33.4152, lng: -111.8315 }, // Mesa
  '85224': { lat: 33.3062, lng: -111.8413 }, // Chandler
  
  // California ZIP codes
  '90210': { lat: 34.0901, lng: -118.4065 }, // Beverly Hills
  '90401': { lat: 34.0195, lng: -118.4912 }, // Santa Monica
  '92101': { lat: 32.7157, lng: -117.1611 }, // San Diego
  '94102': { lat: 37.7749, lng: -122.4194 }, // San Francisco
  
  // Texas ZIP codes
  '78701': { lat: 30.2672, lng: -97.7431 }, // Austin
  '75201': { lat: 32.7767, lng: -96.7970 }, // Dallas
  '77002': { lat: 29.7604, lng: -95.3698 }, // Houston
  
  // Florida ZIP codes
  '33101': { lat: 25.7617, lng: -80.1918 }, // Miami
  '32801': { lat: 28.5383, lng: -81.3792 }, // Orlando
  '33602': { lat: 27.9506, lng: -82.4572 }  // Tampa
}

/**
 * Get coordinates for a city and state combination
 */
export function getCityCoordinates(city?: string, state?: string): Coordinates | null {
  if (!city || !state) return null

  const key = `${city},${state}`
  return CITY_COORDINATES[key] || null
}

/**
 * Get coordinates for ZIP codes
 */
export function getZipCodeCoordinates(zipCode: string): Coordinates | null {
  const zipCoordinates: Record<string, Coordinates> = {
    // Arizona ZIP codes
    '85001': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85002': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85003': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85004': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85005': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85006': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85007': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85008': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85009': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85010': { lat: 33.4484, lng: -112.0740 }, // Phoenix
    '85251': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85252': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85253': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85254': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85255': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85256': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85257': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85258': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85259': { lat: 33.4942, lng: -111.9261 }, // Scottsdale
    '85260': { lat: 33.4942, lng: -111.9261 }, // Scottsdale

    // North Carolina ZIP codes
    '28201': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28202': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28203': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28204': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28205': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28206': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28207': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28208': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28209': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28210': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28211': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28212': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28213': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28214': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28215': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28216': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28217': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28218': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28219': { lat: 35.2271, lng: -80.8431 }, // Charlotte
    '28220': { lat: 35.2271, lng: -80.8431 }, // Charlotte
  }

  return zipCoordinates[zipCode] || null
}

/**
 * Get coordinates for states (center point)
 */
export function getStateCoordinates(state: string): Coordinates | null {
  const stateCoordinates: Record<string, Coordinates> = {
    // State abbreviations
    'AZ': { lat: 33.4484, lng: -112.0740 }, // Arizona (Phoenix)
    'NC': { lat: 35.2271, lng: -80.8431 }, // North Carolina (Charlotte)
    'CA': { lat: 36.7783, lng: -119.4179 }, // California
    'TX': { lat: 31.9686, lng: -99.9018 }, // Texas
    'FL': { lat: 27.7663, lng: -81.6868 }, // Florida
    'NY': { lat: 42.1657, lng: -74.9481 }, // New York

    // Full state names (lowercase)
    'arizona': { lat: 33.4484, lng: -112.0740 },
    'north carolina': { lat: 35.2271, lng: -80.8431 },
    'california': { lat: 36.7783, lng: -119.4179 },
    'texas': { lat: 31.9686, lng: -99.9018 },
    'florida': { lat: 27.7663, lng: -81.6868 },
    'new york': { lat: 42.1657, lng: -74.9481 },
  }

  return stateCoordinates[state.toUpperCase()] || stateCoordinates[state.toLowerCase()] || null
}

/**
 * Get coordinates for a ZIP code
 */
export function getZipCoordinates(zipCode: string): Coordinates | null {
  return ZIP_COORDINATES[zipCode] || null
}

/**
 * Calculate distance between two points using Haversine formula
 * Returns distance in miles
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

/**
 * Geocode an address string to coordinates
 * Uses Google Maps API when available, falls back to OpenStreetMap
 */
export async function geocodeAddress(address: string): Promise<GeocodingResult | null> {
  // Try Google Maps first if API key is available
  if (process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    try {
      const { geocodeAddressGoogle } = await import('./google-geocoding')
      const googleResult = await geocodeAddressGoogle(address)

      if (googleResult) {
        return {
          coordinates: googleResult.coordinates,
          formattedAddress: googleResult.formattedAddress,
          city: googleResult.addressComponents.city,
          state: googleResult.addressComponents.state,
          zipCode: googleResult.addressComponents.zipCode
        }
      }
    } catch (error) {
      console.warn('Google geocoding failed, falling back to OpenStreetMap:', error)
    }
  }

  // Fallback to existing OpenStreetMap geocoding
  const trimmedAddress = address.trim()

  // Check if it's a ZIP code first
  if (/^\d{5}$/.test(trimmedAddress)) {
    const coordinates = getZipCoordinates(trimmedAddress)
    if (coordinates) {
      return {
        coordinates,
        formattedAddress: trimmedAddress,
        zipCode: trimmedAddress
      }
    }
  }

  // Parse comma-separated format (City, State)
  const parts = trimmedAddress.split(',').map(part => part.trim())

  if (parts.length >= 2) {
    const city = parts[0]
    const stateOrZip = parts[1]

    // Check if second part is a ZIP code
    if (/^\d{5}$/.test(stateOrZip)) {
      const coordinates = getZipCoordinates(stateOrZip)
      if (coordinates) {
        return {
          coordinates,
          formattedAddress: trimmedAddress,
          zipCode: stateOrZip
        }
      }
    }

    // Check if it's city, state
    const coordinates = getCityCoordinates(city, stateOrZip)
    if (coordinates) {
      return {
        coordinates,
        formattedAddress: trimmedAddress,
        city,
        state: stateOrZip
      }
    }
  }

  // Handle single city name - try to find a match
  if (parts.length === 1) {
    const cityName = parts[0].toLowerCase()

    // Search through all city coordinates for partial matches
    for (const [cityStateKey, coords] of Object.entries(CITY_COORDINATES)) {
      const [city, state] = cityStateKey.split(',')

      // Check for exact city match (case insensitive)
      if (city.toLowerCase() === cityName) {
        return {
          coordinates: coords,
          formattedAddress: `${city}, ${state}`,
          city: city,
          state: state
        }
      }
    }

    // If no exact match, try partial matching
    for (const [cityStateKey, coords] of Object.entries(CITY_COORDINATES)) {
      const [city, state] = cityStateKey.split(',')

      // Check if the search term is contained in the city name
      if (city.toLowerCase().includes(cityName) || cityName.includes(city.toLowerCase())) {
        return {
          coordinates: coords,
          formattedAddress: `${city}, ${state}`,
          city: city,
          state: state
        }
      }
    }
  }

  return null
}

/**
 * Specific coordinates for known businesses (temporary solution)
 */
const BUSINESS_COORDINATES: Record<string, Coordinates> = {
  // Phoenix, AZ businesses
  '85012': { lat: 33.4734, lng: -112.0431 }, // BotStack, Search Fox
  '85014': { lat: 33.4734, lng: -112.0431 }, // GoPost.Me
  '85015': { lat: 33.5806, lng: -112.1374 }, // United Power Washing
  '85007': { lat: 33.4255, lng: -112.1040 }, // AZ Power Wash Pros
  '85029': { lat: 33.5387, lng: -112.2860 }, // Clean Surface Pressure Washing
  '85006': { lat: 33.4152, lng: -112.0315 }, // Black Feather Power Washing
  '85041': { lat: 33.3062, lng: -112.1413 }, // Pressure Wash AZ
  '85034': { lat: 33.4152, lng: -112.0315 }, // Pressure Systems Inc
  '85028': { lat: 33.6292, lng: -111.9679 }, // AZ Power Washing, Phoenix Power Washing
  '85045': { lat: 33.2528, lng: -112.0890 }, // Valley Pro Power Wash

  // Scottsdale, AZ
  '85254': { lat: 33.6292, lng: -111.8679 }, // Az Pressure Wash and Windows

  // Charlotte, NC businesses
  '28205': { lat: 35.2271, lng: -80.8431 }, // Clean Home Power Washing
  '28206': { lat: 35.2271, lng: -80.8431 }, // Power Wash Charlotte
  '28217': { lat: 35.1495, lng: -80.9342 }, // No Pressure Power Wash
  '28226': { lat: 35.1495, lng: -80.7342 }, // Pressure Washing Charlotte, True Clean
}

/**
 * Get business coordinates from location data
 */
export function getBusinessCoordinates(location?: {
  city?: string
  state?: string
  zip_code?: string
  latitude?: number
  longitude?: number
}): Coordinates | null {
  if (!location) return null

  // First priority: Use actual database coordinates if available
  if (location.latitude && location.longitude) {
    return {
      lat: location.latitude,
      lng: location.longitude
    }
  }

  // Second priority: Use specific business coordinates by ZIP code
  if (location.zip_code && BUSINESS_COORDINATES[location.zip_code]) {
    return BUSINESS_COORDINATES[location.zip_code]
  }

  // Third priority: Try ZIP code lookup (more precise)
  if (location.zip_code) {
    const zipCoords = getZipCoordinates(location.zip_code)
    if (zipCoords) return zipCoords
  }

  // Fourth priority: Fall back to city coordinates
  if (location.city && location.state) {
    return getCityCoordinates(location.city, location.state)
  }

  return null
}

/**
 * Format distance for display
 */
export function formatDistance(distance: number): string {
  if (distance < 0.1) {
    return '< 0.1 mi'
  } else if (distance < 1) {
    return `${distance.toFixed(1)} mi`
  } else if (distance < 10) {
    return `${distance.toFixed(1)} mi`
  } else {
    return `${Math.round(distance)} mi`
  }
}

/**
 * State name to abbreviation mapping
 */
const STATE_ABBREVIATIONS: Record<string, string> = {
  'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
  'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
  'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
  'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
  'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
  'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
  'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
  'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
  'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
  'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
}

/**
 * Abbreviation to state name mapping
 */
const ABBREVIATION_TO_STATE: Record<string, string> = Object.fromEntries(
  Object.entries(STATE_ABBREVIATIONS).map(([state, abbr]) => [abbr.toLowerCase(), state])
)

/**
 * Get state abbreviation from full name
 */
export function getStateAbbreviation(stateName: string): string | null {
  return STATE_ABBREVIATIONS[stateName.toLowerCase()] || null
}

/**
 * Get full state name from abbreviation
 */
export function getFullStateName(abbreviation: string): string | null {
  return ABBREVIATION_TO_STATE[abbreviation.toLowerCase()] || null
}

/**
 * Sort businesses by distance
 */
export function sortByDistance<T extends { distance?: number }>(
  items: T[], 
  ascending: boolean = true
): T[] {
  return items.sort((a, b) => {
    const distA = a.distance ?? Infinity
    const distB = b.distance ?? Infinity
    return ascending ? distA - distB : distB - distA
  })
}

/**
 * Filter businesses within radius
 */
export function filterByRadius<T extends { distance?: number }>(
  items: T[], 
  radius: number
): T[] {
  return items.filter(item => 
    item.distance === undefined || item.distance <= radius
  )
}