import { NextRequest, NextResponse } from 'next/server'
import { updateReview, deleteReview } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const updateReviewSchema = z.object({
  rating: z.number().min(1).max(5).optional(),
  content: z.string().max(2000).optional()
})

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ reviewId: string }> }
) {
  try {
    await requireAuth()
    const { reviewId } = await params
    const body = await request.json()
    
    // Validate input
    const validation = updateReviewSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }

    const { rating, content } = validation.data

    // At least one field must be provided
    if (rating === undefined && content === undefined) {
      return NextResponse.json({ 
        error: 'At least one field (rating or content) must be provided' 
      }, { status: 400 })
    }

    const { review, error } = await updateReview(reviewId, { rating, content })
    
    if (error) {
      if (error.message.includes('not found')) {
        return NextResponse.json({ error: error.message }, { status: 404 })
      }
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json({ error: error.message }, { status: 403 })
      }
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json({ review })
  } catch (error) {
    console.error('Error updating review:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ reviewId: string }> }
) {
  try {
    await requireAuth()
    const { reviewId } = await params
    
    const { success, error } = await deleteReview(reviewId)
    
    if (error) {
      if (error.message.includes('not found')) {
        return NextResponse.json({ error: error.message }, { status: 404 })
      }
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json({ error: error.message }, { status: 403 })
      }
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    if (!success) {
      return NextResponse.json({ error: 'Failed to delete review' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting review:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}
