"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface AdminLoadingProps {
  type?: 'dashboard' | 'table' | 'form' | 'messages' | 'bulk-import'
  title?: string
}

export function AdminLoading({ type = 'dashboard', title }: AdminLoadingProps) {
  if (type === 'dashboard') {
    return (
      <div className="space-y-6 loading-skeleton">
        {/* Header */}
        <div className="mb-6">
          <Skeleton className="h-8 w-48 bg-neutral-800 mb-2" />
          <Skeleton className="h-4 w-96 bg-neutral-800" />
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-neutral-900 border-neutral-800 metric-card">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24 bg-neutral-800" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 bg-neutral-800 mb-2" />
                <Skeleton className="h-3 w-20 bg-neutral-800" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart */}
        <Card className="bg-neutral-900 border-neutral-800 chart-container">
          <CardHeader>
            <Skeleton className="h-6 w-32 bg-neutral-800" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full bg-neutral-800" />
          </CardContent>
        </Card>

        {/* Table */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <Skeleton className="h-6 w-32 bg-neutral-800" />
          </CardHeader>
          <CardContent>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between py-3 border-b border-neutral-800 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <Skeleton className="h-8 w-8 rounded bg-neutral-800" />
                  <div>
                    <Skeleton className="h-4 w-48 bg-neutral-800 mb-1" />
                    <Skeleton className="h-3 w-32 bg-neutral-800" />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-6 w-16 bg-neutral-800" />
                  <Skeleton className="h-8 w-8 bg-neutral-800" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (type === 'table') {
    return (
      <div className="space-y-6 loading-skeleton">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Skeleton className="h-8 w-48 bg-neutral-800 mb-2" />
            <Skeleton className="h-4 w-96 bg-neutral-800" />
          </div>
          <Skeleton className="h-10 w-32 bg-neutral-800" />
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <Skeleton className="h-10 w-64 bg-neutral-800" />
          <Skeleton className="h-10 w-32 bg-neutral-800" />
          <Skeleton className="h-10 w-32 bg-neutral-800" />
        </div>

        {/* Table */}
        <Card className="bg-neutral-900 border-neutral-800 admin-table">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b border-neutral-800">
                  <tr>
                    {Array.from({ length: 5 }).map((_, i) => (
                      <th key={i} className="text-left p-4">
                        <Skeleton className="h-4 w-20 bg-neutral-800" />
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {Array.from({ length: 8 }).map((_, i) => (
                    <tr key={i} className="border-b border-neutral-800 admin-table-row">
                      {Array.from({ length: 5 }).map((_, j) => (
                        <td key={j} className="p-4 data-table-cell">
                          <Skeleton className="h-4 w-24 bg-neutral-800" />
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (type === 'messages') {
    return (
      <div className="space-y-6 loading-skeleton">
        {/* Header */}
        <div className="mb-6">
          <Skeleton className="h-8 w-48 bg-neutral-800 mb-2" />
          <Skeleton className="h-4 w-96 bg-neutral-800" />
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-neutral-900 border-neutral-800 metric-card">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24 bg-neutral-800" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 bg-neutral-800" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Messages Interface */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <Skeleton className="h-6 w-32 bg-neutral-800" />
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex gap-4 mb-6">
              <Skeleton className="h-10 flex-1 bg-neutral-800" />
              <Skeleton className="h-10 w-20 bg-neutral-800" />
              <Skeleton className="h-10 w-20 bg-neutral-800" />
            </div>

            {/* Message Threads */}
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="p-4 border border-neutral-800 rounded-lg message-thread">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Skeleton className="h-4 w-48 bg-neutral-800" />
                        <Skeleton className="h-5 w-16 bg-neutral-800" />
                      </div>
                      <div className="flex items-center gap-4 mb-2">
                        <Skeleton className="h-3 w-32 bg-neutral-800" />
                        <Skeleton className="h-3 w-32 bg-neutral-800" />
                      </div>
                      <div className="flex items-center gap-4">
                        <Skeleton className="h-3 w-20 bg-neutral-800" />
                        <Skeleton className="h-3 w-24 bg-neutral-800" />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-8 w-8 bg-neutral-800" />
                      <Skeleton className="h-8 w-8 bg-neutral-800" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (type === 'bulk-import') {
    return (
      <div className="space-y-6 loading-skeleton">
        {/* Header */}
        <div className="mb-8">
          <Skeleton className="h-8 w-48 bg-neutral-800 mb-2" />
          <Skeleton className="h-4 w-96 bg-neutral-800" />
        </div>

        {/* Import Form */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <Skeleton className="h-6 w-32 bg-neutral-800" />
          </CardHeader>
          <CardContent className="space-y-6">
            {/* File Upload */}
            <div>
              <Skeleton className="h-4 w-24 bg-neutral-800 mb-2" />
              <Skeleton className="h-32 w-full bg-neutral-800" />
            </div>

            {/* Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Skeleton className="h-4 w-24 bg-neutral-800 mb-2" />
                <Skeleton className="h-10 w-full bg-neutral-800" />
              </div>
              <div>
                <Skeleton className="h-4 w-24 bg-neutral-800 mb-2" />
                <Skeleton className="h-10 w-full bg-neutral-800" />
              </div>
            </div>

            {/* Progress */}
            <div>
              <Skeleton className="h-4 w-32 bg-neutral-800 mb-2" />
              <Skeleton className="h-2 w-full bg-neutral-800" />
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <Skeleton className="h-10 w-32 bg-neutral-800" />
              <Skeleton className="h-10 w-24 bg-neutral-800" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Default form loading
  return (
    <div className="space-y-6 loading-skeleton">
      {title && (
        <div className="mb-6">
          <Skeleton className="h-8 w-48 bg-neutral-800 mb-2" />
          <Skeleton className="h-4 w-96 bg-neutral-800" />
        </div>
      )}

      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <Skeleton className="h-6 w-32 bg-neutral-800" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i}>
              <Skeleton className="h-4 w-24 bg-neutral-800 mb-2" />
              <Skeleton className="h-10 w-full bg-neutral-800" />
            </div>
          ))}
          <div className="flex gap-4 pt-4">
            <Skeleton className="h-10 w-24 bg-neutral-800" />
            <Skeleton className="h-10 w-20 bg-neutral-800" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
