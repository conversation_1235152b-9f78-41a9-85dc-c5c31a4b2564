import { NextRequest, NextResponse } from 'next/server'
import { reportReview } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const reportReviewSchema = z.object({
  reason: z.enum(['spam', 'inappropriate', 'fake', 'offensive', 'other']),
  description: z.string().max(1000).optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ reviewId: string }> }
) {
  try {
    await requireAuth()
    const { reviewId } = await params
    const body = await request.json()
    
    // Validate input
    const validation = reportReviewSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }

    const { reason, description } = validation.data

    const { success, error } = await reportReview(reviewId, reason, description)
    
    if (error) {
      if (error.message.includes('not found')) {
        return NextResponse.json({ error: 'Review not found' }, { status: 404 })
      }
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    if (!success) {
      return NextResponse.json({ error: 'Failed to report review' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Review reported successfully. Our moderation team will review it shortly.' 
    })
  } catch (error) {
    console.error('Error reporting review:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}
