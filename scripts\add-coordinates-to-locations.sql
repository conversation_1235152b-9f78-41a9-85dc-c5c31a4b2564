-- Add latitude and longitude columns to locations table if they don't exist
-- This migration ensures the coordinates columns are available for map functionality

-- Add latitude column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'locations' 
        AND column_name = 'latitude'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.locations ADD COLUMN latitude DECIMAL(10, 8);
        COMMENT ON COLUMN public.locations.latitude IS 'Latitude coordinate for map display';
    END IF;
END $$;

-- Add longitude column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'locations' 
        AND column_name = 'longitude'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.locations ADD COLUMN longitude DECIMAL(11, 8);
        COMMENT ON COLUMN public.locations.longitude IS 'Longitude coordinate for map display';
    END IF;
END $$;

-- Create an index on coordinates for better performance
CREATE INDEX IF NOT EXISTS idx_locations_coordinates 
ON public.locations (latitude, longitude) 
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Update any existing businesses with coordinates based on their city/state
-- This is a one-time update for existing data
UPDATE public.locations 
SET 
    latitude = CASE 
        WHEN city = 'Phoenix' AND state = 'AZ' THEN 33.4484
        WHEN city = 'Scottsdale' AND state = 'AZ' THEN 33.4942
        WHEN city = 'Tempe' AND state = 'AZ' THEN 33.4255
        WHEN city = 'Mesa' AND state = 'AZ' THEN 33.4152
        WHEN city = 'Chandler' AND state = 'AZ' THEN 33.3062
        WHEN city = 'Glendale' AND state = 'AZ' THEN 33.5387
        WHEN city = 'Peoria' AND state = 'AZ' THEN 33.5806
        WHEN city = 'Gilbert' AND state = 'AZ' THEN 33.3528
        WHEN city = 'Surprise' AND state = 'AZ' THEN 33.6292
        WHEN city = 'Avondale' AND state = 'AZ' THEN 33.4356
        ELSE latitude
    END,
    longitude = CASE 
        WHEN city = 'Phoenix' AND state = 'AZ' THEN -112.0740
        WHEN city = 'Scottsdale' AND state = 'AZ' THEN -111.9261
        WHEN city = 'Tempe' AND state = 'AZ' THEN -111.9400
        WHEN city = 'Mesa' AND state = 'AZ' THEN -111.8315
        WHEN city = 'Chandler' AND state = 'AZ' THEN -111.8413
        WHEN city = 'Glendale' AND state = 'AZ' THEN -112.1860
        WHEN city = 'Peoria' AND state = 'AZ' THEN -112.2374
        WHEN city = 'Gilbert' AND state = 'AZ' THEN -111.7890
        WHEN city = 'Surprise' AND state = 'AZ' THEN -112.3679
        WHEN city = 'Avondale' AND state = 'AZ' THEN -112.3496
        ELSE longitude
    END
WHERE latitude IS NULL OR longitude IS NULL;
