import { Suspense } from "react"
import { AdminMessagingDashboard } from "@/components/admin/admin-messaging-dashboard"
import { AdminLoading } from "@/components/admin/admin-loading"

export default function AdminMessagesPage() {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Message Management</h1>
        <p className="text-neutral-400">
          Monitor and moderate conversations between customers and businesses
        </p>
      </div>

      <Suspense fallback={<AdminLoading type="messages" />}>
        <AdminMessagingDashboard />
      </Suspense>
    </div>
  )
}
