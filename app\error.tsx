"use client"

import { useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log error to monitoring service
    console.error('Application error:', error)
    
    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to monitoring service
      // logErrorToService(error)
    }
  }, [error])

  const getErrorMessage = (error: Error) => {
    // Provide user-friendly messages for common errors
    if (error.message.includes('ChunkLoadError')) {
      return 'Failed to load application resources. This usually happens after an update.'
    }
    
    if (error.message.includes('NetworkError')) {
      return 'Network connection error. Please check your internet connection.'
    }
    
    if (error.message.includes('timeout')) {
      return 'The request timed out. Please try again.'
    }
    
    if (error.message.includes('not found')) {
      return 'The requested resource could not be found.'
    }
    
    return 'An unexpected error occurred. Our team has been notified.'
  }

  const getErrorSuggestion = (error: Error) => {
    if (error.message.includes('ChunkLoadError')) {
      return 'Try refreshing the page or clearing your browser cache.'
    }
    
    if (error.message.includes('NetworkError')) {
      return 'Check your internet connection and try again.'
    }
    
    if (error.message.includes('timeout')) {
      return 'The server might be busy. Please wait a moment and try again.'
    }
    
    return 'If this problem persists, please contact our support team.'
  }

  return (
    <div className="min-h-screen bg-neutral-950 flex items-center justify-center p-4">
      <Card className="bg-neutral-900 border-neutral-800 max-w-2xl w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-16 w-16 text-red-400" />
          </div>
          <CardTitle className="text-white text-2xl mb-2">
            Something went wrong
          </CardTitle>
          <p className="text-neutral-400">
            {getErrorMessage(error)}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Error suggestion */}
          <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
            <h3 className="text-blue-400 font-semibold mb-2">What you can do:</h3>
            <p className="text-neutral-300 text-sm">
              {getErrorSuggestion(error)}
            </p>
          </div>

          {/* Error details for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
              <h3 className="text-red-400 font-semibold mb-2">Error Details (Development)</h3>
              <div className="text-sm text-neutral-300 space-y-2">
                <div>
                  <strong>Message:</strong> {error.message}
                </div>
                {error.digest && (
                  <div>
                    <strong>Digest:</strong> {error.digest}
                  </div>
                )}
                {error.stack && (
                  <div>
                    <strong>Stack:</strong>
                    <pre className="mt-1 text-xs bg-neutral-900 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={reset}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload Page
            </Button>
            <Button
              asChild
              variant="outline"
              className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
            >
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>
            </Button>
          </div>

          {/* Additional help */}
          <div className="text-center text-sm text-neutral-500">
            <p>
              Still having trouble?{' '}
              <Link 
                href="/contact" 
                className="text-blue-400 hover:text-blue-300 underline"
              >
                Contact our support team
              </Link>
              {' '}for assistance.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
