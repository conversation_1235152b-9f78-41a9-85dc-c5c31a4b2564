"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, MoreHorizontal, Mail, UserX, UserCheck, Eye, Users, Building2, MapPin, Calendar } from "lucide-react"

// Mock user data
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "customer",
    status: "active",
    location: "Phoenix, AZ",
    joinDate: "2024-06-15",
    lastActive: "2024-07-19",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "business",
    status: "active",
    location: "Scottsdale, AZ",
    joinDate: "2024-05-20",
    lastActive: "2024-07-18",
    businessName: "AZ Suds Power Washing",
  },
  {
    id: "3",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    type: "customer",
    status: "suspended",
    location: "Tempe, AZ",
    joinDate: "2024-07-01",
    lastActive: "2024-07-10",
  },
  {
    id: "4",
    name: "Lisa Chen",
    email: "<EMAIL>",
    type: "business",
    status: "active",
    location: "Mesa, AZ",
    joinDate: "2024-04-10",
    lastActive: "2024-07-19",
    businessName: "Aqua Clean Services",
  },
  {
    id: "5",
    name: "Robert Wilson",
    email: "<EMAIL>",
    type: "customer",
    status: "active",
    location: "Chandler, AZ",
    joinDate: "2024-06-28",
    lastActive: "2024-07-17",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Active</Badge>
    case "suspended":
      return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Suspended</Badge>
    case "pending":
      return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Pending</Badge>
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{status}</Badge>
  }
}

const getTypeBadge = (type: string) => {
  switch (type) {
    case "business":
      return (
        <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">
          <Building2 className="h-3 w-3 mr-1" />
          Business
        </Badge>
      )
    case "customer":
      return (
        <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">
          <Users className="h-3 w-3 mr-1" />
          Customer
        </Badge>
      )
    default:
      return <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">{type}</Badge>
  }
}

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")

  const filteredUsers = mockUsers.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === "all" || user.type === filterType
    const matchesStatus = filterStatus === "all" || user.status === filterStatus

    return matchesSearch && matchesType && matchesStatus
  })

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">User Management</h1>
          <p className="text-neutral-400">Manage platform users and their accounts</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
          <Input
            placeholder="Search users by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
          />
        </div>

        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-40 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
            <SelectValue placeholder="User Type" />
          </SelectTrigger>
          <SelectContent className="bg-neutral-900 border-neutral-800">
            <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Types</SelectItem>
            <SelectItem value="customer" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Customers</SelectItem>
            <SelectItem value="business" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Businesses</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-40 bg-neutral-900 border-blue-500/20 text-white hover:border-blue-500/40 transition-colors">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent className="bg-neutral-900 border-neutral-800">
            <SelectItem value="all" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">All Status</SelectItem>
            <SelectItem value="active" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Active</SelectItem>
            <SelectItem value="suspended" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Suspended</SelectItem>
            <SelectItem value="pending" className="text-neutral-300 hover:text-white hover:bg-blue-500/10">Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Users Table */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white">All Users ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-800">
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">User</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Location</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Join Date</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Last Active</th>
                  <th className="text-left py-3 px-4 font-medium text-neutral-400">Actions</th>
                </tr>
              </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b border-neutral-800 hover:bg-neutral-800/50">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium text-white">{user.name}</p>
                            <p className="text-sm text-neutral-400">{user.email}</p>
                            {user.businessName && <p className="text-xs text-blue-400">{user.businessName}</p>}
                          </div>
                        </td>
                        <td className="py-3 px-4">{getTypeBadge(user.type)}</td>
                        <td className="py-3 px-4">{getStatusBadge(user.status)}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center text-neutral-300">
                            <MapPin className="h-4 w-4 mr-1 text-neutral-400" />
                            {user.location}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center text-neutral-300">
                            <Calendar className="h-4 w-4 mr-1 text-neutral-400" />
                            {new Date(user.joinDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td className="py-3 px-4 text-neutral-300">{new Date(user.lastActive).toLocaleDateString()}</td>
                        <td className="py-3 px-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white hover:bg-blue-500/10">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-neutral-900 border-neutral-800">
                              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                                <Eye className="h-4 w-4 mr-2" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                                <Mail className="h-4 w-4 mr-2" />
                                Send Email
                              </DropdownMenuItem>
                              {user.status === "active" ? (
                                <DropdownMenuItem className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10">
                                  <UserX className="h-4 w-4 mr-2" />
                                  Suspend User
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem className="text-green-400 hover:text-green-300 hover:bg-green-500/10">
                                  <UserCheck className="h-4 w-4 mr-2" />
                                  Activate User
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
    </>
  )
}
