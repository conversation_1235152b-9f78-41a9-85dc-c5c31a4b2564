"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Droplets, Mail, ArrowLeft } from "lucide-react"

export function ForgotPasswordForm() {
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Frontend only - no actual password reset
    console.log("Password reset requested for:", email)
    setIsSubmitted(true)
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center px-4 py-12">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2">
            <div className="bg-blue-gradient p-3 rounded-lg glow-blue">
              <Droplets className="h-8 w-8 text-white" />
            </div>
            <span className="text-2xl font-bold text-white">PressureWash Pro</span>
          </Link>
        </div>

        {/* Form */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center text-white">
              {isSubmitted ? "Check Your Email" : "Reset Password"}
            </CardTitle>
            <CardDescription className="text-center text-neutral-400">
              {isSubmitted
                ? "We've sent password reset instructions to your email address."
                : "Enter your email address and we'll send you a link to reset your password."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSubmitted ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="bg-blue-gradient p-4 rounded-full w-16 h-16 mx-auto mb-4 glow-blue">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <p className="text-neutral-400 mb-4">
                    If an account with <strong className="text-white">{email}</strong> exists, you'll receive password
                    reset instructions shortly.
                  </p>
                  <p className="text-neutral-500 text-sm">
                    Didn't receive the email? Check your spam folder or try again.
                  </p>
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={() => setIsSubmitted(false)}
                    variant="outline"
                    className="w-full border-neutral-700 bg-neutral-800 text-neutral-300 hover:bg-neutral-700"
                  >
                    Try Different Email
                  </Button>
                  <Link href="/auth/login">
                    <Button variant="ghost" className="w-full text-blue-400 hover:text-blue-300">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Login
                    </Button>
                  </Link>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-white">
                    Email Address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
                      required
                    />
                  </div>
                </div>

                <Button type="submit" className="w-full bg-blue-gradient-hover">
                  Send Reset Link
                </Button>

                <div className="text-center">
                  <Link href="/auth/login" className="text-blue-400 hover:text-blue-300 text-sm">
                    <ArrowLeft className="h-4 w-4 mr-1 inline" />
                    Back to Login
                  </Link>
                </div>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-neutral-500 text-sm">
            Need help?{" "}
            <Link href="/contact" className="text-blue-400 hover:text-blue-300">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
