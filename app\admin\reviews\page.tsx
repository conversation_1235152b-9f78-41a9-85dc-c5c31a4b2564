import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { ReviewModeration } from "@/components/admin/review-moderation-simple"
import { AdminLoading } from "@/components/admin/admin-loading"

export const metadata: Metadata = {
  title: "Review Moderation - Admin Dashboard",
  description: "Moderate reviews, handle reports, and maintain content quality",
}

export default function ReviewModerationPage() {
  return (
    <Suspense fallback={<AdminLoading type="table" title="Review Moderation" />}>
      <ReviewModeration />
    </Suspense>
  )
}
