import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  const supabase = await createServerClient()

  if (!supabase) {
    return NextResponse.json(
      { error: 'Authentication service unavailable' },
      { status: 500 }
    )
  }

  try {
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      console.error('Logout error:', error)
      return NextResponse.json(
        { error: 'Failed to sign out' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Unexpected logout error:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const supabase = await createServerClient()

  try {
    if (supabase) {
      await supabase.auth.signOut()
    }
    
    // Redirect to home page after logout
    const requestUrl = new URL(request.url)
    return NextResponse.redirect(new URL('/', requestUrl.origin))
  } catch (error) {
    console.error('Logout error:', error)
    const requestUrl = new URL(request.url)
    return NextResponse.redirect(new URL('/', requestUrl.origin))
  }
}
