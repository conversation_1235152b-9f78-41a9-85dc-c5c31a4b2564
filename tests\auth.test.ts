import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { AuthService } from '../lib/auth-service'

describe('Authentication System', () => {
  let authService: AuthService
  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123',
    fullName: 'Test User'
  }

  beforeEach(() => {
    authService = new AuthService(false) // Client-side for testing
  })

  afterEach(async () => {
    // Clean up by signing out
    try {
      await authService.signOut()
    } catch (error) {
      // Ignore cleanup errors
    }
  })

  describe('User Registration', () => {
    it('should validate email format', async () => {
      const result = await authService.signUp({
        email: 'invalid-email',
        password: 'testpassword123',
        fullName: 'Test User'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('email')
    })

    it('should validate password length', async () => {
      const result = await authService.signUp({
        email: '<EMAIL>',
        password: '123',
        fullName: 'Test User'
      })

      expect(result.success).toBe(false)
      expect(result.error?.toLowerCase()).toContain('password')
    })

    it('should validate full name', async () => {
      const result = await authService.signUp({
        email: '<EMAIL>',
        password: 'testpassword123',
        fullName: 'A'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('name')
    })

    it('should create user account successfully', async () => {
      const result = await authService.signUp(testUser)

      expect(result.success).toBe(true)
      expect(result.user).toBeDefined()
      expect(result.message).toContain('successfully')
    })
  })

  describe('User Sign In', () => {
    it('should validate email format', async () => {
      const result = await authService.signIn({
        email: 'invalid-email',
        password: 'testpassword123'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('email')
    })

    it('should require password', async () => {
      const result = await authService.signIn({
        email: '<EMAIL>',
        password: ''
      })

      expect(result.success).toBe(false)
      expect(result.error?.toLowerCase()).toContain('password')
    })

    it('should handle invalid credentials', async () => {
      const result = await authService.signIn({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid')
    })
  })

  describe('Password Reset', () => {
    it('should validate email format for forgot password', async () => {
      const result = await authService.forgotPassword('invalid-email')

      expect(result.success).toBe(false)
      expect(result.error).toContain('email')
    })

    it('should send reset email for valid email', async () => {
      const result = await authService.forgotPassword('<EMAIL>')

      expect(result.success).toBe(true)
      expect(result.message).toContain('sent')
    })

    it('should validate password confirmation', async () => {
      const result = await authService.resetPassword({
        password: 'newpassword123',
        confirmPassword: 'differentpassword'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('match')
    })
  })

  describe('Session Management', () => {
    it('should get current user when authenticated', async () => {
      // First sign up
      await authService.signUp(testUser)
      
      const user = await authService.getCurrentUser()
      expect(user).toBeDefined()
      expect(user?.email).toBe(testUser.email)
    })

    it('should get current session when authenticated', async () => {
      // First sign up
      await authService.signUp(testUser)
      
      const session = await authService.getCurrentSession()
      expect(session).toBeDefined()
      expect(session?.user?.email).toBe(testUser.email)
    })

    it('should return null when not authenticated', async () => {
      const user = await authService.getCurrentUser()
      expect(user).toBeNull()
    })

    it('should refresh session successfully', async () => {
      // First sign up
      await authService.signUp(testUser)
      
      const refreshedSession = await authService.refreshSession()
      expect(refreshedSession).toBeDefined()
    })
  })

  describe('Sign Out', () => {
    it('should sign out successfully', async () => {
      // First sign up
      await authService.signUp(testUser)
      
      const result = await authService.signOut()
      expect(result.success).toBe(true)
      expect(result.message).toContain('successfully')
      
      // Verify user is signed out
      const user = await authService.getCurrentUser()
      expect(user).toBeNull()
    })
  })
})