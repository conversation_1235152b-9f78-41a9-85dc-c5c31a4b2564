/**
 * Test Database Seed Manager
 * Handles seeding and cleanup of test data
 */

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import { getTestEnvironment } from '../config/test-environments'

export class TestSeedManager {
  private supabase
  private environment

  constructor(envName?: string) {
    this.environment = getTestEnvironment(envName)
    this.supabase = createClient(
      this.environment.supabaseUrl,
      this.environment.supabaseServiceKey
    )
  }

  /**
   * Seed the database with test data
   */
  async seedDatabase(): Promise<void> {
    console.log(`🌱 Seeding test database for ${this.environment.name} environment...`)
    
    try {
      const seedSql = readFileSync(
        join(__dirname, 'test-seed.sql'),
        'utf-8'
      )

      // Execute the seed SQL
      const { error } = await this.supabase.rpc('exec_sql', { sql: seedSql })
      
      if (error) {
        throw new Error(`Failed to seed database: ${error.message}`)
      }

      console.log('✅ Database seeded successfully')
    } catch (error) {
      console.error('❌ Failed to seed database:', error)
      throw error
    }
  }

  /**
   * Clean up test data from the database
   */
  async cleanupDatabase(): Promise<void> {
    console.log(`🧹 Cleaning up test database for ${this.environment.name} environment...`)
    
    try {
      const cleanupSql = readFileSync(
        join(__dirname, 'test-cleanup.sql'),
        'utf-8'
      )

      // Execute the cleanup SQL
      const { error } = await this.supabase.rpc('exec_sql', { sql: cleanupSql })
      
      if (error) {
        throw new Error(`Failed to cleanup database: ${error.message}`)
      }

      console.log('✅ Database cleaned up successfully')
    } catch (error) {
      console.error('❌ Failed to cleanup database:', error)
      throw error
    }
  }

  /**
   * Reset database to clean state and re-seed
   */
  async resetDatabase(): Promise<void> {
    await this.cleanupDatabase()
    await this.seedDatabase()
  }

  /**
   * Verify that test data exists
   */
  async verifyTestData(): Promise<boolean> {
    try {
      // Check for test businesses
      const { data: businesses, error: businessError } = await this.supabase
        .from('businesses')
        .select('id, name')
        .like('name', 'Test%')

      if (businessError) {
        throw businessError
      }

      // Check for test profiles
      const { data: profiles, error: profileError } = await this.supabase
        .from('profiles')
        .select('id, email')
        .like('email', '%<EMAIL>')

      if (profileError) {
        throw profileError
      }

      const hasTestData = businesses && businesses.length > 0 && profiles && profiles.length > 0

      if (hasTestData) {
        console.log(`✅ Test data verified: ${businesses.length} businesses, ${profiles.length} profiles`)
      } else {
        console.log('⚠️ No test data found')
      }

      return hasTestData
    } catch (error) {
      console.error('❌ Failed to verify test data:', error)
      return false
    }
  }

  /**
   * Get test user credentials for authentication tests
   */
  getTestUsers() {
    return {
      homeowner: {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        name: 'Sarah Johnson',
        phone: '******-0101'
      },
      businessOwner: {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        name: 'Mike Thompson',
        phone: '******-0102'
      },
      admin: {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        name: 'Test Administrator',
        phone: '******-0103'
      }
    }
  }

  /**
   * Get test business data
   */
  getTestBusinesses() {
    return {
      powerClean: {
        id: '660e8400-e29b-41d4-a716-446655440001',
        name: 'Test Power Clean Pro',
        slug: 'test-power-clean-pro',
        ownerId: '550e8400-e29b-41d4-a716-446655440002'
      },
      sparkleWash: {
        id: '660e8400-e29b-41d4-a716-446655440002',
        name: 'Test Sparkle Wash',
        slug: 'test-sparkle-wash',
        ownerId: '550e8400-e29b-41d4-a716-446655440004'
      },
      elitePressure: {
        id: '660e8400-e29b-41d4-a716-446655440003',
        name: 'Test Elite Pressure Services',
        slug: 'test-elite-pressure-services',
        ownerId: '550e8400-e29b-41d4-a716-446655440005'
      }
    }
  }
}