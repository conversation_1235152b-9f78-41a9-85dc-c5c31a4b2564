import { createServerClient } from './supabase'
import { redirect } from 'next/navigation'
import { NextRequest, NextResponse } from 'next/server'
import type { Profile } from './types'
import type { User } from '@supabase/supabase-js'

/**
 * Get current user without throwing errors
 */
export async function getUser(): Promise<User | null> {
  const supabase = await createServerClient()

  if (!supabase) {
    return null
  }

  const { data: { user }, error } = await supabase.auth.getUser()

  if (error) {
    console.error('Error getting user:', error)
    return null
  }

  return user
}

/**
 * Get current session
 */
export async function getSession() {
  const supabase = await createServerClient()

  if (!supabase) {
    return null
  }

  const { data: { session }, error } = await supabase.auth.getSession()

  if (error) {
    console.error('Error getting session:', error)
    return null
  }

  return session
}

/**
 * Server-side authentication utility for API routes and server components
 * Checks for valid user session and returns user data
 */
export async function requireAuth(): Promise<User> {
  const supabase = await createServerClient()

  if (!supabase) {
    throw new Error('Supabase client not available')
  }

  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    throw new Error('Authentication required')
  }

  return user
}

export async function getUserProfile(userId?: string): Promise<Profile | null> {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  let targetUserId = userId
  if (!targetUserId) {
    const user = await getUser()
    if (!user) return null
    targetUserId = user.id
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', targetUserId)
    .single()
  
  if (error) {
    console.error('Error getting user profile:', error)
    return null
  }
  
  return data
}

/**
 * Server-side authentication check for pages
 * Redirects to login if not authenticated
 */
export async function requireAuthPage(): Promise<User> {
  try {
    return await requireAuth()
  } catch (error) {
    redirect('/auth/login')
  }
}

/**
 * API route authentication middleware
 * Returns user data or error response
 */
export async function authenticateApiRoute(request: NextRequest): Promise<{ user: User } | NextResponse> {
  try {
    const user = await requireAuth()
    return { user }
  } catch (error) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }
}

/**
 * Check if user is authenticated without throwing
 * Returns user data or null
 */
export async function getOptionalAuth(): Promise<User | null> {
  try {
    return await requireAuth()
  } catch (error) {
    return null
  }
}

/**
 * Create or update user profile
 */
export async function upsertUserProfile(user: User) {
  const supabase = await createServerClient()

  if (!supabase) {
    throw new Error('Supabase client not available')
  }

  const profileData = {
    id: user.id,
    email: user.email,
    full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
    updated_at: new Date().toISOString()
  }

  const { error } = await supabase
    .from('profiles')
    .upsert(profileData, { onConflict: 'id' })

  if (error) {
    console.error('Error upserting user profile:', error)
    throw error
  }

  return profileData
}

/**
 * Sign out user and redirect
 */
export async function signOut() {
  const supabase = await createServerClient()
  if (supabase) {
    await supabase.auth.signOut()
  }
  redirect('/')
}

/**
 * Auth error types for better error handling
 */
export type AuthError = {
  message: string
  code?: string
}

/**
 * Handle authentication errors with user-friendly messages
 */
export function getAuthErrorMessage(error: any): string {
  if (!error) return 'An unknown error occurred'

  const message = error.message || error.error_description || error.toString()

  // Common Supabase auth error messages
  if (message.includes('Invalid login credentials')) {
    return 'Invalid email or password. Please try again.'
  }

  if (message.includes('Email not confirmed')) {
    return 'Please check your email and click the confirmation link.'
  }

  if (message.includes('User already registered')) {
    return 'An account with this email already exists. Please sign in instead.'
  }

  if (message.includes('Password should be at least')) {
    return 'Password must be at least 6 characters long.'
  }

  if (message.includes('Unable to validate email address')) {
    return 'Please enter a valid email address.'
  }

  if (message.includes('Signup is disabled')) {
    return 'Account registration is currently disabled.'
  }

  if (message.includes('Email rate limit exceeded')) {
    return 'Too many emails sent. Please wait before requesting another.'
  }

  // Return original message if no specific handling
  return message
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { isValid: boolean; message?: string } {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' }
  }

  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters' }
  }

  return { isValid: true }
}

/**
 * Generate secure redirect URL for auth callbacks
 */
export function getAuthRedirectUrl(path: string = '/dashboard'): string {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  return `${baseUrl}${path}`
}

export async function createUserProfile(userId: string, profileData: Partial<Profile>) {
  const supabase = await createServerClient()
  
  if (!supabase) {
    throw new Error('Database connection not available')
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .insert({
      id: userId,
      ...profileData,
    })
    .select()
    .single()
  
  if (error) {
    console.error('Error creating user profile:', error)
    throw error
  }
  
  return data
}

export async function updateUserProfile(userId: string, profileData: Partial<Profile>) {
  const supabase = await createServerClient()
  
  if (!supabase) {
    throw new Error('Database connection not available')
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .update({
      ...profileData,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)
    .select()
    .single()
  
  if (error) {
    console.error('Error updating user profile:', error)
    throw error
  }
  
  return data
}



export async function refreshSession() {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  const { data, error } = await supabase.auth.refreshSession()
  
  if (error) {
    console.error('Error refreshing session:', error)
    return null
  }
  
  return data.session
}