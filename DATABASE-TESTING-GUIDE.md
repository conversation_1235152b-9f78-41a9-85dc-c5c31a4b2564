# Database Testing Guide (No Auth Mode)

## 🚫 Authentication Temporarily Disabled

I've temporarily disabled authentication across the application to focus on testing database functionality. Here's what was changed:

### Changes Made:

1. **Middleware Disabled** - All route protection bypassed
2. **Mock User Provider** - Returns fake user data for testing
3. **Mock Auth Functions** - `requireAuth()` returns test user
4. **R<PERSON> Disabled** - Row Level Security turned off for testing
5. **Test Data Created** - Sample businesses, leads, and subscriptions

## 🗄️ Database Setup

### Step 1: Run the No-Auth Migration Script

1. **Go to your Supabase Dashboard** → **SQL Editor**
2. **Copy and paste** the contents of `scripts/09-create-tables-no-auth.sql`
3. **Run the script** - this will:
   - Create missing tables (leads, subscriptions, etc.)
   - Disable Row Level Security temporarily
   - Insert test data with the business ID from your error logs
   - Grant permissions for testing

### Step 2: Verify Tables Were Created

The script should create these tables:
- ✅ `subscriptions` 
- ✅ `leads`
- ✅ `lead_activities`
- ✅ Test business with ID: `530f712e-abdf-431f-ac55-b16666f696e9`
- ✅ Test leads and subscription data

## 🧪 Testing the Database

### Method 1: Use the Test Page

1. **Start your development server**:
   ```bash
   npm run dev:network
   ```

2. **Navigate to the test page**:
   ```
   http://localhost:3000/test-db
   ```

3. **Click "Run Database Tests"** to check all tables

4. **Click "Create Test Data"** to test write operations

### Method 2: Test the Dashboard

1. **Navigate to the dashboard**:
   ```
   http://localhost:3000/dashboard
   ```

2. **Check the leads and subscriptions tabs** - they should now load without 404 errors

### Method 3: Test API Endpoints Directly

You can test the API endpoints directly:

```bash
# Test leads endpoint
curl http://localhost:3000/api/leads

# Test subscriptions endpoint  
curl http://localhost:3000/api/subscriptions

# Test businesses endpoint
curl http://localhost:3000/api/businesses
```

## 🔍 What to Look For

### ✅ Success Indicators:
- No more 404 errors for leads/subscriptions
- Dashboard loads completely
- Test page shows green "success" badges
- You can see test data in the tables

### ❌ Failure Indicators:
- Still getting 404 errors
- "Table does not exist" errors
- Permission denied errors
- Empty results when test data should exist

## 🐛 Troubleshooting

### If You Still Get 404 Errors:
1. Check that the migration script ran successfully
2. Verify tables exist in Supabase Dashboard → Table Editor
3. Check browser console for detailed error messages

### If You Get Permission Errors:
1. Ensure RLS is disabled (the script does this)
2. Check that `anon` role has permissions
3. Verify Supabase environment variables are set

### If Tables Are Empty:
1. Check that test data was inserted
2. Look for constraint violations in Supabase logs
3. Verify the business ID matches what's in your error logs

## 📊 Expected Test Results

After running the migration, you should see:

### In Supabase Dashboard:
- `businesses` table with test business
- `subscriptions` table with 1 test subscription
- `leads` table with 3 test leads
- `profiles` table with test user

### In Your Application:
- Dashboard loads without errors
- Leads tab shows test leads
- Subscriptions tab shows test subscription
- No authentication prompts

## 🔄 Re-enabling Authentication Later

When you're ready to re-enable authentication:

1. **Revert middleware changes** in `middleware.ts`
2. **Revert auth function changes** in `lib/auth.ts`
3. **Revert user provider changes** in `hooks/use-user.tsx`
4. **Re-enable RLS** by running:
   ```sql
   ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.lead_activities ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
   ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
   ```

## 🎯 Testing Checklist

- [ ] Migration script ran without errors
- [ ] Tables exist in Supabase Dashboard
- [ ] Test page shows all green results
- [ ] Dashboard loads without 404 errors
- [ ] Can view leads in dashboard
- [ ] Can view subscription in dashboard
- [ ] Can create new test data
- [ ] API endpoints respond correctly

Once all these are working, we know the database structure is correct and can focus on re-enabling authentication properly!
