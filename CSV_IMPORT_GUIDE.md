# CSV Import Guide for Pressure Washing Directory

## 📋 CSV Format Specification

The bulk import system supports CSV files with **tab-separated values** using the exact format exported from Google Places API tools.

### Required Headers (Tab-Separated)

```
Name	Address	Phone Number	International Phone	Website	Google Maps URL	Rating	Total Ratings	Status	Business Status	Price Level	Price Level Text	Latitude	Longitude	Plus Code	Place ID	Types	Hours	Photo URLs	Icon URL	Recent Reviews Count	Average Recent Rating	Recent Reviews Text
```

### Field Descriptions

| Field | Type | Required | Description | Example |
|-------|------|----------|-------------|---------|
| **Name** | String | ✅ Yes | Business name | `Clean Home Power Washing` |
| **Address** | String | ✅ Yes | Full street address | `3813 Miriam Dr, Charlotte, NC 28205, United States` |
| **Phone Number** | String | ❌ No | Local phone format | `(*************` |
| **International Phone** | String | ❌ No | International format | `******-214-1485` |
| **Website** | String | ❌ No | Business website URL | `https://www.cleanhomepowerwashing.com/` |
| **Google Maps URL** | String | ❌ No | Google Maps link | `https://maps.google.com/?cid=4673589347672508557` |
| **Rating** | Number | ✅ Yes | Average rating (0-5) | `5` |
| **Total Ratings** | Number | ✅ Yes | Number of reviews | `647` |
| **Status** | String | ❌ No | Current status | `Open`, `Closed` |
| **Business Status** | String | ❌ No | Operational status | `OPERATIONAL`, `CLOSED_TEMPORARILY` |
| **Price Level** | String | ❌ No | Price level number | `2` |
| **Price Level Text** | String | ❌ No | Price description | `Moderate`, `Unknown` |
| **Latitude** | Number | ✅ Yes | GPS latitude | `35.1980212` |
| **Longitude** | Number | ✅ Yes | GPS longitude | `-80.7962048` |
| **Plus Code** | String | ❌ No | Google Plus Code | `867X56X3+6G` |
| **Place ID** | String | ❌ No | Google Place ID | `ChIJNYAitQC9VogRjcgR_r_s20A` |
| **Types** | String | ❌ No | Business types (comma-separated) | `point_of_interest, establishment` |
| **Hours** | String | ❌ No | Business hours (semicolon-separated) | `Monday: 8:00 AM – 5:00 PM; Tuesday: 8:00 AM – 5:00 PM` |
| **Photo URLs** | String | ❌ No | Photo URLs (comma-separated) | `https://example.com/photo1.jpg, https://example.com/photo2.jpg` |
| **Icon URL** | String | ❌ No | Business icon URL | `https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png` |
| **Recent Reviews Count** | Number | ❌ No | Number of recent reviews | `5` |
| **Average Recent Rating** | Number | ❌ No | Average of recent reviews | `4.8` |
| **Recent Reviews Text** | String | ❌ No | Formatted review text | See format below |

### Recent Reviews Text Format

The reviews should be formatted as follows, with double line breaks between reviews:

```
★★★★★ Anthony Rivera (3 months ago): What a wonderful experience we had with the Clean Home Power Washing company.
Mr. Trey Bryan was pleasant, courteous and professional. He answered our questions, did an awesome job on the house, walkw...

★★★★★ Rick Hearn (3 weeks ago): My Homes Vinyl siding hasn't been cleaned in approximately 30 years and looked awful. I had no idea whether it could be cleaned or I'd need to replace it. Clean Home Power washing came out washed my s...

★★★★★ Anne-Marie McMenamin (a week ago): Raymond and Cameron were prompt, professional and did an excellent job.  We had roof, gutters, windows, house, driveway and front porch stone floor cleaned and they all look so much better.  We especi...
```

## 🔄 Data Processing

### Automatic Service Detection

The system automatically detects services based on business names and types:

- **House Washing**: Keywords like "house", "home", "residential", "siding", "exterior"
- **Driveway Cleaning**: Keywords like "driveway", "concrete", "pavement", "sidewalk"
- **Deck & Patio**: Keywords like "deck", "patio", "wood", "composite", "fence"
- **Commercial**: Keywords like "commercial", "business", "industrial", "office"

### Address Parsing

Full addresses are automatically parsed into components:
- `3813 Miriam Dr, Charlotte, NC 28205, United States`
- **Street**: `3813 Miriam Dr`
- **City**: `Charlotte`
- **State**: `NC`
- **ZIP**: `28205`

### Phone Number Formatting

Phone numbers are cleaned and standardized:
- `(*************` → `7042141485`
- `******-214-1485` → `7042141485`

### Website URL Validation

URLs are validated and formatted:
- `cleanhomepowerwashing.com` → `https://cleanhomepowerwashing.com`
- Invalid URLs are filtered out

## 📊 Sample CSV Data

```
Name	Address	Phone Number	International Phone	Website	Google Maps URL	Rating	Total Ratings	Status	Business Status	Price Level	Price Level Text	Latitude	Longitude	Plus Code	Place ID	Types	Hours	Photo URLs	Icon URL	Recent Reviews Count	Average Recent Rating	Recent Reviews Text
Clean Home Power Washing	3813 Miriam Dr, Charlotte, NC 28205, United States	(*************	******-214-1485	https://www.cleanhomepowerwashing.com/	https://maps.google.com/?cid=4673589347672508557	5	647	Closed	OPERATIONAL		Unknown	35.1980212	-80.7962048	867X56X3+6G	ChIJNYAitQC9VogRjcgR_r_s20A	point_of_interest, establishment	Monday: 8:00 AM – 5:00 PM; Tuesday: 8:00 AM – 5:00 PM; Wednesday: 8:00 AM – 5:00 PM; Thursday: 8:00 AM – 5:00 PM; Friday: 8:00 AM – 5:00 PM; Saturday: 8:00 AM – 5:00 PM; Sunday: Closed		https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png	5	5	★★★★★ Anthony Rivera (3 months ago): What a wonderful experience we had with the Clean Home Power Washing company.

★★★★★ Rick Hearn (3 weeks ago): My Homes Vinyl siding hasn't been cleaned in approximately 30 years and looked awful.

★★★★★ Anne-Marie McMenamin (a week ago): Raymond and Cameron were prompt, professional and did an excellent job.
```

## 🚀 Import Process

### Step 1: Prepare Your CSV File
1. Export data from Google Places API or similar tool
2. Ensure tab-separated format
3. Include required fields: Name, Address, Rating, Total Ratings, Latitude, Longitude
4. Verify data quality and formatting

### Step 2: Upload via Admin Interface
1. Go to `/admin/bulk-import`
2. Enter the Owner ID (user who will own these businesses)
3. Upload your CSV file via drag-and-drop or file picker
4. Click "Import Businesses"

### Step 3: Review Results
- **Success Count**: Number of businesses successfully imported
- **Failure Count**: Number of failed imports
- **Error Details**: Specific error messages for troubleshooting
- **Business Names**: Identifies which businesses failed

## ⚠️ Common Issues & Solutions

### Issue: "Invalid CSV format"
**Solution**: Ensure your file uses tab separators, not commas

### Issue: "Business name is required"
**Solution**: Check that the Name column has values for all rows

### Issue: "Valid latitude/longitude required"
**Solution**: Ensure coordinates are valid numbers (not text)

### Issue: "Rating must be between 0 and 5"
**Solution**: Check that rating values are numeric and within range

### Issue: "Failed to insert business"
**Solution**: Check that Owner ID exists and is valid

## 📈 Best Practices

### Data Quality
- ✅ Verify all required fields are populated
- ✅ Check coordinate accuracy
- ✅ Validate phone numbers and websites
- ✅ Ensure business names are unique

### Performance
- ✅ Import in batches of 100-500 businesses
- ✅ Test with small sample first
- ✅ Monitor import results for errors
- ✅ Clean data before importing

### Maintenance
- ✅ Regular data updates
- ✅ Remove duplicate entries
- ✅ Update business information
- ✅ Monitor review quality

## 🔧 Technical Details

### File Processing
- **Format**: Tab-separated values (TSV)
- **Encoding**: UTF-8
- **Size Limit**: 10MB per file
- **Row Limit**: 1000 businesses per import

### Database Operations
- **Transactions**: Each business import is atomic
- **Validation**: Multi-level validation (file → data → database)
- **Error Handling**: Continues processing after individual failures
- **Rollback**: Failed businesses don't affect successful ones

### Security
- **File Validation**: Only CSV/TSV files accepted
- **Data Sanitization**: All input data is cleaned
- **SQL Injection Prevention**: Parameterized queries
- **Access Control**: Admin-only functionality

---

**Need Help?** Contact the development team or check the admin interface for sample files and detailed instructions.
