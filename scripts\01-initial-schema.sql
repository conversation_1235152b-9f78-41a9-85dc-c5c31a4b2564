-- Profiles table to store user data, linked to Supabase auth
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Businesses table
CREATE TABLE businesses (
    id SERIAL PRIMARY KEY,
    owner_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    phone_number TEXT,
    website_url TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    -- For Geo-location searches
    latitude FLOAT,
    longitude FLOAT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    -- For monetization
    is_premium BOOLEAN DEFAULT FALSE
);

-- Services lookup table
CREATE TABLE services (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE
);

-- Pre-populate with common services
INSERT INTO services (name) VALUES 
('Residential Pressure Washing'),
('Commercial Pressure Washing'),
('Roof Cleaning'),
('Gutter Cleaning'),
('Driveway Cleaning'),
('Deck & Fence Cleaning'),
('Soft Washing'),
('Window Cleaning');

-- business_services join table (many-to-many relationship)
CREATE TABLE business_services (
    business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    service_id INTEGER NOT NULL REFERENCES services(id) ON DELETE CASCADE,
    PRIMARY KEY (business_id, service_id)
);

-- Reviews table
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Galleries table to store work photos
CREATE TABLE galleries (
    id SERIAL PRIMARY KEY,
    business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    caption TEXT,
    -- e.g., 'before', 'after' for showcasing results
    tag TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscriptions table for premium listings
CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    business_id INTEGER NOT NULL UNIQUE REFERENCES businesses(id) ON DELETE CASCADE,
    stripe_customer_id TEXT UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    status TEXT, -- e.g., 'active', 'canceled', 'past_due'
    created_at TIMESTAMPTZ DEFAULT NOW(),
    current_period_end TIMESTAMPTZ
);

-- Leads table for quote requests
CREATE TABLE leads (
    id SERIAL PRIMARY KEY,
    business_id INTEGER NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    customer_email TEXT NOT NULL,
    customer_phone TEXT,
    message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
