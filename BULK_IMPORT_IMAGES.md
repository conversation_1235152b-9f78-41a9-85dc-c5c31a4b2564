# Bulk Import with Image Processing

The bulk import system now supports automatic image downloading and uploading from Google Places API URLs and other image sources.

## 🖼️ Image Processing Features

### **Automatic Image Downloads**
- Downloads images from URLs provided in `photoUrls` field
- Supports Google Places API image URLs
- Handles multiple images per business (up to 10)
- Validates image formats and sizes

### **Supported Image Formats**
- JPEG/JPG
- PNG
- WebP
- GIF

### **Image Constraints**
- **Maximum file size**: 10MB per image
- **Maximum images per business**: 10
- **Download timeout**: 30 seconds per image
- **Storage bucket**: `business-portfolios`

## 📋 Data Format

### **JSON Format**
```json
{
  "name": "Sample Pressure Washing Co",
  "address": "123 Main St, Charlotte, NC 28205, United States",
  "rating": 4.8,
  "totalRatings": 150,
  "latitude": 35.1980212,
  "longitude": -80.7962048,
  "photoUrls": [
    "https://maps.googleapis.com/maps/api/place/js/PhotoService.GetPhoto?...",
    "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800",
    "https://example.com/business-photo.jpg"
  ],
  "reviews": [...],
  "weekdayText": [...]
}
```

### **CSV Format**
The CSV format includes a `Photo URLs` column with comma-separated image URLs:

```
Name	Address	...	Photo URLs	...
Sample Co	123 Main St...	...	https://example.com/img1.jpg, https://example.com/img2.jpg	...
```

## 🔄 Processing Workflow

1. **Business Creation**: Standard business, location, services, and reviews are created
2. **Image Download**: Each URL in `photoUrls` is downloaded with validation
3. **Image Upload**: Downloaded images are uploaded to Supabase Storage
4. **Database Storage**: Image URLs are stored in `portfolio_images` table
5. **Error Handling**: Failed downloads don't stop the business import

## ⚙️ Configuration

### **Image Processing Settings** (in `lib/image-utils.ts`)
```typescript
const MAX_IMAGES_PER_BUSINESS = 10
const MAX_IMAGE_SIZE = 10 * 1024 * 1024 // 10MB
const DOWNLOAD_TIMEOUT = 30000 // 30 seconds
const SUPPORTED_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
```

### **Storage Configuration**
- **Bucket**: `business-portfolios`
- **Public access**: Enabled
- **File naming**: `{businessId}/{timestamp}-{randomId}-{filename}`

## 🚨 Error Handling

### **Image Processing Errors**
- Failed image downloads don't prevent business creation
- Detailed error logging for debugging
- Graceful degradation when images fail

### **Common Error Types**
- **Download timeout**: Image takes too long to download
- **Unsupported format**: File is not a supported image type
- **File too large**: Image exceeds 10MB limit
- **HTTP errors**: 404, 403, or other server errors
- **Storage errors**: Supabase upload failures

## 📊 Import Results

The bulk import now returns detailed image processing statistics:

```json
{
  "success": true,
  "imported": 1,
  "failed": 0,
  "errors": [],
  "imageStats": {
    "totalImages": 3,
    "successfulUploads": 2,
    "failedUploads": 1,
    "errors": ["Image 3: Download timeout"]
  }
}
```

## 🧪 Testing

### **Test File**
Use `test-image-import.json` for testing the image processing functionality:

```bash
# Upload the test file through the admin interface
# Check the console logs for image processing details
# Verify images appear in the business gallery
```

### **Manual Testing**
1. Create a JSON file with `photoUrls` field
2. Upload through admin bulk import interface
3. Check browser console for processing logs
4. Verify images in business profile gallery

## 🔧 Troubleshooting

### **Images Not Downloading**
- Check if URLs are accessible
- Verify image format is supported
- Check file size limits
- Review console logs for specific errors

### **Storage Issues**
- Ensure `business-portfolios` bucket exists
- Check Supabase storage permissions
- Verify storage bucket configuration

### **Performance Considerations**
- Large batches with many images will take longer
- Images are processed sequentially with 500ms delays
- Consider smaller batches for better performance

## 🔗 Related Files

- `lib/image-utils.ts` - Core image processing logic
- `app/api/admin/bulk-import/route.ts` - Updated import endpoint
- `lib/bulk-import-utils.ts` - Updated data types
- `components/admin/bulk-import-form.tsx` - Updated UI
- `tests/image-utils.test.ts` - Test suite

## 📈 Next Steps

- Monitor image processing performance
- Add image optimization (resize, compress)
- Implement retry logic for failed downloads
- Add progress indicators for large imports
- Consider parallel processing for better performance
