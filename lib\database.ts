import { supabase, supabaseAdmin, createServerClient } from './supabase'
import type {
  Business,
  BusinessWithDetails,
  Profile,
  Review,
  ReviewWithProfile,
  Service,
  Location,
  PortfolioImage,
  MessageThread,
  Message,
  BusinessMember
} from './types'

import { calculateDistance, getBusinessCoordinates, getStateAbbreviation, getFullStateName } from './geocoding'
import { DatabaseError, NotFoundError, ConflictError, ValidationError } from './api-error-handler'

// Database error handling utilities
interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  backoffFactor?: number
}

const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2
}

// Retry wrapper for database operations
async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const { maxRetries, baseDelay, maxDelay, backoffFactor } = {
    ...DEFAULT_RETRY_OPTIONS,
    ...options
  }

  let lastError: any

  for (let attempt = 0; attempt <= maxRetries!; attempt++) {
    try {
      return await operation()
    } catch (error: any) {
      lastError = error

      // Don't retry on certain errors
      if (
        error?.code === 'PGRST116' || // Not found
        error?.code === '23505' || // Unique violation
        error?.code === '23503' || // Foreign key violation
        error?.code === '23514' || // Check violation
        error?.message?.includes('duplicate key') ||
        error?.message?.includes('violates foreign key') ||
        error?.message?.includes('violates check constraint')
      ) {
        break
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        baseDelay! * Math.pow(backoffFactor!, attempt),
        maxDelay!
      )

      console.warn(`Database operation failed (attempt ${attempt + 1}/${maxRetries! + 1}), retrying in ${delay}ms:`, error.message)

      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // Handle the final error
  handleDatabaseError(lastError)
}

// Enhanced error handler
function handleDatabaseError(error: any): never {
  console.error('Database error:', error)

  if (!error) {
    throw new DatabaseError('Unknown database error')
  }

  // Supabase/PostgREST specific errors
  if (error.code === 'PGRST116') {
    throw new NotFoundError('Resource not found')
  }

  // PostgreSQL specific errors
  if (error.code === '23505' || error.message?.includes('duplicate key')) {
    throw new ConflictError('Resource already exists')
  }

  if (error.code === '23503' || error.message?.includes('violates foreign key')) {
    throw new ValidationError('Invalid reference - related resource not found')
  }

  if (error.code === '23514' || error.message?.includes('violates check constraint')) {
    throw new ValidationError('Data validation failed')
  }

  if (error.code === '23502' || error.message?.includes('violates not-null')) {
    throw new ValidationError('Required field is missing')
  }

  // Connection and timeout errors
  if (
    error.message?.includes('timeout') ||
    error.message?.includes('connection') ||
    error.message?.includes('network') ||
    error.code === 'ECONNREFUSED' ||
    error.code === 'ETIMEDOUT'
  ) {
    throw new DatabaseError('Database connection error - please try again')
  }

  // Permission errors
  if (
    error.message?.includes('permission') ||
    error.message?.includes('access') ||
    error.code === '42501'
  ) {
    throw new DatabaseError('Database permission error')
  }

  // Generic database error
  throw new DatabaseError(error.message || 'Database operation failed')
}

// Validation helper for database operations
function validateDatabaseResult<T>(result: { data: T | null, error: any }, operation: string): T {
  if (result.error) {
    handleDatabaseError(result.error)
  }

  if (result.data === null) {
    throw new NotFoundError(`${operation} - resource not found`)
  }

  return result.data
}

// Safe database operation wrapper
async function safeDbOperation<T>(
  operation: () => Promise<{ data: T | null, error: any }>,
  operationName: string,
  retryOptions?: RetryOptions
): Promise<T> {
  return withRetry(async () => {
    const result = await operation()
    return validateDatabaseResult(result, operationName)
  }, retryOptions)
}

// ===== BUSINESS OPERATIONS =====

// Enhanced search function with geographic filtering and sorting
export async function searchBusinesses(options: {
  query?: string
  latitude?: number
  longitude?: number
  radius?: number // miles
  city?: string
  state?: string
  zipCode?: string
  serviceIds?: number[]
  minRating?: number
  sortBy?: 'distance' | 'rating' | 'reviews' | 'name'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}) {
  if (!supabaseAdmin) {
    // Return mock businesses when Supabase is not configured
    const mockBusinesses: (BusinessWithDetails & { distance?: number })[] = [
      {
        id: '1',
        name: 'Elite Pressure Pros',
        slug: 'elite-pressure-pros',
        description: 'Professional pressure washing services for residential and commercial properties.',
        phone: '(*************',
        website_url: 'https://elitepressurepros.com',
        avg_rating: 4.8,
        review_count: 127,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        owner_id: '1',
        distance: 2.5,
        location: {
          id: '1',
          business_id: '1',
          street_address: '123 Main St',
          city: 'Phoenix',
          state: 'AZ',
          zip_code: '85001'
        },
        services: [],
        portfolio_images: [],
        reviews: [],
        owner: {
          id: '1',
          full_name: 'John Smith',
          updated_at: new Date().toISOString()
        }
      },
      {
        id: '2',
        name: 'Clean Pro Services',
        slug: 'clean-pro-services',
        description: 'Residential and commercial pressure washing with eco-friendly solutions.',
        phone: '(*************',
        website_url: 'https://cleanproservices.com',
        avg_rating: 4.6,
        review_count: 89,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        owner_id: '2',
        distance: 5.2,
        location: {
          id: '2',
          business_id: '2',
          street_address: '456 Oak Ave',
          city: 'Phoenix',
          state: 'AZ',
          zip_code: '85002'
        },
        services: [],
        portfolio_images: [],
        reviews: [],
        owner: {
          id: '2',
          full_name: 'Sarah Wilson',
          updated_at: new Date().toISOString()
        }
      }
    ]
    
    // Apply basic filtering for mock data
    let filteredBusinesses = mockBusinesses

    // Text search in business name and description
    if (options.query) {
      filteredBusinesses = filteredBusinesses.filter(b =>
        b.name.toLowerCase().includes(options.query!.toLowerCase()) ||
        b.description.toLowerCase().includes(options.query!.toLowerCase())
      )
    }

    // Location filters (case-insensitive)
    if (options.city) {
      filteredBusinesses = filteredBusinesses.filter(b =>
        b.location?.city?.toLowerCase().includes(options.city!.toLowerCase())
      )
    }

    if (options.state) {
      filteredBusinesses = filteredBusinesses.filter(b =>
        b.location?.state?.toLowerCase().includes(options.state!.toLowerCase())
      )
    }

    if (options.zipCode) {
      filteredBusinesses = filteredBusinesses.filter(b =>
        b.location?.zip_code === options.zipCode
      )
    }

    if (options.minRating) {
      filteredBusinesses = filteredBusinesses.filter(b => b.avg_rating >= options.minRating!)
    }

    if (options.radius && options.latitude && options.longitude) {
      filteredBusinesses = filteredBusinesses.filter(b => (b.distance || 0) <= options.radius!)
    }
    
    // Apply sorting
    if (options.sortBy === 'distance') {
      filteredBusinesses.sort((a, b) => 
        options.sortOrder === 'asc' ? (a.distance || 0) - (b.distance || 0) : (b.distance || 0) - (a.distance || 0)
      )
    } else if (options.sortBy === 'rating') {
      filteredBusinesses.sort((a, b) => 
        options.sortOrder === 'asc' ? a.avg_rating - b.avg_rating : b.avg_rating - a.avg_rating
      )
    } else if (options.sortBy === 'reviews') {
      filteredBusinesses.sort((a, b) => 
        options.sortOrder === 'asc' ? a.review_count - b.review_count : b.review_count - a.review_count
      )
    } else if (options.sortBy === 'name') {
      filteredBusinesses.sort((a, b) => 
        options.sortOrder === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
      )
    }
    
    // Apply pagination
    const offset = options.offset || 0
    const limit = options.limit || 20
    const paginatedBusinesses = filteredBusinesses.slice(offset, offset + limit)
    
    return { 
      businesses: paginatedBusinesses, 
      total: filteredBusinesses.length, 
      error: null 
    }
  }

  let query = supabaseAdmin
    .from('businesses')
    .select(`
      *,
      location:locations(*),
      services:business_services(
        service:services(*)
      ),
      portfolio_images(*),
      owner:profiles!businesses_owner_id_fkey(*)
    `)

  // Text search in business name and description
  if (options.query) {
    query = query.or(`name.ilike.%${options.query}%,description.ilike.%${options.query}%`)
  }

  // Location filters (case-insensitive)
  // Note: For joined tables in Supabase, we need to filter after the query
  // The filtering will be done in the post-processing step

  // Service filters
  if (options.serviceIds && options.serviceIds.length > 0) {
    query = query.in('business_services.service_id', options.serviceIds)
  }

  // Rating filter
  if (options.minRating) {
    query = query.gte('avg_rating', options.minRating)
  }

  // Apply sorting (distance sorting will be handled post-query)
  if (options.sortBy === 'rating') {
    query = query.order('avg_rating', { ascending: options.sortOrder === 'asc' })
  } else if (options.sortBy === 'reviews') {
    query = query.order('review_count', { ascending: options.sortOrder === 'asc' })
  } else if (options.sortBy === 'name') {
    query = query.order('name', { ascending: options.sortOrder === 'asc' })
  } else {
    // Default sorting by rating desc, then review count desc
    query = query.order('avg_rating', { ascending: false })
      .order('review_count', { ascending: false })
  }

  const { data, error } = await query

  if (error) {
    console.error('Error searching businesses:', error)
    return { businesses: [], total: 0, error }
  }

  let businesses = data as (BusinessWithDetails & { distance?: number })[]

  // Apply location filters (post-processing for joined tables)
  if (options.city) {
    businesses = businesses.filter(business =>
      business.location?.city?.toLowerCase().includes(options.city!.toLowerCase())
    )
  }

  if (options.state) {
    businesses = businesses.filter(business => {
      const businessState = business.location?.state?.toLowerCase()
      const searchState = options.state!.toLowerCase()

      // Check for exact match or partial match
      if (businessState?.includes(searchState)) {
        return true
      }

      // Check if searching for full state name but business has abbreviation
      const stateAbbreviation = getStateAbbreviation(searchState)
      if (stateAbbreviation && businessState === stateAbbreviation.toLowerCase()) {
        return true
      }

      // Check if searching for abbreviation but business has full name
      const fullStateName = getFullStateName(searchState)
      if (fullStateName && businessState?.includes(fullStateName.toLowerCase())) {
        return true
      }

      return false
    })
  }

  if (options.zipCode) {
    businesses = businesses.filter(business =>
      business.location?.zip_code === options.zipCode
    )
  }

  // Calculate distances if coordinates provided
  if (options.latitude && options.longitude) {
    businesses = businesses.map(business => {
      const businessCoords = getBusinessCoordinates(business.location)
      if (businessCoords) {
        const distance = calculateDistance(
          options.latitude!,
          options.longitude!,
          businessCoords.lat,
          businessCoords.lng
        )
        return { ...business, distance }
      }
      return business
    })

    // Filter by radius
    if (options.radius) {
      businesses = businesses.filter(business => 
        business.distance === undefined || business.distance <= options.radius!
      )
    }

    // Sort by distance if requested
    if (options.sortBy === 'distance') {
      businesses.sort((a, b) => {
        const distA = a.distance || Infinity
        const distB = b.distance || Infinity
        return options.sortOrder === 'asc' ? distA - distB : distB - distA
      })
    }
  }

  // Apply pagination
  const total = businesses.length
  const offset = options.offset || 0
  const limit = options.limit || 20
  const paginatedBusinesses = businesses.slice(offset, offset + limit)

  return { 
    businesses: paginatedBusinesses, 
    total, 
    error: null 
  }
}

export async function getBusinesses(options?: {
  city?: string
  state?: string
  zipCode?: string
  serviceId?: number
  minRating?: number
  limit?: number
  offset?: number
}) {
  if (!supabaseAdmin) {
    // Return mock businesses when Supabase is not configured
    const mockBusinesses: BusinessWithDetails[] = [
      {
        id: '1',
        name: 'Elite Pressure Pros',
        slug: 'elite-pressure-pros',
        description: 'Professional pressure washing services for residential and commercial properties.',
        phone: '(*************',
        website_url: 'https://elitepressurepros.com',
        avg_rating: 4.8,
        review_count: 127,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        owner_id: '1',
        location: {
          id: '1',
          business_id: '1',
          street_address: '123 Main St',
          city: 'Phoenix',
          state: 'AZ',
          zip_code: '85001'
        },
        services: [],
        portfolio_images: [],
        reviews: [],
        owner: {
          id: '1',
          full_name: 'John Smith',
          updated_at: new Date().toISOString()
        }
      }
    ]
    return { businesses: mockBusinesses, error: null }
  }

  let query = supabaseAdmin
    .from('businesses')
    .select(`
      *,
      location:locations(*),
      services:business_services(
        service:services(*)
      ),
      portfolio_images(*)
    `)

  // Apply filters
  if (options?.city) {
    query = query.eq('locations.city', options.city)
  }
  if (options?.state) {
    query = query.eq('locations.state', options.state)
  }
  if (options?.zipCode) {
    query = query.eq('locations.zip_code', options.zipCode)
  }
  if (options?.serviceId) {
    query = query.eq('business_services.service_id', options.serviceId)
  }
  if (options?.minRating) {
    query = query.gte('avg_rating', options.minRating)
  }

  // Apply pagination
  if (options?.limit) {
    query = query.limit(options.limit)
  }
  if (options?.offset) {
    query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1)
  }

  // Order by rating and review count
  query = query.order('avg_rating', { ascending: false })
    .order('review_count', { ascending: false })

  const { data, error } = await query

  if (error) {
    console.error('Error fetching businesses:', error)
    return { businesses: [], error }
  }

  return { businesses: data as BusinessWithDetails[], error: null }
}

export async function getBusinessBySlug(slug: string) {
  if (!supabaseAdmin) {
    // Return mock data when Supabase is not configured
    const mockBusiness: BusinessWithDetails = {
      id: '1',
      name: 'Elite Pressure Pros',
      slug: slug,
      description: 'Professional pressure washing services for residential and commercial properties.',
      phone: '(*************',
      website_url: 'https://elitepressurepros.com',
      avg_rating: 4.8,
      review_count: 127,
      business_hours: [
        'Monday: 8:00 AM – 5:00 PM',
        'Tuesday: 8:00 AM – 5:00 PM',
        'Wednesday: 8:00 AM – 5:00 PM',
        'Thursday: 8:00 AM – 5:00 PM',
        'Friday: 8:00 AM – 5:00 PM',
        'Saturday: 9:00 AM – 3:00 PM',
        'Sunday: Closed'
      ],
      google_place_id: 'ChIJMockPlaceId123',
      google_maps_url: 'https://maps.google.com/?cid=123456789',
      business_status: 'OPERATIONAL',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      owner_id: '1',
      location: {
        id: '1',
        business_id: '1',
        street_address: '123 Main St',
        city: 'Phoenix',
        state: 'AZ',
        zip_code: '85001'
      },
      services: [],
      portfolio_images: [],
      reviews: [],
      owner: {
        id: '1',
        full_name: 'John Smith',
        updated_at: new Date().toISOString()
      }
    }
    return { business: mockBusiness, error: null }
  }

  // First try to find by slug
  let { data, error } = await supabaseAdmin
    .from('businesses')
    .select(`
      *,
      location:locations(*),
      services:business_services(
        service:services(*)
      ),
      portfolio_images(*),
      reviews(
        *,
        profile:profiles!reviews_author_id_fkey(*)
      ),
      owner:profiles!businesses_owner_id_fkey(*)
    `)
    .eq('slug', slug)
    .single()

  // If not found by slug and the slug looks like a UUID, try finding by ID
  if (error && error.code === 'PGRST116' && slug.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    console.log('Slug looks like UUID, trying to find by ID:', slug)
    const result = await supabaseAdmin
      .from('businesses')
      .select(`
        *,
        location:locations(*),
        services:business_services(
          service:services(*)
        ),
        portfolio_images(*),
        reviews(
          *,
          profile:profiles!reviews_author_id_fkey(*)
        ),
        owner:profiles!businesses_owner_id_fkey(*)
      `)
      .eq('id', slug)
      .single()

    data = result.data
    error = result.error
  }

  if (error) {
    console.error('Error fetching business:', error)
    return { business: null, error }
  }

  return { business: data as BusinessWithDetails, error: null }
}

export async function createBusiness(businessData: {
  name: string
  slug: string
  description?: string
  phone?: string
  website_url?: string
  owner_id: string
}) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { business: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('businesses')
    .insert(businessData)
    .select()
    .single()

  if (error) {
    console.error('Error creating business:', error)
    return { business: null, error }
  }

  // Add the owner as a business member
  await supabaseServer
    .from('business_members')
    .insert({
      business_id: data.id,
      user_id: businessData.owner_id,
      role: 'owner'
    })

  return { business: data as Business, error: null }
}

export async function updateBusiness(businessId: string, updates: Partial<Business>) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { business: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('businesses')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', businessId)
    .select()
    .single()

  if (error) {
    console.error('Error updating business:', error)
    return { business: null, error }
  }

  return { business: data as Business, error: null }
}

export async function deleteBusiness(businessId: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { error: new Error('Database not available') }
  }
  
  const { error } = await supabaseServer
    .from('businesses')
    .delete()
    .eq('id', businessId)

  if (error) {
    console.error('Error deleting business:', error)
    return { error }
  }

  return { error: null }
}

// ===== LOCATION OPERATIONS =====

export async function upsertLocation(businessId: string, locationData: Omit<Location, 'id' | 'business_id'>) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { location: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('locations')
    .upsert({
      business_id: businessId,
      ...locationData
    })
    .select()
    .single()

  if (error) {
    console.error('Error upserting location:', error)
    return { location: null, error }
  }

  return { location: data as Location, error: null }
}

// ===== SERVICE OPERATIONS =====

export async function getServices() {
  if (!supabase) {
    // Return mock services when Supabase is not configured
    const mockServices: Service[] = [
      { id: 1, name: 'House Washing', description: 'Complete exterior house cleaning' },
      { id: 2, name: 'Driveway Cleaning', description: 'Concrete and asphalt driveway cleaning' },
      { id: 3, name: 'Deck & Patio Cleaning', description: 'Wood and composite deck restoration' },
      { id: 4, name: 'Commercial Cleaning', description: 'Commercial building exterior cleaning' }
    ]
    return { services: mockServices, error: null }
  }

  const { data, error } = await supabase
    .from('services')
    .select('*')
    .order('name')

  if (error) {
    console.error('Error fetching services:', error)
    return { services: [], error }
  }

  return { services: data as Service[], error: null }
}

export async function updateBusinessServices(businessId: string, serviceIds: number[]) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { error: new Error('Database not available') }
  }
  
  // First, delete existing services
  await supabaseServer
    .from('business_services')
    .delete()
    .eq('business_id', businessId)

  // Then, insert new services
  if (serviceIds.length > 0) {
    const { error } = await supabaseServer
      .from('business_services')
      .insert(
        serviceIds.map(serviceId => ({
          business_id: businessId,
          service_id: serviceId
        }))
      )

    if (error) {
      console.error('Error updating business services:', error)
      return { error }
    }
  }

  return { error: null }
}

// ===== PORTFOLIO OPERATIONS =====

export async function addPortfolioImage(businessId: string, imageData: {
  image_url: string
  caption?: string
  display_order?: number
}) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { image: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('portfolio_images')
    .insert({
      business_id: businessId,
      ...imageData
    })
    .select()
    .single()

  if (error) {
    console.error('Error adding portfolio image:', error)
    return { image: null, error }
  }

  return { image: data as PortfolioImage, error: null }
}

export async function deletePortfolioImage(imageId: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { error: new Error('Database not available') }
  }
  
  const { error } = await supabaseServer
    .from('portfolio_images')
    .delete()
    .eq('id', imageId)

  if (error) {
    console.error('Error deleting portfolio image:', error)
    return { error }
  }

  return { error: null }
}

// ===== REVIEW OPERATIONS =====

export async function getBusinessReviews(businessId: string, limit = 10, offset = 0) {
  if (!supabase) {
    // Return mock reviews when Supabase is not configured
    const mockReviews: ReviewWithProfile[] = [
      {
        id: '1',
        business_id: businessId,
        author_id: '1',
        rating: 5,
        content: 'Elite Pressure Pros did an amazing job cleaning our driveway and house exterior. Very professional and thorough.',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        profile: {
          id: '1',
          full_name: 'Sarah Johnson',
          updated_at: new Date().toISOString()
        }
      },
      {
        id: '2',
        business_id: businessId,
        author_id: '2',
        rating: 5,
        content: 'Great results and fair pricing. They were on time and cleaned up after themselves.',
        created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        profile: {
          id: '2',
          full_name: 'Mike Davis',
          updated_at: new Date().toISOString()
        }
      }
    ]
    return { reviews: mockReviews, error: null }
  }

  const { data, error } = await supabase
    .from('reviews')
    .select(`
      *,
      profile:profiles(*)
    `)
    .eq('business_id', businessId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching reviews:', error)
    return { reviews: [], error }
  }

  return { reviews: data as ReviewWithProfile[], error: null }
}

export async function createReview(reviewData: {
  business_id: string
  rating: number
  content?: string
}) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { review: null, error: new Error('Database not available') }
  }

  // Since authentication is disabled, use the existing profile ID
  const mockUserId = '5354596e-3cd1-4992-9824-7c0d88fe8a05'

  const { data, error } = await supabaseServer
    .from('reviews')
    .insert({
      ...reviewData,
      author_id: mockUserId
    })
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error creating review:', error)
    return { review: null, error }
  }

  return { review: data as ReviewWithProfile, error: null }
}

export async function updateReview(reviewId: string, updateData: {
  rating?: number
  content?: string
}) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { review: null, error: new Error('Database not available') }
  }

  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { review: null, error: new Error('User not authenticated') }
  }

  // First check if the review exists and belongs to the user
  const { data: existingReview, error: fetchError } = await supabaseServer
    .from('reviews')
    .select('author_id')
    .eq('id', reviewId)
    .single()

  if (fetchError || !existingReview) {
    return { review: null, error: new Error('Review not found') }
  }

  if (existingReview.author_id !== user.id) {
    return { review: null, error: new Error('Unauthorized to update this review') }
  }

  const { data, error } = await supabaseServer
    .from('reviews')
    .update(updateData)
    .eq('id', reviewId)
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error updating review:', error)
    return { review: null, error }
  }

  return { review: data as ReviewWithProfile, error: null }
}

export async function deleteReview(reviewId: string) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { success: false, error: new Error('Database not available') }
  }

  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { success: false, error: new Error('User not authenticated') }
  }

  // First check if the review exists and belongs to the user
  const { data: existingReview, error: fetchError } = await supabaseServer
    .from('reviews')
    .select('author_id')
    .eq('id', reviewId)
    .single()

  if (fetchError || !existingReview) {
    return { success: false, error: new Error('Review not found') }
  }

  if (existingReview.author_id !== user.id) {
    return { success: false, error: new Error('Unauthorized to delete this review') }
  }

  const { error } = await supabaseServer
    .from('reviews')
    .delete()
    .eq('id', reviewId)

  if (error) {
    console.error('Error deleting review:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

export async function updateBusinessRating(businessId: string) {
  if (!supabase) {
    return { success: false, error: new Error('Database not available') }
  }

  // Calculate the new average rating and review count
  const { data: reviews, error: reviewsError } = await supabase
    .from('reviews')
    .select('rating')
    .eq('business_id', businessId)

  if (reviewsError) {
    console.error('Error fetching reviews for rating calculation:', reviewsError)
    return { success: false, error: reviewsError }
  }

  const reviewCount = reviews.length
  const averageRating = reviewCount > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviewCount
    : 0

  // Update the business with the new rating and count
  const { error: updateError } = await supabase
    .from('businesses')
    .update({
      avg_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      review_count: reviewCount
    })
    .eq('id', businessId)

  if (updateError) {
    console.error('Error updating business rating:', updateError)
    return { success: false, error: updateError }
  }

  return { success: true, error: null }
}

export async function reportReview(reviewId: string, reason: string, description?: string) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { success: false, error: new Error('Database not available') }
  }

  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { success: false, error: new Error('User not authenticated') }
  }

  // Check if the review exists
  const { data: existingReview, error: fetchError } = await supabaseServer
    .from('reviews')
    .select('id')
    .eq('id', reviewId)
    .single()

  if (fetchError || !existingReview) {
    return { success: false, error: new Error('Review not found') }
  }

  // Create a review report
  const { error } = await supabaseServer
    .from('review_reports')
    .insert({
      review_id: reviewId,
      reporter_id: user.id,
      reason,
      description
    })

  if (error) {
    console.error('Error reporting review:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

// ===== MESSAGE OPERATIONS =====

export async function createMessageThread(businessId: string, subject?: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { thread: null, error: new Error('Database not available') }
  }
  
  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { thread: null, error: new Error('User not authenticated') }
  }

  const { data, error } = await supabaseServer
    .from('message_threads')
    .insert({
      business_id: businessId,
      user_id: user.id,
      subject
    })
    .select()
    .single()

  if (error) {
    console.error('Error creating message thread:', error)
    return { thread: null, error }
  }

  return { thread: data as MessageThread, error: null }
}

export async function sendMessage(threadId: string, content: string, attachments?: string[]) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { message: null, error: new Error('Database not available') }
  }

  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { message: null, error: new Error('User not authenticated') }
  }

  const { data, error } = await supabaseServer
    .from('messages')
    .insert({
      thread_id: threadId,
      author_id: user.id,
      content,
      attachments: attachments || null
    })
    .select(`
      *,
      author:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error sending message:', error)
    return { message: null, error }
  }

  // Update thread's updated_at timestamp
  await supabaseServer
    .from('message_threads')
    .update({ updated_at: new Date().toISOString() })
    .eq('id', threadId)

  return { message: data as Message, error: null }
}

export async function getMessageThreads(businessId?: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { threads: [], error: new Error('Database not available') }
  }
  
  let query = supabaseServer
    .from('message_threads')
    .select(`
      *,
      business:businesses(*),
      user:profiles(*),
      messages(*)
    `)
    .order('updated_at', { ascending: false })

  if (businessId) {
    query = query.eq('business_id', businessId)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching message threads:', error)
    return { threads: [], error }
  }

  return { threads: data, error: null }
}

export async function getThreadMessages(threadId: string) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { messages: [], error: new Error('Database not available') }
  }

  const { data, error } = await supabaseServer
    .from('messages')
    .select(`
      *,
      author:profiles(*)
    `)
    .eq('thread_id', threadId)
    .order('sent_at', { ascending: true })

  if (error) {
    console.error('Error fetching thread messages:', error)
    return { messages: [], error }
  }

  return { messages: data, error: null }
}

export async function updateThreadStatus(threadId: string, status: string) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { thread: null, error: new Error('Database not available') }
  }

  const { data: { user } } = await supabaseServer.auth.getUser()
  if (!user) {
    return { thread: null, error: new Error('User not authenticated') }
  }

  const { data, error } = await supabaseServer
    .from('message_threads')
    .update({
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', threadId)
    .select(`
      *,
      business:businesses(*),
      user:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error updating thread status:', error)
    return { thread: null, error }
  }

  return { thread: data, error: null }
}

export async function markMessagesAsRead(threadId: string, userId: string) {
  const supabaseServer = await createServerClient()

  if (!supabaseServer) {
    return { success: false, error: new Error('Database not available') }
  }

  const { error } = await supabaseServer
    .from('messages')
    .update({ read_at: new Date().toISOString() })
    .eq('thread_id', threadId)
    .neq('author_id', userId)
    .is('read_at', null)

  if (error) {
    console.error('Error marking messages as read:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

// ===== USER OPERATIONS =====

export async function getUserBusinesses(userId: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { businesses: [], error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('business_members')
    .select(`
      role,
      business:businesses(*)
    `)
    .eq('user_id', userId)

  if (error) {
    console.error('Error fetching user businesses:', error)
    return { businesses: [], error }
  }

  return { businesses: data, error: null }
}

export async function updateProfile(userId: string, updates: Partial<Profile>) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { profile: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('profiles')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', userId)
    .select()
    .single()

  if (error) {
    console.error('Error updating profile:', error)
    return { profile: null, error }
  }

  return { profile: data as Profile, error: null }
}

// ===== BUSINESS MEMBER OPERATIONS =====

export async function getBusinessMembers(businessId: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { members: [], error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('business_members')
    .select(`
      *,
      profile:profiles(*)
    `)
    .eq('business_id', businessId)
    .order('joined_at', { ascending: true })

  if (error) {
    console.error('Error fetching business members:', error)
    return { members: [], error }
  }

  return { members: data, error: null }
}

export async function addBusinessMember(businessId: string, email: string, role: 'admin' | 'member') {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { member: null, error: new Error('Database not available') }
  }
  
  // Use the admin client to find user by email in auth.users
  if (!supabaseAdmin) {
    return { member: null, error: new Error('Admin access not available') }
  }
  
  // First, find the user by email in auth.users
  const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.listUsers()
  
  if (authError) {
    return { member: null, error: new Error('Failed to search for user') }
  }
  
  const foundUser = authUser.users.find(user => user.email === email)

  if (!foundUser) {
    return { member: null, error: new Error('User not found with that email address') }
  }

  const userId = foundUser.id

  // Check if user is already a member
  const { data: existingMember } = await supabaseServer
    .from('business_members')
    .select('user_id')
    .eq('business_id', businessId)
    .eq('user_id', userId)
    .single()

  if (existingMember) {
    return { member: null, error: new Error('User is already a member of this business') }
  }

  // Add the member
  const { data, error } = await supabaseServer
    .from('business_members')
    .insert({
      business_id: businessId,
      user_id: userId,
      role
    })
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error adding business member:', error)
    return { member: null, error }
  }

  return { member: data, error: null }
}

export async function updateBusinessMember(businessId: string, userId: string, role: 'admin' | 'member') {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { member: null, error: new Error('Database not available') }
  }
  
  const { data, error } = await supabaseServer
    .from('business_members')
    .update({ role })
    .eq('business_id', businessId)
    .eq('user_id', userId)
    .select(`
      *,
      profile:profiles(*)
    `)
    .single()

  if (error) {
    console.error('Error updating business member:', error)
    return { member: null, error }
  }

  return { member: data, error: null }
}

export async function removeBusinessMember(businessId: string, userId: string) {
  const supabaseServer = await createServerClient()
  
  if (!supabaseServer) {
    return { error: new Error('Database not available') }
  }
  
  const { error } = await supabaseServer
    .from('business_members')
    .delete()
    .eq('business_id', businessId)
    .eq('user_id', userId)

  if (error) {
    console.error('Error removing business member:', error)
    return { error }
  }

  return { error: null }
}