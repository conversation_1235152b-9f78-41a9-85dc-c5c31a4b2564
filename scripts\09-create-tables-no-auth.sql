-- Create Missing Tables: Leads and Subscriptions (NO AUTH VERSION)
-- This script creates tables WITHOUT Row Level Security for testing
-- TEMPORARY: For database testing only - DO NOT USE IN PRODUCTION

-- ===== CREATE SUBSCRIPTIONS TABLE =====

CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL UNIQUE REFERENCES public.businesses(id) ON DELETE CASCADE,
  plan TEXT NOT NULL DEFAULT 'free' CHECK (plan IN ('free', 'premium')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'past_due', 'incomplete')),
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT UNIQUE,
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE LEADS TABLE =====

CREATE TABLE IF NOT EXISTS public.leads (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  
  -- Lead contact information
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  
  -- Lead details
  service_type TEXT,
  property_address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  
  -- Lead status and tracking
  status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'quoted', 'scheduled', 'completed', 'lost')),
  source TEXT DEFAULT 'website' CHECK (source IN ('website', 'referral', 'google', 'facebook', 'phone', 'other')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Lead value and notes
  estimated_value DECIMAL(10,2),
  notes TEXT,
  
  -- Follow-up tracking
  last_contact_date TIMESTAMPTZ,
  next_follow_up_date TIMESTAMPTZ,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE LEAD ACTIVITIES TABLE =====

CREATE TABLE IF NOT EXISTS public.lead_activities (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES public.leads(id) ON DELETE CASCADE,
  
  -- Activity details
  activity_type TEXT NOT NULL CHECK (activity_type IN ('call', 'email', 'meeting', 'quote_sent', 'follow_up', 'note')),
  description TEXT NOT NULL,
  
  -- Activity metadata
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE INDEXES =====

-- Subscription indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_business_id ON public.subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);

-- Lead indexes
CREATE INDEX IF NOT EXISTS idx_leads_business_id ON public.leads(business_id);
CREATE INDEX IF NOT EXISTS idx_leads_status ON public.leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON public.leads(created_at);
CREATE INDEX IF NOT EXISTS idx_leads_next_follow_up ON public.leads(next_follow_up_date);

-- Lead activity indexes
CREATE INDEX IF NOT EXISTS idx_lead_activities_lead_id ON public.lead_activities(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_activities_created_at ON public.lead_activities(created_at);

-- ===== DISABLE ROW LEVEL SECURITY FOR TESTING =====

ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.leads DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_activities DISABLE ROW LEVEL SECURITY;

-- Also disable RLS on related tables for testing
ALTER TABLE public.businesses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- ===== CREATE TRIGGERS =====

-- Function to update updated_at timestamp (create only if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers (drop first if they exist)
DROP TRIGGER IF EXISTS trigger_update_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER trigger_update_subscriptions_updated_at
  BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_leads_updated_at ON public.leads;
CREATE TRIGGER trigger_update_leads_updated_at
  BEFORE UPDATE ON public.leads
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ===== GRANT PERMISSIONS =====

GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.leads TO authenticated;
GRANT ALL ON public.lead_activities TO authenticated;

-- Grant to anonymous for testing (TEMPORARY)
GRANT ALL ON public.subscriptions TO anon;
GRANT ALL ON public.leads TO anon;
GRANT ALL ON public.lead_activities TO anon;
GRANT ALL ON public.businesses TO anon;
GRANT ALL ON public.profiles TO anon;

-- ===== CREATE SAMPLE DATA FOR TESTING =====

-- Insert a test profile
INSERT INTO public.profiles (id, full_name, updated_at)
VALUES ('mock-user-id-12345', 'Test User', NOW())
ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  updated_at = EXCLUDED.updated_at;

-- Insert a test business
INSERT INTO public.businesses (id, owner_id, name, slug, description, created_at, updated_at)
VALUES (
  '530f712e-abdf-431f-ac55-b16666f696e9',
  'mock-user-id-12345',
  'Test Pressure Washing Co',
  'test-pressure-washing-co',
  'A test business for database testing',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  updated_at = EXCLUDED.updated_at;

-- Insert test subscription
INSERT INTO public.subscriptions (business_id, plan, status, created_at, updated_at)
VALUES (
  '530f712e-abdf-431f-ac55-b16666f696e9',
  'free',
  'active',
  NOW(),
  NOW()
) ON CONFLICT (business_id) DO UPDATE SET
  plan = EXCLUDED.plan,
  status = EXCLUDED.status,
  updated_at = EXCLUDED.updated_at;

-- Insert test leads
INSERT INTO public.leads (business_id, name, email, phone, service_type, status, source, priority, estimated_value, notes, created_at, updated_at)
VALUES 
  (
    '530f712e-abdf-431f-ac55-b16666f696e9',
    'John Smith',
    '<EMAIL>',
    '(*************',
    'Driveway Cleaning',
    'new',
    'website',
    'medium',
    250.00,
    'Interested in driveway cleaning service',
    NOW(),
    NOW()
  ),
  (
    '530f712e-abdf-431f-ac55-b16666f696e9',
    'Jane Doe',
    '<EMAIL>',
    '(*************',
    'House Washing',
    'contacted',
    'referral',
    'high',
    500.00,
    'Needs full house exterior cleaning',
    NOW(),
    NOW()
  ),
  (
    '530f712e-abdf-431f-ac55-b16666f696e9',
    'Bob Johnson',
    '<EMAIL>',
    '(*************',
    'Deck Cleaning',
    'quoted',
    'google',
    'low',
    150.00,
    'Small deck cleaning job',
    NOW(),
    NOW()
  )
ON CONFLICT DO NOTHING;

-- ===== COMPLETION MESSAGE =====

DO $$
BEGIN
  RAISE NOTICE '=== TABLES CREATED (NO AUTH MODE) ===';
  RAISE NOTICE '🚫 WARNING: Row Level Security is DISABLED';
  RAISE NOTICE '📊 subscriptions table: READY';
  RAISE NOTICE '📊 leads table: READY';
  RAISE NOTICE '📊 lead_activities table: READY';
  RAISE NOTICE '🧪 Test data: INSERTED';
  RAISE NOTICE '';
  RAISE NOTICE '✅ Database is ready for testing!';
  RAISE NOTICE '⚠️  Remember to re-enable RLS after testing';
END $$;
