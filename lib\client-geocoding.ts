/**
 * Client-side geocoding service
 * Uses secure API endpoints to keep Google Maps API key on server
 */

export interface ClientGeocodingResult {
  coordinates: {
    lat: number
    lng: number
  }
  formattedAddress: string
  addressComponents: {
    streetNumber?: string
    route?: string
    city?: string
    state?: string
    zipCode?: string
    country?: string
  }
  placeId?: string
}

/**
 * Geocode an address using the secure API endpoint
 */
export async function geocodeAddressClient(address: string): Promise<ClientGeocodingResult | null> {
  try {
    const response = await fetch('/api/geocoding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    })

    const data = await response.json()

    if (data.success && data.data) {
      return data.data
    } else {
      console.warn('Client geocoding failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('Client geocoding error:', error)
    return null
  }
}

/**
 * Reverse geocode coordinates using the secure API endpoint
 */
export async function reverseGeocodeClient(lat: number, lng: number): Promise<ClientGeocodingResult | null> {
  try {
    const response = await fetch(`/api/geocoding?lat=${lat}&lng=${lng}`, {
      method: 'GET',
    })

    const data = await response.json()

    if (data.success && data.data) {
      return data.data
    } else {
      console.warn('Client reverse geocoding failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('Client reverse geocoding error:', error)
    return null
  }
}

/**
 * Batch geocode multiple addresses
 */
export async function batchGeocodeClient(addresses: string[]): Promise<(ClientGeocodingResult | null)[]> {
  const results: (ClientGeocodingResult | null)[] = []
  
  // Process in batches to avoid overwhelming the server
  const batchSize = 5
  const delay = 200 // ms between requests
  
  for (let i = 0; i < addresses.length; i += batchSize) {
    const batch = addresses.slice(i, i + batchSize)
    
    const batchPromises = batch.map(async (address, index) => {
      // Add delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, index * delay))
      return geocodeAddressClient(address)
    })
    
    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
    
    // Add delay between batches
    if (i + batchSize < addresses.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  return results
}

/**
 * Get user's current location and reverse geocode it
 */
export async function getCurrentLocationAndGeocode(): Promise<{
  coordinates: { lat: number; lng: number }
  address?: ClientGeocodingResult
} | null> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      console.warn('Geolocation is not supported by this browser')
      resolve(null)
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const coordinates = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        }

        try {
          const address = await reverseGeocodeClient(coordinates.lat, coordinates.lng)
          resolve({ coordinates, address: address || undefined })
        } catch (error) {
          console.warn('Failed to reverse geocode current location:', error)
          resolve({ coordinates })
        }
      },
      (error) => {
        console.warn('Failed to get current location:', error)
        resolve(null)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    )
  })
}

/**
 * Calculate distance between two points using Haversine formula
 * Returns distance in miles
 */
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 3959 // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

/**
 * Format distance for display
 */
export function formatDistance(distance: number): string {
  if (distance < 0.1) {
    return 'Less than 0.1 mi'
  } else if (distance < 1) {
    return `${distance.toFixed(1)} mi`
  } else if (distance < 10) {
    return `${distance.toFixed(1)} mi`
  } else {
    return `${Math.round(distance)} mi`
  }
}

/**
 * Validate coordinates
 */
export function validateCoordinates(lat: number, lng: number): boolean {
  return (
    lat >= -90 && lat <= 90 &&
    lng >= -180 && lng <= 180 &&
    !isNaN(lat) && !isNaN(lng)
  )
}

/**
 * Get center point of multiple coordinates
 */
export function getCenterPoint(coordinates: { lat: number; lng: number }[]): { lat: number; lng: number } | null {
  if (coordinates.length === 0) return null
  
  if (coordinates.length === 1) return coordinates[0]
  
  let totalLat = 0
  let totalLng = 0
  
  coordinates.forEach(coord => {
    totalLat += coord.lat
    totalLng += coord.lng
  })
  
  return {
    lat: totalLat / coordinates.length,
    lng: totalLng / coordinates.length
  }
}

/**
 * Get bounds that contain all coordinates
 */
export function getBounds(coordinates: { lat: number; lng: number }[]): {
  north: number
  south: number
  east: number
  west: number
} | null {
  if (coordinates.length === 0) return null
  
  let north = coordinates[0].lat
  let south = coordinates[0].lat
  let east = coordinates[0].lng
  let west = coordinates[0].lng
  
  coordinates.forEach(coord => {
    north = Math.max(north, coord.lat)
    south = Math.min(south, coord.lat)
    east = Math.max(east, coord.lng)
    west = Math.min(west, coord.lng)
  })
  
  return { north, south, east, west }
}
