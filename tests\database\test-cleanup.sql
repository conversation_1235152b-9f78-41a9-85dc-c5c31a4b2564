-- Test Data Cleanup Script
-- This script removes all test data from the database

-- Clean up test data in reverse dependency order
DELETE FROM messages WHERE thread_id IN (
  SELECT id FROM message_threads 
  WHERE business_id IN (
    SELECT id FROM businesses WHERE name LIKE 'Test%'
  )
);

DELETE FROM message_threads WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM reviews WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM portfolio_images WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM business_services WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM business_members WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM locations WHERE business_id IN (
  SELECT id FROM businesses WHERE name LIKE 'Test%'
);

DELETE FROM businesses WHERE name LIKE 'Test%';

DELETE FROM profiles WHERE email LIKE '%<EMAIL>';

-- Reset sequences if needed (PostgreSQL specific)
-- This ensures clean ID generation for future tests
SELECT setval(pg_get_serial_sequence('profiles', 'id'), COALESCE(MAX(id), 1), false) FROM profiles;
SELECT setval(pg_get_serial_sequence('businesses', 'id'), COALESCE(MAX(id), 1), false) FROM businesses;
SELECT setval(pg_get_serial_sequence('locations', 'id'), COALESCE(MAX(id), 1), false) FROM locations;
SELECT setval(pg_get_serial_sequence('reviews', 'id'), COALESCE(MAX(id), 1), false) FROM reviews;
SELECT setval(pg_get_serial_sequence('message_threads', 'id'), COALESCE(MAX(id), 1), false) FROM message_threads;
SELECT setval(pg_get_serial_sequence('messages', 'id'), COALESCE(MAX(id), 1), false) FROM messages;