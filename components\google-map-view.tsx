"use client"

import { useCallback, useState } from "react"
import { APIProvider, Map, AdvancedMarker, Pin, InfoWindow } from "@vis.gl/react-google-maps"
import type { BusinessWithDetails } from "@/lib/types"
import { getBusinessCoordinates, formatDistance } from "@/lib/geocoding"
import { MapPin, Star } from "lucide-react"
import { MapTroubleshooting } from "./map-troubleshooting"

interface GoogleMapViewProps {
  businesses: (BusinessWithDetails & { distance?: number })[]
  center?: { lat: number; lng: number }
  zoom?: number
  hoveredBusinessId?: string | null
  onBusinessHover?: (businessId: string | null) => void
}

interface CustomMarkerProps {
  position: { lat: number; lng: number }
  business: BusinessWithDetails & { distance?: number }
  isHovered?: boolean
  onHover?: (businessId: string | null) => void
}

// Custom marker component that matches our UI design
function CustomMarker({ position, business, isHovered, onHover }: CustomMarkerProps) {
  const [infoWindowShown, setInfoWindowShown] = useState(false)

  const handleMarkerClick = useCallback(() => {
    setInfoWindowShown(isShown => !isShown)
  }, [])

  const handleClose = useCallback(() => {
    setInfoWindowShown(false)
  }, [])

  const handleMouseEnter = useCallback(() => {
    onHover?.(business.id)
  }, [business.id, onHover])

  const handleMouseLeave = useCallback(() => {
    onHover?.(null)
  }, [onHover])

  return (
    <>
      <AdvancedMarker
        position={position}
        onClick={handleMarkerClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className="relative group cursor-pointer">
          <div className={`
            p-2 rounded-full shadow-lg transition-all duration-200
            ${isHovered
              ? 'bg-blue-gradient glow-blue-strong scale-110'
              : 'bg-blue-gradient glow-blue hover:glow-blue-strong hover:scale-105'
            }
          `}>
            <MapPin className="h-4 w-4 text-white" />
          </div>
        </div>
      </AdvancedMarker>

      {infoWindowShown && (
        <InfoWindow
          position={position}
          onClose={handleClose}
        >
          <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-3 shadow-xl min-w-[200px]">
            <h4 className="text-white font-medium text-sm mb-1">{business.name}</h4>
            <div className="flex items-center gap-1 mb-1">
              <div className="flex">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < Math.floor(business.avg_rating || 0)
                        ? "text-yellow-400 fill-current"
                        : "text-neutral-600"
                    }`}
                  />
                ))}
              </div>
              <span className="text-neutral-400 text-xs ml-1">
                {business.avg_rating?.toFixed(1) || 'N/A'}
              </span>
            </div>
            <p className="text-neutral-400 text-xs">
              {business.location?.city}, {business.location?.state}
            </p>
            {business.distance && (
              <p className="text-blue-400 text-xs mt-1">
                {formatDistance(business.distance)}
              </p>
            )}
            {business.services?.[0]?.service?.name && (
              <p className="text-xs text-blue-400 mt-1">
                {business.services[0].service.name}
              </p>
            )}
          </div>
        </InfoWindow>
      )}
    </>
  )
}

// Dark theme map styles to match your fallback design
const darkMapStyles = [
  // Base map styling - dark background
  {
    "elementType": "geometry",
    "stylers": [{ "color": "#171717" }] // neutral-900
  },
  {
    "elementType": "labels",
    "stylers": [{ "visibility": "off" }] // Hide most labels for cleaner look
  },
  {
    "elementType": "labels.text.fill",
    "stylers": [{ "color": "#525252" }] // neutral-600
  },
  {
    "elementType": "labels.text.stroke",
    "stylers": [{ "color": "#171717" }] // neutral-900
  },
  // Roads - subtle grid-like appearance
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [{ "color": "#262626" }] // neutral-800
  },
  {
    "featureType": "road",
    "elementType": "geometry.stroke",
    "stylers": [{ "color": "#404040" }] // neutral-700
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry",
    "stylers": [{ "color": "#404040" }] // neutral-700
  },
  {
    "featureType": "road.arterial",
    "elementType": "geometry",
    "stylers": [{ "color": "#262626" }] // neutral-800
  },
  {
    "featureType": "road.local",
    "elementType": "geometry",
    "stylers": [{ "color": "#262626" }] // neutral-800
  },
  // Water - dark to match theme
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [{ "color": "#0a0a0a" }] // Very dark
  },
  // Administrative boundaries - subtle grid lines
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [{ "color": "#404040" }, { "weight": 0.5 }] // neutral-700
  },
  // Parks and natural features
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [{ "color": "#1a1a1a" }] // Slightly lighter than base
  },
  {
    "featureType": "poi",
    "stylers": [{ "visibility": "off" }] // Hide points of interest
  },
  // Transit - hide for cleaner look
  {
    "featureType": "transit",
    "stylers": [{ "visibility": "off" }]
  }
]

// Main map component using modern @vis.gl/react-google-maps
function MapComponent({ businesses, center, zoom = 12, hoveredBusinessId, onBusinessHover }: GoogleMapViewProps) {
  // Calculate center if not provided
  const mapCenter = center || { lat: 33.4484, lng: -112.0740 } // Default to Phoenix

  return (
    <div className="relative w-full h-full">
      <Map
        mapId="bf51a910020fa25a"
        defaultCenter={mapCenter}
        defaultZoom={zoom}
        style={{ width: '100%', height: '100%' }}
        gestureHandling="greedy"
        disableDefaultUI={true}
        zoomControl={true}
        mapTypeControl={false}
        streetViewControl={false}
        fullscreenControl={false}
        styles={darkMapStyles}
        backgroundColor="#171717"
      >
        {/* Render custom markers */}
        {businesses.map((business) => {
          const coordinates = getBusinessCoordinates(business.location)
          if (!coordinates) return null

          return (
            <CustomMarker
              key={business.id}
              position={{ lat: coordinates.lat, lng: coordinates.lng }}
              business={business}
              isHovered={hoveredBusinessId === business.id}
              onHover={onBusinessHover}
            />
          )
        })}
      </Map>
    </div>
  )
}

// Main exported component using modern @vis.gl/react-google-maps
export function GoogleMapView({ businesses, center, zoom, hoveredBusinessId, onBusinessHover }: GoogleMapViewProps) {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

  // Check if API key is missing or still placeholder
  if (!apiKey || apiKey === 'your_google_maps_api_key_here') {
    return (
      <div className="w-full h-full bg-neutral-900 border border-neutral-800 rounded-lg flex items-center justify-center">
        <div className="text-center p-6">
          <MapPin className="h-8 w-8 text-yellow-400 mx-auto mb-3" />
          <h3 className="text-yellow-400 text-sm font-medium mb-2">Google Maps Setup Required</h3>
          <div className="text-neutral-400 text-xs space-y-1 max-w-sm">
            <p>To enable interactive maps:</p>
            <ol className="text-left space-y-1 mt-2">
              <li>1. Get a Google Maps API key</li>
              <li>2. Enable Maps JavaScript API</li>
              <li>3. Add key to .env.local</li>
              <li>4. Restart the dev server</li>
            </ol>
            <p className="mt-3 text-neutral-500">See GOOGLE_MAPS_SETUP.md for details</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      <APIProvider apiKey={apiKey}>
        <MapComponent
          businesses={businesses}
          center={center}
          zoom={zoom}
          hoveredBusinessId={hoveredBusinessId}
          onBusinessHover={onBusinessHover}
        />
      </APIProvider>
    </div>
  )
}
