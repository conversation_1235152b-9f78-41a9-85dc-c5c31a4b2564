#!/usr/bin/env node

/**
 * Google Maps Setup Helper
 * Helps developers set up Google Maps API key
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function main() {
  console.log('\n🗺️  Google Maps Setup Helper\n')
  console.log('This script will help you set up Google Maps integration.\n')

  // Check if .env.local exists
  const envPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env.local file not found!')
    console.log('Please create a .env.local file first.')
    rl.close()
    return
  }

  // Read current .env.local
  const envContent = fs.readFileSync(envPath, 'utf8')
  
  // Check current API key status
  const hasApiKeyLine = envContent.includes('NEXT_PUBLIC_GOOGLE_MAPS_API_KEY')
  const hasPlaceholder = envContent.includes('your_google_maps_api_key_here')
  
  if (hasApiKeyLine && !hasPlaceholder) {
    console.log('✅ Google Maps API key appears to be already configured!')
    console.log('If you\'re still seeing errors, check that:')
    console.log('  1. The API key is valid')
    console.log('  2. Maps JavaScript API is enabled')
    console.log('  3. Your domain is whitelisted')
    console.log('  4. You\'ve restarted the dev server')
    rl.close()
    return
  }

  console.log('📋 To get your Google Maps API key:')
  console.log('  1. Go to https://console.cloud.google.com/')
  console.log('  2. Create or select a project')
  console.log('  3. Enable "Maps JavaScript API" and "Geocoding API"')
  console.log('  4. Go to Credentials → Create Credentials → API Key')
  console.log('  5. Restrict the key to your domain for security')
  console.log('')

  const hasKey = await question('Do you have a Google Maps API key? (y/n): ')
  
  if (hasKey.toLowerCase() !== 'y') {
    console.log('\n📖 Please follow the setup guide in GOOGLE_MAPS_SETUP.md')
    console.log('   Then run this script again when you have your API key.')
    rl.close()
    return
  }

  const apiKey = await question('\nEnter your Google Maps API key: ')
  
  if (!apiKey || apiKey.trim().length < 10) {
    console.log('❌ Invalid API key. Please try again.')
    rl.close()
    return
  }

  // Update .env.local
  let newEnvContent = envContent
  
  if (hasApiKeyLine) {
    // Replace existing line
    newEnvContent = newEnvContent.replace(
      /NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=.*/,
      `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${apiKey.trim()}`
    )
  } else {
    // Add new line
    newEnvContent += `\n# Google Maps Configuration\nNEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${apiKey.trim()}\n`
  }

  try {
    fs.writeFileSync(envPath, newEnvContent)
    console.log('\n✅ API key saved to .env.local')
    console.log('\n🔄 Please restart your development server:')
    console.log('   npm run dev')
    console.log('\n🎉 Your maps should now work!')
  } catch (error) {
    console.log('\n❌ Failed to update .env.local:', error.message)
  }

  rl.close()
}

main().catch(console.error)
