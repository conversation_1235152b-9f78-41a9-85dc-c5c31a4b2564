"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import type { Business, Lead } from "@/lib/types"
import { MessageSquare, Phone, Mail, Calendar } from "lucide-react"

interface LeadsTabProps {
  business: Business
}

export function LeadsTab({ business }: LeadsTabProps) {
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchLeads()
  }, [business.id])

  const fetchLeads = async () => {
    try {
      const { data, error } = await supabase
        .from("leads")
        .select("*")
        .eq("business_id", business.id)
        .order("created_at", { ascending: false })

      if (error) throw error
      setLeads(data || [])
    } catch (error) {
      console.error("Error fetching leads:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getLeadAge = (dateString: string) => {
    const now = new Date()
    const leadDate = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - leadDate.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Quote Requests
        </CardTitle>
        <CardDescription className="text-neutral-400">
          Manage customer inquiries and quote requests for your services.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <p className="text-neutral-400">Loading leads...</p>
          </div>
        ) : leads.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
            <p className="text-neutral-400">No quote requests yet</p>
            <p className="text-neutral-500 text-sm">
              Customer inquiries will appear here when they request quotes from your business
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {leads.map((lead) => (
              <div key={lead.id} className="bg-neutral-800 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-white font-medium text-lg">{lead.customer_name}</h3>
                    <div className="flex items-center gap-4 mt-1">
                      {lead.customer_email && (
                        <div className="flex items-center gap-1 text-neutral-400 text-sm">
                          <Mail className="h-3 w-3" />
                          <a href={`mailto:${lead.customer_email}`} className="hover:text-blue-400 transition-colors">
                            {lead.customer_email}
                          </a>
                        </div>
                      )}
                      {lead.customer_phone && (
                        <div className="flex items-center gap-1 text-neutral-400 text-sm">
                          <Phone className="h-3 w-3" />
                          <a href={`tel:${lead.customer_phone}`} className="hover:text-blue-400 transition-colors">
                            {lead.customer_phone}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className="bg-green-500/10 text-green-400 border-green-500/20 mb-1">New Lead</Badge>
                    <div className="flex items-center gap-1 text-neutral-500 text-xs">
                      <Calendar className="h-3 w-3" />
                      {getLeadAge(lead.created_at)}
                    </div>
                  </div>
                </div>

                {lead.message && (
                  <div className="mb-4">
                    <h4 className="text-white text-sm font-medium mb-2">Message:</h4>
                    <p className="text-neutral-300 bg-neutral-900 rounded p-3 text-sm">{lead.message}</p>
                  </div>
                )}

                <div className="flex items-center justify-between pt-3 border-t border-neutral-700">
                  <span className="text-neutral-500 text-xs">Received {formatDate(lead.created_at)}</span>
                  <div className="flex gap-2">
                    {lead.customer_email && (
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
                        asChild
                      >
                        <a href={`mailto:${lead.customer_email}?subject=Quote Request Response`}>
                          <Mail className="h-3 w-3 mr-1" />
                          Email
                        </a>
                      </Button>
                    )}
                    {lead.customer_phone && (
                      <Button size="sm" className="bg-blue-gradient-hover" asChild>
                        <a href={`tel:${lead.customer_phone}`}>
                          <Phone className="h-3 w-3 mr-1" />
                          Call
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
