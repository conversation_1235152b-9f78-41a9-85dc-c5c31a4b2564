-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE galleries ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Allow public read access on most data
CREATE POLICY "Public businesses are viewable by everyone." ON businesses FOR SELECT USING ( true );
CREATE POLICY "Public services are viewable by everyone." ON services FOR SELECT USING ( true );
CREATE POLICY "Public business_services are viewable by everyone." ON business_services FOR SELECT USING ( true );
CREATE POLICY "Public reviews are viewable by everyone." ON reviews FOR SELECT USING ( true );
CREATE POLICY "Public galleries are viewable by everyone." ON galleries FOR SELECT USING ( true );
CREATE POLICY "Public profiles are viewable by everyone." ON profiles FOR SELECT USING ( true );

-- Allow users to manage their own profile
CREATE POLICY "Users can insert their own profile." ON profiles FOR INSERT WITH CHECK ( auth.uid() = id );
CREATE POLICY "Users can update their own profile." ON profiles FOR UPDATE USING ( auth.uid() = id );

-- Allow business owners to manage their own business listings
CREATE POLICY "Owners can insert their own business." ON businesses FOR INSERT WITH CHECK ( auth.uid() = owner_id );
CREATE POLICY "Owners can update their own business." ON businesses FOR UPDATE USING ( auth.uid() = owner_id );
CREATE POLICY "Owners can delete their own business." ON businesses FOR DELETE USING ( auth.uid() = owner_id );

-- Allow owners to manage their own gallery images
CREATE POLICY "Owners can insert into their own gallery." ON galleries FOR INSERT WITH CHECK (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);
CREATE POLICY "Owners can manage their own gallery." ON galleries FOR ALL USING (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);

-- Allow authenticated users to write and manage their own reviews
CREATE POLICY "Authenticated users can create reviews." ON reviews FOR INSERT WITH CHECK ( auth.uid() = user_id );
CREATE POLICY "Users can update their own reviews." ON reviews FOR UPDATE USING ( auth.uid() = user_id );

-- Business services policies
CREATE POLICY "Owners can manage their business services." ON business_services FOR ALL USING (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);
CREATE POLICY "Owners can insert their business services." ON business_services FOR INSERT WITH CHECK (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);

-- Subscriptions policies
CREATE POLICY "Owners can view their own subscriptions." ON subscriptions FOR SELECT USING (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);
CREATE POLICY "Owners can manage their own subscriptions." ON subscriptions FOR ALL USING (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);

-- Leads policies
CREATE POLICY "Anyone can create leads." ON leads FOR INSERT WITH CHECK ( true );
CREATE POLICY "Business owners can view their leads." ON leads FOR SELECT USING (
  auth.uid() = (SELECT owner_id FROM businesses WHERE id = business_id)
);
