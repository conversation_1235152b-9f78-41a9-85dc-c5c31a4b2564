"use client"

import { useEffect, useState } from "react"
import { useUser } from "@/hooks/use-user"
import { supabase } from "@/lib/supabase"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Loader2 } from "lucide-react"
import type { Business } from "@/lib/types"

export default function DashboardPage() {
  const { user } = useUser()
  const [business, setBusiness] = useState<Business | null>(null)
  const [businessLoading, setBusinessLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchBusiness()
    }
  }, [user])

  const fetchBusiness = async () => {
    try {
      setBusinessLoading(true)

      if (!supabase) {
        console.log("Supabase not configured, skipping business fetch")
        setBusinessLoading(false)
        return
      }

      if (!user) {
        console.log("No user found, skipping business fetch")
        setBusinessLoading(false)
        return
      }

      // Get the business for the authenticated user
      const { data, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("owner_id", user.id)
        .limit(1)

      if (error) {
        console.log("Error fetching business:", error)
        setBusiness(null)
      } else if (data && data.length > 0) {
        setBusiness(data[0])
      } else {
        console.log("No business found, will show onboarding")
        setBusiness(null)
      }
    } catch (error) {
      console.log("Business fetch error, will show onboarding:", error)
      setBusiness(null)
    } finally {
      setBusinessLoading(false)
    }
  }

  // Show loading while fetching business
  if (businessLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return <DashboardLayout initialBusiness={business} />
}
