"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { ChevronDown, ChevronUp } from "lucide-react"

export function ContactFAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: "How quickly will I receive a response?",
      answer:
        "We typically respond to all inquiries within 24 hours during business days. For urgent technical issues, we aim to respond within 4-6 hours. Premium business customers receive priority support.",
    },
    {
      question: "I'm having trouble with my business listing. Who should I contact?",
      answer:
        "For business listing issues, please use our contact form and select 'Business Account Support' as the category. Include your business name and account email for faster assistance.",
    },
    {
      question: "How do I report inappropriate content or fake reviews?",
      answer:
        "You can report inappropriate content by contacting us with the 'Customer Support' category. Please include the specific business name and details about the content you're reporting.",
    },
    {
      question: "Can I schedule a phone call to discuss my business needs?",
      answer:
        "Yes! Premium business customers can schedule consultation calls. Contact us through the form or call our business support line to arrange a convenient time.",
    },
    {
      question: "I'm a customer looking for pressure washing services. Can you recommend someone?",
      answer:
        "While we don't make specific recommendations, our search and filter tools are designed to help you find the best-rated businesses in your area. Use our search page to explore options.",
    },
    {
      question: "How do I delete my account or business listing?",
      answer:
        "Account deletion requests must be submitted through our contact form for security reasons. Select 'Business Account Support' and we'll guide you through the process.",
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-16 px-4 bg-neutral-900/50">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          <p className="text-neutral-400 max-w-2xl mx-auto">
            Quick answers to common questions. Can't find what you're looking for? Use the contact form above.
          </p>
        </div>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-neutral-800/50 transition-colors"
                >
                  <h3 className="text-white font-medium pr-4">{faq.question}</h3>
                  {openIndex === index ? (
                    <ChevronUp className="h-5 w-5 text-blue-400 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-neutral-400 flex-shrink-0" />
                  )}
                </button>
                {openIndex === index && (
                  <div className="px-6 pb-6">
                    <p className="text-neutral-400 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
