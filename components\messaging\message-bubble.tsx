"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  User, 
  Building2, 
  Check, 
  CheckCheck, 
  Download,
  FileText,
  Image as ImageIcon
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import type { Message } from "@/lib/types"

interface MessageBubbleProps {
  message: Message
  isOwn: boolean
}

export function MessageBubble({ message, isOwn }: MessageBubbleProps) {
  const getFileIcon = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase()
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return ImageIcon
    }
    return FileText
  }

  const getFileName = (url: string) => {
    return url.split('/').pop() || 'Unknown file'
  }

  return (
    <div className={`flex gap-3 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
      {/* Avatar */}
      <Avatar className="h-8 w-8 flex-shrink-0">
        <AvatarImage src={message.author?.avatar_url} />
        <AvatarFallback className="bg-neutral-700 text-white text-xs">
          {message.author?.full_name ? (
            message.author.full_name.charAt(0).toUpperCase()
          ) : isOwn ? (
            <User className="h-4 w-4" />
          ) : (
            <Building2 className="h-4 w-4" />
          )}
        </AvatarFallback>
      </Avatar>

      {/* Message Content */}
      <div className={`flex-1 max-w-[70%] ${isOwn ? 'items-end' : 'items-start'} flex flex-col`}>
        {/* Author and Time */}
        <div className={`flex items-center gap-2 mb-1 ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
          <span className="text-sm font-medium text-white">
            {message.author?.full_name || 'Unknown User'}
          </span>
          <span className="text-xs text-neutral-500">
            {formatDistanceToNow(new Date(message.sent_at), { addSuffix: true })}
          </span>
        </div>

        {/* Message Bubble */}
        <div
          className={`rounded-lg px-4 py-2 max-w-full ${
            isOwn
              ? 'bg-blue-600 text-white rounded-br-sm'
              : 'bg-neutral-800 text-neutral-100 rounded-bl-sm'
          }`}
        >
          {/* Text Content */}
          <p className="whitespace-pre-wrap break-words">{message.content}</p>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-3 space-y-2">
              {message.attachments.map((attachment, index) => {
                const FileIcon = getFileIcon(attachment)
                const fileName = getFileName(attachment)
                const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(
                  attachment.split('.').pop()?.toLowerCase() || ''
                )

                return (
                  <div key={index} className="border border-neutral-600 rounded-lg p-2">
                    {isImage ? (
                      <div className="space-y-2">
                        <img
                          src={attachment}
                          alt={fileName}
                          className="max-w-full h-auto rounded max-h-64 object-cover"
                        />
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-neutral-300">{fileName}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => window.open(attachment, '_blank')}
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileIcon className="h-4 w-4 text-neutral-400" />
                          <span className="text-sm text-neutral-300">{fileName}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => window.open(attachment, '_blank')}
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Message Status */}
        {isOwn && (
          <div className="flex items-center gap-1 mt-1">
            {message.read_at ? (
              <CheckCheck className="h-3 w-3 text-blue-400" />
            ) : (
              <Check className="h-3 w-3 text-neutral-500" />
            )}
            <span className="text-xs text-neutral-500">
              {message.read_at ? 'Read' : 'Delivered'}
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
