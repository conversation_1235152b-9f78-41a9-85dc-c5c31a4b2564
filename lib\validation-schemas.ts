import { z } from 'zod'

// Common validation patterns
const emailSchema = z.string()
  .email('Please enter a valid email address')
  .min(1, 'Email is required')
  .max(254, 'Email is too long')

const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password is too long')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Password must contain at least one uppercase letter, one lowercase letter, and one number')

const phoneSchema = z.string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
  .optional()
  .or(z.literal(''))

const urlSchema = z.string()
  .url('Please enter a valid URL')
  .optional()
  .or(z.literal(''))

const slugSchema = z.string()
  .min(1, 'Slug is required')
  .max(100, 'Slug is too long')
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 
    'Slug can only contain lowercase letters, numbers, and hyphens')

// User schemas
export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Full name can only contain letters and spaces'),
  acceptTerms: z.boolean()
    .refine(val => val === true, 'You must accept the terms and conditions')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
})

export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional()
})

export const forgotPasswordSchema = z.object({
  email: emailSchema
})

export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string(),
  token: z.string().min(1, 'Reset token is required')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
})

export const updateProfileSchema = z.object({
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Full name can only contain letters and spaces'),
  email: emailSchema,
  phone: phoneSchema,
  bio: z.string()
    .max(500, 'Bio is too long')
    .optional()
    .or(z.literal('')),
  location: z.string()
    .max(100, 'Location is too long')
    .optional()
    .or(z.literal(''))
})

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
})

// Business schemas
export const businessSchema = z.object({
  name: z.string()
    .min(2, 'Business name must be at least 2 characters')
    .max(100, 'Business name is too long'),
  slug: slugSchema,
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description is too long'),
  address: z.string()
    .min(5, 'Address must be at least 5 characters')
    .max(200, 'Address is too long'),
  city: z.string()
    .min(2, 'City must be at least 2 characters')
    .max(50, 'City is too long'),
  state: z.string()
    .min(2, 'State must be at least 2 characters')
    .max(50, 'State is too long'),
  zipCode: z.string()
    .regex(/^\d{5}(-\d{4})?$/, 'Please enter a valid ZIP code'),
  phone: z.string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),
  email: emailSchema.optional().or(z.literal('')),
  website: urlSchema,
  category: z.string()
    .min(1, 'Category is required'),
  services: z.array(z.string())
    .min(1, 'At least one service is required'),
  serviceAreas: z.array(z.string())
    .min(1, 'At least one service area is required'),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  businessHours: z.object({
    monday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    tuesday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    wednesday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    thursday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    friday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    saturday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional(),
    sunday: z.object({
      open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      closed: z.boolean().optional()
    }).optional()
  }).optional()
})

export const updateBusinessSchema = businessSchema.partial().extend({
  id: z.string().uuid('Invalid business ID')
})

// Review schemas
export const reviewSchema = z.object({
  businessId: z.string().uuid('Invalid business ID'),
  rating: z.number()
    .int('Rating must be a whole number')
    .min(1, 'Rating must be at least 1')
    .max(5, 'Rating cannot be more than 5'),
  content: z.string()
    .max(2000, 'Review content is too long')
    .optional()
    .or(z.literal(''))
})

export const updateReviewSchema = z.object({
  rating: z.number()
    .int('Rating must be a whole number')
    .min(1, 'Rating must be at least 1')
    .max(5, 'Rating cannot be more than 5')
    .optional(),
  content: z.string()
    .max(2000, 'Review content is too long')
    .optional()
}).refine(data => data.rating !== undefined || data.content !== undefined, {
  message: 'At least one field (rating or content) must be provided'
})

export const reportReviewSchema = z.object({
  reason: z.enum(['spam', 'inappropriate', 'fake', 'offensive', 'other'], {
    required_error: 'Report reason is required',
    invalid_type_error: 'Invalid report reason'
  }),
  description: z.string()
    .max(1000, 'Description is too long')
    .optional()
    .or(z.literal(''))
})

// Quote request schemas
export const quoteRequestSchema = z.object({
  businessId: z.string().uuid('Invalid business ID'),
  customerName: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long'),
  customerEmail: emailSchema,
  customerPhone: phoneSchema,
  serviceType: z.string()
    .min(1, 'Service type is required'),
  propertyType: z.enum(['residential', 'commercial'], {
    required_error: 'Property type is required'
  }),
  propertySize: z.string()
    .min(1, 'Property size is required'),
  address: z.string()
    .min(5, 'Address must be at least 5 characters')
    .max(200, 'Address is too long'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description is too long'),
  preferredDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Please enter a valid date')
    .optional()
    .or(z.literal('')),
  urgency: z.enum(['low', 'medium', 'high'], {
    required_error: 'Urgency level is required'
  })
})

// Contact form schemas
export const contactFormSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name is too long'),
  email: emailSchema,
  subject: z.string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject is too long'),
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message is too long'),
  category: z.enum(['general', 'support', 'business', 'technical'], {
    required_error: 'Category is required'
  })
})

// Search schemas
export const searchSchema = z.object({
  query: z.string()
    .max(100, 'Search query is too long')
    .optional()
    .or(z.literal('')),
  location: z.string()
    .max(100, 'Location is too long')
    .optional()
    .or(z.literal('')),
  category: z.string()
    .optional(),
  services: z.array(z.string())
    .optional(),
  radius: z.number()
    .min(1, 'Radius must be at least 1 mile')
    .max(100, 'Radius cannot exceed 100 miles')
    .optional(),
  sortBy: z.enum(['relevance', 'rating', 'distance', 'name'], {
    invalid_type_error: 'Invalid sort option'
  }).optional(),
  page: z.number()
    .int('Page must be a whole number')
    .min(1, 'Page must be at least 1')
    .optional(),
  limit: z.number()
    .int('Limit must be a whole number')
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .optional()
})

// Admin schemas
export const adminUserUpdateSchema = z.object({
  role: z.enum(['user', 'business_owner', 'admin'], {
    required_error: 'Role is required'
  }),
  status: z.enum(['active', 'suspended', 'banned'], {
    required_error: 'Status is required'
  }),
  notes: z.string()
    .max(1000, 'Notes are too long')
    .optional()
    .or(z.literal(''))
})

export const bulkImportSchema = z.object({
  data: z.array(businessSchema),
  overwrite: z.boolean().optional(),
  validateOnly: z.boolean().optional()
})

// File upload schemas
export const fileUploadSchema = z.object({
  file: z.instanceof(File, { message: 'File is required' }),
  type: z.enum(['image', 'document'], {
    required_error: 'File type is required'
  }),
  maxSize: z.number().optional()
}).refine(data => {
  const maxSize = data.maxSize || 5 * 1024 * 1024 // 5MB default
  return data.file.size <= maxSize
}, {
  message: 'File size exceeds maximum allowed size'
}).refine(data => {
  if (data.type === 'image') {
    return ['image/jpeg', 'image/png', 'image/webp'].includes(data.file.type)
  }
  if (data.type === 'document') {
    return ['application/pdf', 'text/csv', 'application/json'].includes(data.file.type)
  }
  return true
}, {
  message: 'Invalid file type'
})

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// Export all schemas for easy access
export const schemas = {
  // User schemas
  signUp: signUpSchema,
  signIn: signInSchema,
  forgotPassword: forgotPasswordSchema,
  resetPassword: resetPasswordSchema,
  updateProfile: updateProfileSchema,
  changePassword: changePasswordSchema,
  
  // Business schemas
  business: businessSchema,
  updateBusiness: updateBusinessSchema,
  
  // Review schemas
  review: reviewSchema,
  updateReview: updateReviewSchema,
  reportReview: reportReviewSchema,
  
  // Quote schemas
  quoteRequest: quoteRequestSchema,
  
  // Contact schemas
  contactForm: contactFormSchema,
  
  // Search schemas
  search: searchSchema,
  
  // Admin schemas
  adminUserUpdate: adminUserUpdateSchema,
  bulkImport: bulkImportSchema,
  
  // File schemas
  fileUpload: fileUploadSchema,
  
  // Pagination
  pagination: paginationSchema
}
