"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Download,
  Info
} from "lucide-react"

interface ImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    index: number
    business: string
    error: string
  }>
}

export function BulkImportForm() {
  const [file, setFile] = useState<File | null>(null)
  const [ownerId, setOwnerId] = useState("")
  const [importing, setImporting] = useState(false)
  const [creatingAdmin, setCreatingAdmin] = useState(false)
  const [result, setResult] = useState<ImportResult | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Auto-load existing profile on component mount
  useEffect(() => {
    const loadExistingProfile = async () => {
      try {
        const response = await fetch('/api/admin/get-existing-profile')
        const data = await response.json()

        if (response.ok && data.profile) {
          setOwnerId(data.profile.id)
        }
      } catch (error) {
        console.error('Failed to load existing profile:', error)
      }
    }

    if (!ownerId) {
      loadExistingProfile()
    }
  }, [ownerId])

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile.type === 'application/json' || selectedFile.name.endsWith('.json') || 
        selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
      setFile(selectedFile)
      setResult(null)
    } else {
      alert('Please select a JSON or CSV file')
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const droppedFile = e.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  const createDirectoryAdmin = async () => {
    setCreatingAdmin(true)
    try {
      // Since auth is disabled, use the existing profile for bulk imports
      // First, get any existing profile from the database
      const response = await fetch('/api/admin/get-existing-profile', {
        method: 'GET'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get existing profile')
      }

      if (data.profile) {
        setOwnerId(data.profile.id)
        alert(`Using existing profile for bulk imports: ${data.profile.full_name} (${data.profile.id})`)
      } else {
        throw new Error('No existing profiles found. Please create a user profile first.')
      }
    } catch (error) {
      console.error('Profile retrieval error:', error)
      alert(error instanceof Error ? error.message : 'Failed to get existing profile')
    } finally {
      setCreatingAdmin(false)
    }
  }

  const handleImport = async () => {
    if (!file || !ownerId.trim()) {
      alert('Please select a file and enter an owner ID')
      return
    }

    setImporting(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('ownerId', ownerId.trim())

      const response = await fetch('/api/admin/bulk-import', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Import failed')
      }

      setResult(data)
    } catch (error) {
      console.error('Import error:', error)
      alert(error instanceof Error ? error.message : 'Import failed')
    } finally {
      setImporting(false)
    }
  }

  const downloadSampleJSON = () => {
    const sampleData = [
      {
        "name": "Sample Pressure Washing Co",
        "address": "123 Main St, Charlotte, NC 28205, United States",
        "rating": 4.8,
        "totalRatings": 150,
        "status": "Open",
        "placeId": "ChIJSamplePlaceId123",
        "types": ["point_of_interest", "establishment"],
        "latitude": 35.1980212,
        "longitude": -80.7962048,
        "businessStatus": "OPERATIONAL",
        "phoneNumber": "(*************",
        "website": "https://www.samplepressurewashing.com",
        "weekdayText": [
          "Monday: 8:00 AM – 5:00 PM",
          "Tuesday: 8:00 AM – 5:00 PM",
          "Wednesday: 8:00 AM – 5:00 PM",
          "Thursday: 8:00 AM – 5:00 PM",
          "Friday: 8:00 AM – 5:00 PM",
          "Saturday: 8:00 AM – 3:00 PM",
          "Sunday: Closed"
        ],
        "photoUrls": [
          "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800",
          "https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=800"
        ],
        "reviews": [
          {
            "author_name": "John Doe",
            "rating": 5,
            "relative_time_description": "2 weeks ago",
            "text": "Excellent service! My house looks brand new.",
            "time": 1640995200
          }
        ]
      }
    ]

    const blob = new Blob([JSON.stringify(sampleData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sample-businesses.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const downloadSampleCSV = () => {
    const csvContent = `Name	Address	Phone Number	International Phone	Website	Google Maps URL	Rating	Total Ratings	Status	Business Status	Price Level	Price Level Text	Latitude	Longitude	Plus Code	Place ID	Types	Hours	Photo URLs	Icon URL	Recent Reviews Count	Average Recent Rating	Recent Reviews Text
Sample Pressure Washing Co	123 Main St, Charlotte, NC 28205, United States	(*************	******-555-0123	https://www.samplepressurewashing.com	https://maps.google.com/?cid=1234567890	4.8	150	Open	OPERATIONAL		Unknown	35.1980212	-80.7962048	867X56X3+6G	ChIJSamplePlaceId123	point_of_interest, establishment	Monday: 8:00 AM – 5:00 PM; Tuesday: 8:00 AM – 5:00 PM; Wednesday: 8:00 AM – 5:00 PM; Thursday: 8:00 AM – 5:00 PM; Friday: 8:00 AM – 5:00 PM; Saturday: 8:00 AM – 3:00 PM; Sunday: Closed	https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800, https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=800	https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png	3	4.9	★★★★★ John Doe (2 weeks ago): Excellent service! My house looks brand new after the pressure washing.

★★★★★ Jane Smith (1 month ago): Professional and reliable. Highly recommend for driveway cleaning.

★★★★☆ Mike Johnson (3 weeks ago): Good work overall, very satisfied with the results.
Another Pressure Wash	456 Oak Ave, Charlotte, NC 28206, United States	(*************	******-555-0456	https://www.anotherpressurewash.com	https://maps.google.com/?cid=9876543210	4.6	89	Open	OPERATIONAL		Unknown	35.2563679	-80.8173018	867X754M+G3	ChIJAnotherPlaceId456	point_of_interest, establishment	Monday: 7:00 AM – 6:00 PM; Tuesday: 7:00 AM – 6:00 PM; Wednesday: 7:00 AM – 6:00 PM; Thursday: 7:00 AM – 6:00 PM; Friday: 7:00 AM – 6:00 PM; Saturday: 8:00 AM – 4:00 PM; Sunday: Closed	https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=800	https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png	2	4.5	★★★★★ Sarah Wilson (1 week ago): Great deck cleaning service, very thorough work.

★★★★☆ Tom Brown (2 weeks ago): Good commercial pressure washing, will use again.`

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'sample-businesses.csv'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-400" />
            Import Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-neutral-300 space-y-2">
            <p>• Upload a JSON file containing an array of business objects OR a CSV file with tab-separated values</p>
            <p>• Required fields: Name, Address, Rating, Latitude, Longitude, Total Ratings</p>
            <p>• Optional fields: Phone Number, Website, Business Hours, Reviews, Place ID, Photo URLs</p>
            <p>• CSV format uses tab separators and supports Google Places API export format</p>
            <p>• <span className="text-blue-400 font-medium">Images will be automatically downloaded and uploaded to storage</span></p>
            <p>• The system will automatically extract services and create business profiles</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={downloadSampleJSON}
              className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Sample JSON
            </Button>
            <Button
              variant="outline"
              onClick={downloadSampleCSV}
              className="border-green-500/20 text-green-400 hover:bg-green-500/10"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Sample CSV
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Form */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white">Upload File</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Owner ID Input */}
          <div className="space-y-2">
            <Label htmlFor="ownerId" className="text-white">
              Owner ID <span className="text-red-400">*</span>
            </Label>
            <Input
              id="ownerId"
              value={ownerId}
              onChange={(e) => setOwnerId(e.target.value)}
              placeholder="Enter the user ID who will own these businesses"
              className="bg-neutral-800 border-neutral-700 text-white"
            />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <p className="text-sm text-neutral-400">
                  All imported businesses will be assigned to this user ID.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={createDirectoryAdmin}
                  disabled={creatingAdmin}
                  className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
                >
                  {creatingAdmin ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-400 mr-2" />
                      Creating...
                    </>
                  ) : (
                    'Load Existing Profile'
                  )}
                </Button>
              </div>
              <div className="grid grid-cols-1 gap-2 text-xs">
                <div className="bg-neutral-800 p-2 rounded border border-neutral-700">
                  <p className="text-blue-400 font-medium">Available Profile</p>
                  <p className="text-neutral-300 font-mono">5354596e-3cd1-4992-9824-7c0d88fe8a05</p>
                  <p className="text-neutral-500">Cody Houser</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setOwnerId('5354596e-3cd1-4992-9824-7c0d88fe8a05')}
                    className="mt-1 h-6 px-2 text-xs text-blue-400 hover:bg-blue-500/10"
                  >
                    Use This ID
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver 
                ? 'border-blue-400 bg-blue-500/10' 
                : 'border-neutral-700 hover:border-neutral-600'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.csv"
              onChange={handleFileInputChange}
              className="hidden"
            />
            
            {file ? (
              <div className="space-y-2">
                <FileText className="h-12 w-12 text-green-400 mx-auto" />
                <p className="text-white font-medium">{file.name}</p>
                <p className="text-neutral-400 text-sm">
                  {(file.size / 1024).toFixed(1)} KB
                </p>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    setFile(null)
                  }}
                  className="border-neutral-700 text-neutral-300"
                >
                  Remove
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="h-12 w-12 text-neutral-500 mx-auto" />
                <p className="text-white">Drop your file here or click to browse</p>
                <p className="text-neutral-400 text-sm">Supports JSON and CSV files</p>
              </div>
            )}
          </div>

          {/* Import Button */}
          <Button
            onClick={handleImport}
            disabled={!file || !ownerId.trim() || importing}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {importing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Importing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Import Businesses
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Import Results */}
      {result && (
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-400" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
              )}
              Import Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Badge variant="outline" className="border-green-500/20 text-green-400">
                <CheckCircle className="h-3 w-3 mr-1" />
                {result.imported} Imported
              </Badge>
              {result.failed > 0 && (
                <Badge variant="outline" className="border-red-500/20 text-red-400">
                  <XCircle className="h-3 w-3 mr-1" />
                  {result.failed} Failed
                </Badge>
              )}
            </div>

            {result.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-white font-medium">Errors:</h4>
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {result.errors.map((error, index) => (
                    <Alert key={index} className="bg-red-500/10 border-red-500/20">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <AlertDescription className="text-red-300">
                        <strong>{error.business}</strong>: {error.error}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
