import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 500 }
      )
    }

    // Create a dedicated "Directory Admin" user for bulk imports
    const directoryAdminId = '550e8400-e29b-41d4-a716-446655440099'
    const directoryAdminEmail = '<EMAIL>'
    const directoryAdminName = 'Directory Administrator'

    // Check if user already exists
    const { data: existingProfile } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('id', directoryAdminId)
      .single()

    if (existingProfile) {
      return NextResponse.json({
        success: true,
        user: {
          id: directoryAdminId,
          email: directoryAdminEmail,
          name: directoryAdminName
        },
        message: 'Directory Admin user already exists'
      })
    }

    // Insert the profile directly (since we're not using auth for this admin user)
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: directoryAdminId,
        email: directoryAdminEmail,
        full_name: directoryAdminName,
        phone: '******-ADMIN',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (profileError) {
      console.error('Profile creation error:', profileError)
      return NextResponse.json(
        { error: `Failed to create directory admin: ${profileError.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      user: {
        id: directoryAdminId,
        email: directoryAdminEmail,
        name: directoryAdminName
      },
      message: 'Directory Admin user created successfully'
    })

  } catch (error) {
    console.error('Directory admin creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
