"use client"

import { Badge } from "@/components/ui/badge"
import type { Business } from "@/lib/types"
import type { DashboardTab } from "./dashboard-layout"
import { User, ImageIcon, Wrench, Star, MessageSquare, Crown, Building2 } from "lucide-react"

interface DashboardSidebarProps {
  activeTab: DashboardTab
  onTabChange: (tab: DashboardTab) => void
  business: Business
}

export function DashboardSidebar({ activeTab, onTabChange, business }: DashboardSidebarProps) {
  const tabs = [
    { id: "profile" as DashboardTab, label: "Profile", icon: User },
    { id: "gallery" as DashboardTab, label: "Gallery", icon: ImageIcon },
    { id: "services" as DashboardTab, label: "Services", icon: Wrench },
    { id: "reviews" as DashboardTab, label: "Reviews", icon: Star },
    { id: "leads" as DashboardTab, label: "Leads", icon: MessageSquare },
    { id: "subscription" as DashboardTab, label: "Subscription", icon: Crown },
  ]

  return (
    <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-4">
      {/* Business Info */}
      <div className="mb-6 pb-6 border-b border-neutral-800">
        <div className="flex items-center gap-3 mb-3">
          <div className="bg-blue-gradient p-2 rounded-lg glow-blue">
            <Building2 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-white text-sm">{business.name}</h3>
            <p className="text-neutral-400 text-xs">
              {business.city}, {business.state}
            </p>
          </div>
        </div>

        {business.is_premium && (
          <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">
            <Crown className="h-3 w-3 mr-1" />
            Premium
          </Badge>
        )}
      </div>

      {/* Navigation Tabs */}
      <nav className="space-y-1">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id

          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isActive
                  ? "bg-blue-500/10 text-blue-400 border border-blue-500/20"
                  : "text-neutral-400 hover:text-white hover:bg-neutral-800"
              }`}
            >
              <Icon className="h-4 w-4" />
              {tab.label}
            </button>
          )
        })}
      </nav>
    </div>
  )
}
