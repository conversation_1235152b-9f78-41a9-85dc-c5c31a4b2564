"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  Save,
  Globe,
  Bold,
  Italic,
  Underline,
  LinkIcon,
  ImageIcon,
  Video,
  Type,
  Hash,
  MapPin,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Upload,
} from "lucide-react"

interface BlogEditorProps {
  mode: "create" | "edit"
  postId?: string
}

interface BlogPost {
  id?: string
  title: string
  content: string
  slug: string
  metaTitle: string
  metaDescription: string
  focusKeyword: string
  city: string
  state: string
  status: "draft" | "published" | "scheduled"
  featuredImage?: string
  categories: string[]
  tags: string[]
}

const initialPost: BlogPost = {
  title: "",
  content: "",
  slug: "",
  metaTitle: "",
  metaDescription: "",
  focusKeyword: "",
  city: "",
  state: "",
  status: "draft",
  categories: [],
  tags: [],
}

export function BlogEditor({ mode, postId }: BlogEditorProps) {
  const router = useRouter()
  const [post, setPost] = useState<BlogPost>(initialPost)
  const [seoScore, setSeoScore] = useState(0)
  const [wordCount, setWordCount] = useState(0)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (mode === "edit" && postId) {
      // In a real app, fetch the post data
      setPost({
        ...initialPost,
        title: "10 Things to Know About Pressure Washing in Phoenix, AZ",
        content:
          "Pressure washing in the hot Phoenix sun presents unique challenges. Here are 10 things every homeowner should know...\n\n## 1. The Season Matters\n\nThe intense summer heat can cause cleaning solutions to evaporate too quickly...",
        slug: "pressure-washing-phoenix-az-tips",
        metaTitle: "10 Tips for Pressure Washing in Phoenix, AZ | Expert Guide",
        metaDescription:
          "Pro tips for pressure washing in Phoenix's extreme heat. Learn the best techniques, timing, and solutions for Arizona homes.",
        focusKeyword: "pressure washing phoenix az",
        city: "Phoenix",
        state: "AZ",
        status: "published",
      })
    }
  }, [mode, postId])

  useEffect(() => {
    // Calculate word count
    const words = post.content
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0)
    setWordCount(words.length)

    // Calculate SEO score
    let score = 0

    // Title contains focus keyword
    if (post.focusKeyword && post.title.toLowerCase().includes(post.focusKeyword.toLowerCase())) {
      score += 20
    }

    // Content length (300+ words)
    if (wordCount >= 300) score += 20

    // Meta description length (120-160 chars)
    if (post.metaDescription.length >= 120 && post.metaDescription.length <= 160) {
      score += 20
    }

    // Focus keyword in content
    if (post.focusKeyword && post.content.toLowerCase().includes(post.focusKeyword.toLowerCase())) {
      score += 20
    }

    // Has featured image
    if (post.featuredImage) score += 10

    // URL slug optimization
    if (post.slug && post.slug.length > 0) score += 10

    setSeoScore(score)
  }, [post, wordCount])

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setPost((prev) => ({
      ...prev,
      title,
      slug: generateSlug(title),
      metaTitle: title.length > 60 ? title.substring(0, 57) + "..." : title,
    }))
  }

  const handleSave = async (status: "draft" | "published") => {
    setIsSaving(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    setPost((prev) => ({ ...prev, status }))
    setIsSaving(false)

    if (status === "published") {
      router.push("/admin/blog")
    }
  }

  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return "text-green-400"
    if (score >= 60) return "text-yellow-400"
    return "text-red-400"
  }

  const getSeoScoreLabel = (score: number) => {
    if (score >= 80) return "Good"
    if (score >= 60) return "OK"
    return "Needs Work"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">{mode === "create" ? "Create New Post" : "Edit Post"}</h1>
          <p className="text-neutral-400 mt-1">
            {mode === "create" ? "Write and optimize your blog content" : "Update your blog post"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => handleSave("draft")}
            disabled={isSaving}
            className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "Saving..." : "Save Draft"}
          </Button>
          <Button onClick={() => handleSave("published")} disabled={isSaving} className="bg-blue-gradient-hover">
            <Globe className="h-4 w-4 mr-2" />
            {post.status === "published" ? "Update" : "Publish"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Post Title */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardContent className="p-6">
              <label htmlFor="title" className="block text-sm font-medium text-white mb-2">
                Post Title
              </label>
              <Input
                id="title"
                value={post.title}
                onChange={(e) => handleTitleChange(e.target.value)}
                placeholder="Enter your blog post title..."
                className="text-lg bg-neutral-800 border-neutral-700 text-white"
              />
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">Content</CardTitle>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-neutral-400">{wordCount} words</span>
                  {wordCount >= 300 && <CheckCircle className="h-4 w-4 text-green-400" />}
                </div>
              </div>

              {/* Toolbar */}
              <div className="flex items-center gap-1 p-2 bg-neutral-800 rounded-lg">
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Bold className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Italic className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Underline className="h-4 w-4" />
                </Button>
                <Separator orientation="vertical" className="h-6 bg-neutral-700" />
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <LinkIcon className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Type className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Hash className="h-4 w-4" />
                </Button>
                <Separator orientation="vertical" className="h-6 bg-neutral-700" />
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <ImageIcon className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-neutral-400 hover:text-white">
                  <Video className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6 pt-0">
              <Textarea
                value={post.content}
                onChange={(e) => setPost((prev) => ({ ...prev, content: e.target.value }))}
                placeholder="Write your blog post content here..."
                className="min-h-[400px] bg-neutral-800 border-neutral-700 text-white resize-none"
              />
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Location Targeting */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-blue-400" />
                Location Targeting
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-neutral-400 mb-1">City</label>
                  <Input
                    value={post.city}
                    onChange={(e) => setPost((prev) => ({ ...prev, city: e.target.value }))}
                    placeholder="Phoenix"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-400 mb-1">State</label>
                  <Input
                    value={post.state}
                    onChange={(e) => setPost((prev) => ({ ...prev, state: e.target.value }))}
                    placeholder="AZ"
                    className="bg-neutral-800 border-neutral-700 text-white"
                  />
                </div>
              </div>
              <p className="text-xs text-neutral-500">Leave blank for national posts</p>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
                  SEO Optimization
                </span>
                <Badge className={`${getSeoScoreColor(seoScore)} bg-transparent border-current`}>
                  {getSeoScoreLabel(seoScore)} ({seoScore}%)
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">URL Slug</label>
                <Input
                  value={post.slug}
                  onChange={(e) => setPost((prev) => ({ ...prev, slug: e.target.value }))}
                  className="bg-neutral-800 border-neutral-700 text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">Focus Keyword</label>
                <Input
                  value={post.focusKeyword}
                  onChange={(e) => setPost((prev) => ({ ...prev, focusKeyword: e.target.value }))}
                  placeholder="pressure washing phoenix az"
                  className="bg-neutral-800 border-neutral-700 text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">SEO Title</label>
                <Input
                  value={post.metaTitle}
                  onChange={(e) => setPost((prev) => ({ ...prev, metaTitle: e.target.value }))}
                  className="bg-neutral-800 border-neutral-700 text-white"
                />
                <p className="text-xs text-neutral-500 mt-1">{post.metaTitle.length}/60 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">Meta Description</label>
                <Textarea
                  value={post.metaDescription}
                  onChange={(e) => setPost((prev) => ({ ...prev, metaDescription: e.target.value }))}
                  className="bg-neutral-800 border-neutral-700 text-white"
                  rows={3}
                />
                <p className="text-xs text-neutral-500 mt-1">{post.metaDescription.length}/160 characters</p>
              </div>

              {/* SEO Checklist */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-white">SEO Analysis</h4>
                <div className="space-y-1">
                  <div className="flex items-center text-xs">
                    {post.focusKeyword && post.title.toLowerCase().includes(post.focusKeyword.toLowerCase()) ? (
                      <CheckCircle className="h-3 w-3 text-green-400 mr-2" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-red-400 mr-2" />
                    )}
                    <span className="text-neutral-400">Focus keyword in title</span>
                  </div>
                  <div className="flex items-center text-xs">
                    {wordCount >= 300 ? (
                      <CheckCircle className="h-3 w-3 text-green-400 mr-2" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-red-400 mr-2" />
                    )}
                    <span className="text-neutral-400">Content length (300+ words)</span>
                  </div>
                  <div className="flex items-center text-xs">
                    {post.metaDescription.length >= 120 && post.metaDescription.length <= 160 ? (
                      <CheckCircle className="h-3 w-3 text-green-400 mr-2" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-red-400 mr-2" />
                    )}
                    <span className="text-neutral-400">Meta description length</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Featured Image</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-neutral-700 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 text-neutral-500 mx-auto mb-2" />
                <p className="text-sm text-neutral-400 mb-2">Drop an image here or click to upload</p>
                <Button variant="outline" size="sm" className="border-neutral-700 text-neutral-300 bg-transparent">
                  Choose File
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Categories & Tags */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Categories & Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">Categories</label>
                <Select>
                  <SelectTrigger className="bg-neutral-800 border-neutral-700 text-white">
                    <SelectValue placeholder="Select categories" />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-800 border-neutral-700">
                    <SelectItem value="residential">Residential</SelectItem>
                    <SelectItem value="commercial">Commercial</SelectItem>
                    <SelectItem value="tips">Tips & Guides</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-400 mb-1">Tags</label>
                <Input
                  placeholder="Enter tags separated by commas"
                  className="bg-neutral-800 border-neutral-700 text-white"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
