"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, ExternalLink, CheckCircle, AlertCircle, Copy, Eye, EyeOff } from "lucide-react"

export function MapTroubleshooting() {
  const [showApiKey, setShowApiKey] = useState(false)
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const getApiKeyStatus = () => {
    if (!apiKey) return { status: 'missing', color: 'red', message: 'API key not found' }
    if (apiKey === 'your_google_maps_api_key_here') return { status: 'placeholder', color: 'yellow', message: 'Using placeholder value' }
    if (apiKey.length < 20) return { status: 'invalid', color: 'red', message: 'API key too short' }
    return { status: 'configured', color: 'green', message: 'API key configured' }
  }

  const apiKeyStatus = getApiKeyStatus()

  const troubleshootingSteps = [
    {
      title: "Get Google Maps API Key",
      description: "Create and configure your API key",
      status: apiKeyStatus.status === 'configured' ? 'complete' : 'pending',
      steps: [
        "Go to Google Cloud Console",
        "Create or select a project",
        "Enable Maps JavaScript API",
        "Enable Geocoding API",
        "Create API key",
        "Restrict key to your domain"
      ],
      link: "https://console.cloud.google.com/"
    },
    {
      title: "Add API Key to Environment",
      description: "Configure your local environment",
      status: apiKeyStatus.status === 'configured' ? 'complete' : 'pending',
      steps: [
        "Open .env.local file",
        "Add NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_key",
        "Save the file",
        "Restart development server"
      ]
    },
    {
      title: "Verify Setup",
      description: "Test your configuration",
      status: 'pending',
      steps: [
        "Check browser console for errors",
        "Verify map loads on search page",
        "Test business pin interactions",
        "Confirm geocoding works"
      ]
    }
  ]

  return (
    <Card className="bg-neutral-900 border-neutral-800 max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="bg-yellow-500/10 p-2 rounded-lg">
            <MapPin className="h-5 w-5 text-yellow-400" />
          </div>
          <div>
            <CardTitle className="text-white">Google Maps Setup</CardTitle>
            <p className="text-neutral-400 text-sm">Configure Google Maps integration for interactive maps</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3">Current Status</h3>
          
          <div className="flex items-center justify-between mb-3">
            <span className="text-neutral-300">API Key Status:</span>
            <Badge className={`
              ${apiKeyStatus.color === 'green' ? 'bg-green-500/10 text-green-400 border-green-500/20' : ''}
              ${apiKeyStatus.color === 'yellow' ? 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20' : ''}
              ${apiKeyStatus.color === 'red' ? 'bg-red-500/10 text-red-400 border-red-500/20' : ''}
            `}>
              {apiKeyStatus.status === 'complete' && <CheckCircle className="h-3 w-3 mr-1" />}
              {apiKeyStatus.status !== 'complete' && <AlertCircle className="h-3 w-3 mr-1" />}
              {apiKeyStatus.message}
            </Badge>
          </div>

          {apiKey && (
            <div className="flex items-center gap-2">
              <span className="text-neutral-300 text-sm">Current Key:</span>
              <code className="bg-neutral-900 px-2 py-1 rounded text-xs text-neutral-400 flex-1">
                {showApiKey ? apiKey : '•'.repeat(Math.min(apiKey.length, 40))}
              </code>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowApiKey(!showApiKey)}
                className="text-neutral-400 hover:text-white"
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => copyToClipboard(apiKey)}
                className="text-neutral-400 hover:text-white"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Setup Steps */}
        <div className="space-y-4">
          <h3 className="text-white font-medium">Setup Steps</h3>
          
          {troubleshootingSteps.map((step, index) => (
            <div key={index} className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className={`
                  w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mt-0.5
                  ${step.status === 'complete' 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-neutral-700 text-neutral-400'
                  }
                `}>
                  {step.status === 'complete' ? <CheckCircle className="h-4 w-4" /> : index + 1}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium">{step.title}</h4>
                    {step.link && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => window.open(step.link, '_blank')}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  <p className="text-neutral-400 text-sm mb-3">{step.description}</p>
                  
                  <ul className="space-y-1">
                    {step.steps.map((substep, substepIndex) => (
                      <li key={substepIndex} className="text-neutral-300 text-sm flex items-center gap-2">
                        <div className="w-1 h-1 bg-neutral-500 rounded-full" />
                        {substep}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3">Quick Actions</h3>
          
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.open('https://console.cloud.google.com/', '_blank')}
              className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Google Cloud Console
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => copyToClipboard('NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here')}
              className="border-neutral-700 text-neutral-300 hover:bg-neutral-700"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy Env Variable
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.location.reload()}
              className="border-green-500/20 text-green-400 hover:bg-green-500/10"
            >
              Refresh Page
            </Button>
          </div>
        </div>

        {/* Help Text */}
        <div className="text-center text-neutral-500 text-sm">
          <p>Need help? Check the <code className="bg-neutral-800 px-1 py-0.5 rounded">GOOGLE_MAPS_SETUP.md</code> file for detailed instructions.</p>
        </div>
      </CardContent>
    </Card>
  )
}
