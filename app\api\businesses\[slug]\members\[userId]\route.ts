import { NextRequest, NextResponse } from 'next/server'
import { getBusinessBySlug, getBusinessMembers, updateBusinessMember, removeBusinessMember } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const updateMemberSchema = z.object({
  role: z.enum(['admin', 'member'], { required_error: 'Role must be admin or member' })
})

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string; userId: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug, userId } = await params
    const body = await request.json()
    
    // Validate input
    const validation = updateMemberSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    const { role } = validation.data
    
    // Get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner or admin
    const { members, error: membersError } = await getBusinessMembers(business.id)
    
    if (membersError) {
      return NextResponse.json({ error: membersError.message }, { status: 500 })
    }
    
    const userMember = members.find(m => m.user_id === user.id)
    if (!userMember || (userMember.role !== 'owner' && userMember.role !== 'admin')) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 })
    }
    
    // Cannot modify owner role
    const targetMember = members.find(m => m.user_id === userId)
    if (!targetMember) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }
    
    if (targetMember.role === 'owner') {
      return NextResponse.json({ error: 'Cannot modify owner role' }, { status: 400 })
    }
    
    const { member, error } = await updateBusinessMember(business.id, userId, role)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ member })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string; userId: string }> }
) {
  try {
    const user = await requireAuth()
    const { slug, userId } = await params
    
    // Get the business to verify ownership
    const { business, error: businessError } = await getBusinessBySlug(slug)
    
    if (businessError || !business) {
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }
    
    // Check if user is owner or admin
    const { members, error: membersError } = await getBusinessMembers(business.id)
    
    if (membersError) {
      return NextResponse.json({ error: membersError.message }, { status: 500 })
    }
    
    const userMember = members.find(m => m.user_id === user.id)
    if (!userMember || (userMember.role !== 'owner' && userMember.role !== 'admin')) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 })
    }
    
    // Cannot remove owner
    const targetMember = members.find(m => m.user_id === userId)
    if (!targetMember) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }
    
    if (targetMember.role === 'owner') {
      return NextResponse.json({ error: 'Cannot remove business owner' }, { status: 400 })
    }
    
    const { error } = await removeBusinessMember(business.id, userId)
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}