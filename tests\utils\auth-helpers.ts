/**
 * Authentication Test Helpers
 * Utilities for testing authentication flows
 */

import { createClient } from '@supabase/supabase-js'
import { getTestEnvironment } from '../config/test-environments'

export class AuthTestHelper {
  private supabase
  private environment

  constructor(envName?: string) {
    this.environment = getTestEnvironment(envName)
    this.supabase = createClient(
      this.environment.supabaseUrl,
      this.environment.supabaseServiceKey
    )
  }

  /**
   * Create a test user with email and password
   */
  async createTestUser(userData: {
    email: string
    password: string
    full_name: string
    phone?: string
  }) {
    const { data, error } = await this.supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.full_name,
        phone: userData.phone,
      },
    })

    if (error) {
      throw new Error(`Failed to create test user: ${error.message}`)
    }

    return data.user
  }

  /**
   * Sign in a test user and return session
   */
  async signInTestUser(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      throw new Error(`Failed to sign in test user: ${error.message}`)
    }

    return data
  }

  /**
   * Get a valid session token for API testing
   */
  async getTestUserToken(email: string, password: string): Promise<string> {
    const { session } = await this.signInTestUser(email, password)
    
    if (!session?.access_token) {
      throw new Error('Failed to get access token')
    }

    return session.access_token
  }

  /**
   * Create authorization headers for API requests
   */
  createAuthHeaders(token: string): Record<string, string> {
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  }

  /**
   * Clean up test user
   */
  async deleteTestUser(userId: string) {
    const { error } = await this.supabase.auth.admin.deleteUser(userId)
    
    if (error) {
      console.warn(`Failed to delete test user ${userId}: ${error.message}`)
    }
  }

  /**
   * Sign out current user
   */
  async signOut() {
    const { error } = await this.supabase.auth.signOut()
    
    if (error) {
      throw new Error(`Failed to sign out: ${error.message}`)
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser() {
    const { data: { user }, error } = await this.supabase.auth.getUser()
    
    if (error) {
      throw new Error(`Failed to get current user: ${error.message}`)
    }

    return user
  }

  /**
   * Mock authentication state for unit tests
   */
  mockAuthState(user: any = null, session: any = null) {
    const mockAuth = {
      getUser: vi.fn().mockResolvedValue({ data: { user }, error: null }),
      getSession: vi.fn().mockResolvedValue({ data: { session }, error: null }),
      onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } }),
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    }

    vi.mocked(this.supabase.auth).mockImplementation(() => mockAuth as any)
    
    return mockAuth
  }

  /**
   * Create mock user data
   */
  createMockUser(overrides: Partial<any> = {}) {
    return {
      id: '550e8400-e29b-41d4-a716-446655440000',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test User',
        phone: '******-0100',
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides,
    }
  }

  /**
   * Create mock session data
   */
  createMockSession(user: any = null, overrides: Partial<any> = {}) {
    return {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      expires_at: Date.now() + 3600000,
      token_type: 'bearer',
      user: user || this.createMockUser(),
      ...overrides,
    }
  }

  /**
   * Test user credentials for different roles
   */
  getTestCredentials() {
    return {
      homeowner: {
        email: '<EMAIL>',
        password: 'testpassword123',
        full_name: 'Sarah Johnson',
        phone: '******-0101',
      },
      businessOwner: {
        email: '<EMAIL>',
        password: 'testpassword123',
        full_name: 'Mike Thompson',
        phone: '******-0102',
      },
      admin: {
        email: '<EMAIL>',
        password: 'testpassword123',
        full_name: 'Test Administrator',
        phone: '******-0103',
      },
    }
  }
}