"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Users, Building2, Star, Send, TrendingUp, AlertTriangle, Eye, Trash2 } from "lucide-react"

// Mock data for the chart
const chartData = [
  { name: "Jan", users: 400, businesses: 240 },
  { name: "Feb", users: 300, businesses: 139 },
  { name: "<PERSON>", users: 200, businesses: 980 },
  { name: "Apr", users: 278, businesses: 390 },
  { name: "May", users: 189, businesses: 480 },
  { name: "<PERSON>", users: 239, businesses: 380 },
  { name: "Jul", users: 349, businesses: 430 },
]

// Mock reported reviews data
const reportedReviews = [
  {
    id: "1",
    content: "This is spam!",
    business: "AZ Suds Power Washing",
    reporter: "<EMAIL>",
    reason: "Spam",
    date: "2024-07-19",
  },
  {
    id: "2",
    content: "Inappropriate photo in gallery",
    business: "Aqua Clean Services",
    reporter: "<EMAIL>",
    reason: "Inappropriate Content",
    date: "2024-07-18",
  },
  {
    id: "3",
    content: "Fake review - never used this service",
    business: "Phoenix Pressure Pro",
    reporter: "<EMAIL>",
    reason: "Fake Review",
    date: "2024-07-17",
  },
]

export function AdminDashboardContent() {
  return (
    <>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Dashboard Overview</h1>
        <p className="text-neutral-400">Saturday, July 19, 2025</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Total Users</CardTitle>
            <Users className="h-4 w-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">5,480</div>
            <p className="text-xs text-green-400 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Active Businesses</CardTitle>
            <Building2 className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">1,247</div>
            <p className="text-xs text-green-400 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Total Reviews</CardTitle>
            <Star className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">12,847</div>
            <p className="text-xs text-green-400 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +15% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-neutral-400">Messages Sent</CardTitle>
            <Send className="h-4 w-4 text-purple-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">3,924</div>
            <p className="text-xs text-green-400 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +22% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Growth Chart */}
      <Card className="bg-neutral-900 border-neutral-800 mb-8">
        <CardHeader>
          <CardTitle className="text-white">Platform Growth</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="name" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1F2937",
                  border: "1px solid #374151",
                  borderRadius: "8px",
                  color: "#F9FAFB",
                }}
              />
              <Line
                type="monotone"
                dataKey="users"
                stroke="#3B82F6"
                strokeWidth={2}
                dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                name="Users"
              />
              <Line
                type="monotone"
                dataKey="businesses"
                stroke="#10B981"
                strokeWidth={2}
                dot={{ fill: "#10B981", strokeWidth: 2, r: 4 }}
                name="Businesses"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Recently Reported Reviews */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-white flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-yellow-400" />
            Recently Reported Reviews
          </CardTitle>
          <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">{reportedReviews.length} Pending</Badge>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportedReviews.map((report) => (
              <div
                key={report.id}
                className="flex items-center justify-between p-4 bg-neutral-800 rounded-lg border border-neutral-700"
              >
                <div className="flex-1">
                  <p className="text-white font-medium">"{report.content}"</p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-neutral-400">
                    <span>Business: {report.business}</span>
                    <span>Reported by: {report.reporter}</span>
                    <span>Reason: {report.reason}</span>
                    <span>Date: {report.date}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="text-blue-400 hover:text-blue-300">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-400 hover:text-red-300">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  )
}
