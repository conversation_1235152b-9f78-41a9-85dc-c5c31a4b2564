"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Calendar, Clock, User, Search, ArrowRight, ChevronLeft, ChevronRight } from "lucide-react"

export function BlogList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedCategory, setSelectedCategory] = useState("all")

  const categories = [
    { id: "all", name: "All Articles", count: 24 },
    { id: "tips", name: "Tips & Guides", count: 12 },
    { id: "equipment", name: "Equipment", count: 6 },
    { id: "business", name: "Business", count: 4 },
    { id: "maintenance", name: "Maintenance", count: 2 },
  ]

  const articles = [
    {
      id: 1,
      title: "How to Choose the Right Nozzle for the Job",
      excerpt:
        "A pro's guide to pressure washer nozzles and when to use each type for maximum effectiveness and safety.",
      image: "/placeholder.svg?height=200&width=300",
      category: "tips",
      author: "Mike Rodriguez",
      publishDate: "2025-01-15",
      readTime: "5 min read",
      featured: true,
    },
    {
      id: 2,
      title: "5 Mistakes to Avoid When Washing a Driveway",
      excerpt:
        "Don't damage your concrete! Learn what not to do when pressure washing driveways and how to get perfect results.",
      image: "/placeholder.svg?height=200&width=300",
      category: "tips",
      author: "Sarah Johnson",
      publishDate: "2025-01-12",
      readTime: "4 min read",
      featured: false,
    },
    {
      id: 3,
      title: "Best Pressure Washers for Small Businesses in 2025",
      excerpt:
        "Our comprehensive review of the top pressure washing equipment for entrepreneurs starting their cleaning business.",
      image: "/placeholder.svg?height=200&width=300",
      category: "equipment",
      author: "Tom Wilson",
      publishDate: "2025-01-10",
      readTime: "8 min read",
      featured: true,
    },
    {
      id: 4,
      title: "Soft Washing vs Pressure Washing: When to Use Each",
      excerpt:
        "Understanding the difference between soft washing and pressure washing can save you from costly damage.",
      image: "/placeholder.svg?height=200&width=300",
      category: "tips",
      author: "Lisa Chen",
      publishDate: "2025-01-08",
      readTime: "6 min read",
      featured: false,
    },
    {
      id: 5,
      title: "How to Price Your Pressure Washing Services",
      excerpt: "A complete guide to pricing strategies that will help you maximize profits while staying competitive.",
      image: "/placeholder.svg?height=200&width=300",
      category: "business",
      author: "David Martinez",
      publishDate: "2025-01-05",
      readTime: "7 min read",
      featured: false,
    },
    {
      id: 6,
      title: "Winter Maintenance for Your Pressure Washing Equipment",
      excerpt:
        "Protect your investment with proper winter storage and maintenance techniques for pressure washing equipment.",
      image: "/placeholder.svg?height=200&width=300",
      category: "maintenance",
      author: "Mike Rodriguez",
      publishDate: "2025-01-03",
      readTime: "5 min read",
      featured: false,
    },
  ]

  const filteredArticles = articles.filter((article) => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || article.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const articlesPerPage = 6
  const totalPages = Math.ceil(filteredArticles.length / articlesPerPage)
  const startIndex = (currentPage - 1) * articlesPerPage
  const paginatedArticles = filteredArticles.slice(startIndex, startIndex + articlesPerPage)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          Pressure Washing <span className="bg-blue-gradient bg-clip-text text-white">Tips</span>
        </h1>
        <p className="text-xl text-neutral-400 max-w-3xl mx-auto">
          Expert advice for homeowners and professionals. Learn techniques, avoid mistakes, and grow your business.
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
            <Input
              type="text"
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500"
            />
          </div>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              onClick={() => {
                setSelectedCategory(category.id)
                setCurrentPage(1)
              }}
              variant={selectedCategory === category.id ? "default" : "outline"}
              className={
                selectedCategory === category.id
                  ? "bg-blue-gradient-hover"
                  : "border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
              }
            >
              {category.name}
              <Badge className="ml-2 bg-neutral-700 text-neutral-300 text-xs">{category.count}</Badge>
            </Button>
          ))}
        </div>
      </div>

      {/* Articles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {paginatedArticles.map((article) => (
          <Card key={article.id} className="bg-neutral-900 border-neutral-800 card-hover-blue overflow-hidden">
            <div className="relative">
              <div className="aspect-video bg-neutral-800 overflow-hidden">
                <Image
                  src={article.image || "/placeholder.svg"}
                  alt={article.title}
                  width={300}
                  height={200}
                  className="w-full h-full object-cover transition-transform hover:scale-105"
                />
              </div>
              {article.featured && <Badge className="absolute top-3 left-3 bg-blue-500 text-white">Featured</Badge>}
            </div>

            <CardContent className="p-6">
              {/* Category */}
              <Badge className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs mb-3">
                {categories.find((cat) => cat.id === article.category)?.name}
              </Badge>

              {/* Title */}
              <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">{article.title}</h3>

              {/* Excerpt */}
              <p className="text-neutral-400 text-sm mb-4 line-clamp-3">{article.excerpt}</p>

              {/* Meta Info */}
              <div className="flex items-center gap-4 text-xs text-neutral-500 mb-4">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {article.author}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(article.publishDate)}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {article.readTime}
                </div>
              </div>

              {/* Read More Link */}
              <Link
                href={`/blog/${article.id}`}
                className="inline-flex items-center text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors"
              >
                Read More
                <ArrowRight className="h-3 w-3 ml-1" />
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            variant="outline"
            className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex gap-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                onClick={() => setCurrentPage(page)}
                variant={currentPage === page ? "default" : "outline"}
                className={
                  currentPage === page
                    ? "bg-blue-gradient-hover"
                    : "border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                }
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            variant="outline"
            className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Newsletter Signup */}
      <div className="mt-16 text-center">
        <Card className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20 max-w-2xl mx-auto">
          <CardContent className="p-8">
            <h3 className="text-2xl font-bold text-white mb-4">Stay Updated</h3>
            <p className="text-neutral-400 mb-6">
              Get the latest pressure washing tips, equipment reviews, and business advice delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500"
              />
              <Button className="bg-blue-gradient-hover">Subscribe</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
