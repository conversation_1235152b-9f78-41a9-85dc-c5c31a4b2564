"use client"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { Business } from "@/lib/types"
import { Building2, ArrowRight } from "lucide-react"

interface BusinessOnboardingProps {
  onBusinessCreated: (business: Business) => void
}

export function BusinessOnboarding({ onBusinessCreated }: BusinessOnboardingProps) {
  const router = useRouter()

  const startOnboarding = () => {
    router.push("/onboarding")
  }

  return (
    <div className="w-full px-6 py-12">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-blue-gradient p-4 rounded-full w-16 h-16 mx-auto mb-6 glow-blue">
          <Building2 className="h-8 w-8 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-white mb-4">Welcome to PressureWash Pro!</h1>
        <p className="text-neutral-400 mb-8">
          You need to set up your business profile before accessing the dashboard. This only takes a few minutes.
        </p>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <CardTitle className="text-white">Get Started</CardTitle>
            <CardDescription className="text-neutral-400">
              Complete our quick onboarding flow to create your business profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2 text-neutral-300">
                  <div className="w-2 h-2 bg-blue-400 rounded-full" />
                  Business information
                </div>
                <div className="flex items-center gap-2 text-neutral-300">
                  <div className="w-2 h-2 bg-blue-400 rounded-full" />
                  Contact details
                </div>
                <div className="flex items-center gap-2 text-neutral-300">
                  <div className="w-2 h-2 bg-blue-400 rounded-full" />
                  Service location
                </div>
                <div className="flex items-center gap-2 text-neutral-300">
                  <div className="w-2 h-2 bg-blue-400 rounded-full" />
                  Quick survey
                </div>
              </div>

              <Button onClick={startOnboarding} className="w-full bg-blue-gradient-hover">
                Start Setup Process
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
