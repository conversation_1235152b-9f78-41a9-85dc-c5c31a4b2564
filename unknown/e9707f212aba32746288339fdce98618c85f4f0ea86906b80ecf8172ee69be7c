import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export default function ForBusinessesLoading() {
  return (
    <div className="min-h-screen bg-black">
      {/* Header Skeleton */}
      <div className="border-b border-neutral-800 px-4 py-4">
        <div className="container mx-auto flex items-center justify-between">
          <Skeleton className="h-8 w-40 bg-neutral-800" />
          <div className="flex items-center space-x-6">
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-9 w-20 bg-neutral-800" />
            <Skeleton className="h-9 w-24 bg-neutral-800" />
          </div>
        </div>
      </div>

      {/* Hero Section Skeleton */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Skeleton className="h-6 w-32 mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-16 w-full max-w-4xl mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-6 w-full max-w-3xl mx-auto mb-8 bg-neutral-800" />
          <Skeleton className="h-12 w-64 mx-auto mb-4 bg-neutral-800" />
          <Skeleton className="h-4 w-80 mx-auto bg-neutral-800" />
        </div>
      </section>

      {/* How Our Directory Helps You Skeleton */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-80 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6 text-center">
                  <Skeleton className="h-12 w-12 mx-auto mb-4 bg-neutral-800" />
                  <Skeleton className="h-6 w-32 mx-auto mb-3 bg-neutral-800" />
                  <Skeleton className="h-4 w-full mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-3/4 mx-auto bg-neutral-800" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section Skeleton */}
      <section className="py-16 px-4 bg-neutral-900/30">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-64 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6 text-center">
                  <Skeleton className="h-12 w-16 mx-auto mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-24 mx-auto bg-neutral-800" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section Skeleton */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-80 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="space-y-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="bg-neutral-900 border-neutral-800">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Skeleton className="h-8 w-8 mt-1 bg-neutral-800" />
                      <div className="flex-1">
                        <Skeleton className="h-6 w-48 mb-2 bg-neutral-800" />
                        <Skeleton className="h-4 w-full mb-1 bg-neutral-800" />
                        <Skeleton className="h-4 w-3/4 bg-neutral-800" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="space-y-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="bg-neutral-900 border-neutral-800">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <Skeleton className="h-8 w-8 mt-1 bg-neutral-800" />
                      <div className="flex-1">
                        <Skeleton className="h-6 w-48 mb-2 bg-neutral-800" />
                        <Skeleton className="h-4 w-full mb-1 bg-neutral-800" />
                        <Skeleton className="h-4 w-3/4 bg-neutral-800" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonial Section Skeleton */}
      <section className="py-16 px-4 bg-neutral-900/50">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-80 mx-auto mb-12 bg-neutral-800" />
          <Card className="bg-neutral-900 border-neutral-800 max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <div className="flex justify-center mb-4 space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Skeleton key={star} className="h-6 w-6 bg-neutral-800" />
                ))}
              </div>
              <Skeleton className="h-6 w-full mb-2 bg-neutral-800" />
              <Skeleton className="h-6 w-3/4 mx-auto mb-6 bg-neutral-800" />
              <Skeleton className="h-5 w-48 mx-auto mb-1 bg-neutral-800" />
              <Skeleton className="h-4 w-56 mx-auto mb-1 bg-neutral-800" />
              <Skeleton className="h-4 w-40 mx-auto bg-neutral-800" />
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Pricing Preview Skeleton */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-80 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[1, 2].map((i) => (
              <Card key={i} className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-8">
                  <Skeleton className="h-8 w-32 mb-4 bg-neutral-800" />
                  <Skeleton className="h-12 w-24 mb-6 bg-neutral-800" />
                  <div className="space-y-3 mb-8">
                    {[1, 2, 3, 4].map((j) => (
                      <div key={j} className="flex items-center">
                        <Skeleton className="h-5 w-5 mr-3 bg-neutral-800" />
                        <Skeleton className="h-4 w-48 bg-neutral-800" />
                      </div>
                    ))}
                  </div>
                  <Skeleton className="h-10 w-full bg-neutral-800" />
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-8">
            <Skeleton className="h-4 w-64 mx-auto bg-neutral-800" />
          </div>
        </div>
      </section>

      {/* Final CTA Skeleton */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Skeleton className="h-12 w-96 mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-6 w-full max-w-2xl mx-auto mb-8 bg-neutral-800" />
          <Skeleton className="h-12 w-80 mx-auto mb-6 bg-neutral-800" />
          <div className="flex items-center justify-center space-x-8">
            <Skeleton className="h-4 w-48 bg-neutral-800" />
            <Skeleton className="h-4 w-56 bg-neutral-800" />
          </div>
        </div>
      </section>

      {/* Footer Skeleton */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((i) => (
              <div key={i}>
                <Skeleton className="h-6 w-32 mb-4 bg-neutral-800" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24 bg-neutral-800" />
                  <Skeleton className="h-4 w-28 bg-neutral-800" />
                  <Skeleton className="h-4 w-20 bg-neutral-800" />
                </div>
              </div>
            ))}
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center">
            <Skeleton className="h-4 w-64 mx-auto bg-neutral-800" />
          </div>
        </div>
      </footer>
    </div>
  )
}
