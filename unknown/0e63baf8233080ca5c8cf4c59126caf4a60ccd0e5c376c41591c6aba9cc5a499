"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, MapPin, TrendingUp, AlertCircle } from "lucide-react"

interface BlogPost {
  id: string
  title: string
  status: "published" | "draft" | "scheduled"
  location: string | null
  seoScore: "good" | "ok" | "needs-work"
  author: string
  publishedAt: string | null
  views: number
  slug: string
}

const mockBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "10 Things to Know About Pressure Washing in Phoenix, AZ",
    status: "published",
    location: "Phoenix, AZ",
    seoScore: "good",
    author: "Admin User",
    publishedAt: "2024-01-15",
    views: 1247,
    slug: "pressure-washing-phoenix-az-tips",
  },
  {
    id: "2",
    title: "A Homeowner's Guide to Soft Washing",
    status: "published",
    location: null,
    seoScore: "ok",
    author: "Content Team",
    publishedAt: "2024-01-12",
    views: 892,
    slug: "homeowners-guide-soft-washing",
  },
  {
    id: "3",
    title: "The Best Time of Year to Wash a Roof in Miami, FL",
    status: "draft",
    location: "Miami, FL",
    seoScore: "needs-work",
    author: "Admin User",
    publishedAt: null,
    views: 0,
    slug: "best-time-roof-washing-miami",
  },
  {
    id: "4",
    title: "How to Choose the Right Pressure Washing Nozzle",
    status: "published",
    location: null,
    seoScore: "good",
    author: "Expert Writer",
    publishedAt: "2024-01-10",
    views: 2156,
    slug: "choose-right-pressure-washing-nozzle",
  },
  {
    id: "5",
    title: "Commercial Pressure Washing in Dallas: What Businesses Need to Know",
    status: "scheduled",
    location: "Dallas, TX",
    seoScore: "good",
    author: "Business Team",
    publishedAt: "2024-01-20",
    views: 0,
    slug: "commercial-pressure-washing-dallas",
  },
]

export function BlogManagement() {
  const [posts, setPosts] = useState<BlogPost[]>(mockBlogPosts)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [locationFilter, setLocationFilter] = useState<string>("all")

  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.author.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || post.status === statusFilter
    const matchesLocation =
      locationFilter === "all" ||
      (locationFilter === "national" && !post.location) ||
      (locationFilter === "local" && post.location) ||
      post.location?.toLowerCase().includes(locationFilter.toLowerCase())

    return matchesSearch && matchesStatus && matchesLocation
  })

  const getStatusBadge = (status: BlogPost["status"]) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Published</Badge>
      case "draft":
        return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">Draft</Badge>
      case "scheduled":
        return <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Scheduled</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const getSeoScoreBadge = (score: BlogPost["seoScore"]) => {
    switch (score) {
      case "good":
        return <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Good</Badge>
      case "ok":
        return <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">OK</Badge>
      case "needs-work":
        return <Badge className="bg-red-500/10 text-red-400 border-red-500/20">Needs Work</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const handleDeletePost = (postId: string) => {
    setPosts(posts.filter((post) => post.id !== postId))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Blog Management</h1>
          <p className="text-neutral-400 mt-1">Manage your SEO-optimized blog content</p>
        </div>
        <Link href="/admin/blog/new">
          <Button className="bg-blue-gradient-hover">
            <Plus className="h-4 w-4 mr-2" />
            Create New Post
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-neutral-400">Total Posts</p>
                <p className="text-2xl font-bold text-white">{posts.length}</p>
              </div>
              <div className="bg-blue-500/10 p-2 rounded-lg">
                <Edit className="h-5 w-5 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-neutral-400">Published</p>
                <p className="text-2xl font-bold text-white">{posts.filter((p) => p.status === "published").length}</p>
              </div>
              <div className="bg-green-500/10 p-2 rounded-lg">
                <Eye className="h-5 w-5 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-neutral-400">Local Posts</p>
                <p className="text-2xl font-bold text-white">{posts.filter((p) => p.location).length}</p>
              </div>
              <div className="bg-purple-500/10 p-2 rounded-lg">
                <MapPin className="h-5 w-5 text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-neutral-400">Total Views</p>
                <p className="text-2xl font-bold text-white">
                  {posts.reduce((sum, post) => sum + post.views, 0).toLocaleString()}
                </p>
              </div>
              <div className="bg-orange-500/10 p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
                <Input
                  placeholder="Search posts by title or author..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-neutral-800 border-neutral-700 text-white"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40 bg-neutral-800 border-neutral-700 text-white">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-800 border-neutral-700">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger className="w-full md:w-40 bg-neutral-800 border-neutral-700 text-white">
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent className="bg-neutral-800 border-neutral-700">
                <SelectItem value="all">All Locations</SelectItem>
                <SelectItem value="national">National</SelectItem>
                <SelectItem value="local">Local</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Posts Table */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white">All Blog Posts ({filteredPosts.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-neutral-800">
                <tr className="text-left">
                  <th className="p-4 text-neutral-400 font-medium">Status</th>
                  <th className="p-4 text-neutral-400 font-medium">Title</th>
                  <th className="p-4 text-neutral-400 font-medium">Location</th>
                  <th className="p-4 text-neutral-400 font-medium">SEO Score</th>
                  <th className="p-4 text-neutral-400 font-medium">Views</th>
                  <th className="p-4 text-neutral-400 font-medium">Published</th>
                  <th className="p-4 text-neutral-400 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPosts.map((post) => (
                  <tr key={post.id} className="border-b border-neutral-800 hover:bg-neutral-800/50">
                    <td className="p-4">{getStatusBadge(post.status)}</td>
                    <td className="p-4">
                      <div>
                        <p className="text-white font-medium">{post.title}</p>
                        <p className="text-sm text-neutral-400">by {post.author}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      {post.location ? (
                        <div className="flex items-center text-blue-400">
                          <MapPin className="h-4 w-4 mr-1" />
                          {post.location}
                        </div>
                      ) : (
                        <span className="text-neutral-500">(National)</span>
                      )}
                    </td>
                    <td className="p-4">{getSeoScoreBadge(post.seoScore)}</td>
                    <td className="p-4">
                      <span className="text-white">{post.views.toLocaleString()}</span>
                    </td>
                    <td className="p-4">
                      {post.publishedAt ? (
                        <span className="text-neutral-300">{new Date(post.publishedAt).toLocaleDateString()}</span>
                      ) : (
                        <span className="text-neutral-500">Not published</span>
                      )}
                    </td>
                    <td className="p-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="text-neutral-400 hover:text-white">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-neutral-800 border-neutral-700">
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/blog/edit/${post.id}`} className="flex items-center">
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Post
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/blog/${post.slug}`} className="flex items-center">
                              <Eye className="h-4 w-4 mr-2" />
                              View Post
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeletePost(post.id)}
                            className="text-red-400 focus:text-red-300"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Post
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {filteredPosts.length === 0 && (
        <Card className="bg-neutral-900 border-neutral-800">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No posts found</h3>
            <p className="text-neutral-400 mb-4">
              {searchTerm || statusFilter !== "all" || locationFilter !== "all"
                ? "Try adjusting your search criteria or filters."
                : "Get started by creating your first blog post."}
            </p>
            <Link href="/admin/blog/new">
              <Button className="bg-blue-gradient-hover">
                <Plus className="h-4 w-4 mr-2" />
                Create New Post
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
