"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Droplets, Clock, Users, Star } from "lucide-react"

interface WelcomeStepProps {
  onNext: () => void
  userName: string
}

export function WelcomeStep({ onNext, userName }: WelcomeStepProps) {
  const benefits = [
    {
      icon: Users,
      title: "Connect with Customers",
      description: "Get discovered by homeowners looking for pressure washing services",
    },
    {
      icon: Star,
      title: "Build Your Reputation",
      description: "Collect reviews and showcase your best work",
    },
    {
      icon: Clock,
      title: "Save Time",
      description: "Man<PERSON> leads and quotes all in one place",
    },
  ]

  return (
    <div className="max-w-4xl mx-auto text-center">
      {/* Logo */}
      <div className="mb-8">
        <Link href="/" className="inline-flex items-center space-x-2">
          <div className="bg-blue-gradient p-4 rounded-xl glow-blue">
            <Droplets className="h-10 w-10 text-white" />
          </div>
          <span className="text-3xl font-bold text-white">PressureWash Pro</span>
        </Link>
      </div>

      {/* Welcome Message */}
      <div className="mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          Welcome to the Directory, <span className="text-white">{userName}</span>!
        </h1>
        <p className="text-xl text-neutral-400 mb-6">
          Let's get your business profile set up and ready to attract new customers.
        </p>
        <div className="inline-flex items-center gap-2 bg-blue-500/10 border border-blue-500/20 rounded-full px-4 py-2">
          <Clock className="h-4 w-4 text-blue-400" />
          <span className="text-blue-400 font-medium">It only takes 2 minutes</span>
        </div>
      </div>

      {/* Benefits */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {benefits.map((benefit, index) => {
          const Icon = benefit.icon
          return (
            <Card key={index} className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-white font-semibold mb-2">{benefit.title}</h3>
                <p className="text-neutral-400 text-sm">{benefit.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* CTA Button */}
      <Button onClick={onNext} size="lg" className="bg-blue-gradient-hover text-lg px-8 py-4">
        Let's Get Started
      </Button>

      {/* Skip Option */}
      <div className="mt-6">
        <Link href="/dashboard" className="text-neutral-400 hover:text-white text-sm transition-colors">
          Skip for now (you can complete this later)
        </Link>
      </div>
    </div>
  )
}
