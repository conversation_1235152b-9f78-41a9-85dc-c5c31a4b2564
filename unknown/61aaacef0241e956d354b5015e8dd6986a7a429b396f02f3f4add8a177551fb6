"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, Phone, MapPin, Clock, MessageSquare, Users, Shield } from "lucide-react"

export function ContactInfo() {
  const contactMethods = [
    {
      icon: Mail,
      title: "Email Support",
      primary: "<EMAIL>",
      secondary: "We typically respond within 24 hours",
      action: "mailto:<EMAIL>",
    },
    {
      icon: Phone,
      title: "Phone Support",
      primary: "(*************",
      secondary: "Mon-Fri, 9 AM - 6 PM EST",
      action: "tel:+***********",
    },
    {
      icon: MapPin,
      title: "Business Address",
      primary: "123 Business Rd, Suite 100",
      secondary: "Phoenix, AZ 85001",
      action: null,
    },
  ]

  const supportTypes = [
    {
      icon: Users,
      title: "For Business Owners",
      description: "Questions about listings, premium features, billing, or account management",
      badge: "Business Support",
    },
    {
      icon: MessageSquare,
      title: "For Customers",
      description: "Help finding services, reporting issues, or general platform questions",
      badge: "Customer Support",
    },
    {
      icon: Shield,
      title: "Technical Issues",
      description: "Bug reports, website problems, or technical difficulties",
      badge: "Technical Support",
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Get in Touch Directly</h2>
        <p className="text-neutral-400 mb-8">
          Choose the best way to reach us. Our support team is ready to help with any questions or concerns.
        </p>
      </div>

      {/* Contact Methods */}
      <div className="space-y-4">
        {contactMethods.map((method, index) => {
          const Icon = method.icon
          return (
            <Card key={index} className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-blue-gradient p-3 rounded-lg glow-blue flex-shrink-0">
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold mb-1">{method.title}</h3>
                    {method.action ? (
                      <a
                        href={method.action}
                        className="text-blue-400 hover:text-blue-300 font-medium transition-colors"
                      >
                        {method.primary}
                      </a>
                    ) : (
                      <p className="text-white font-medium">{method.primary}</p>
                    )}
                    <p className="text-neutral-400 text-sm mt-1">{method.secondary}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Support Types */}
      <div className="mt-8">
        <h3 className="text-xl font-semibold text-white mb-4">What can we help you with?</h3>
        <div className="space-y-3">
          {supportTypes.map((type, index) => {
            const Icon = type.icon
            return (
              <div key={index} className="bg-neutral-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Icon className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-white font-medium">{type.title}</h4>
                      <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20 text-xs">{type.badge}</Badge>
                    </div>
                    <p className="text-neutral-400 text-sm">{type.description}</p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Business Hours */}
      <Card className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Support Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-neutral-400">Monday - Friday:</span>
              <span className="text-white">9:00 AM - 6:00 PM EST</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-400">Saturday:</span>
              <span className="text-white">10:00 AM - 4:00 PM EST</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-400">Sunday:</span>
              <span className="text-neutral-500">Closed</span>
            </div>
          </div>
          <div className="mt-4 p-3 bg-neutral-800 rounded-lg">
            <p className="text-neutral-400 text-xs">
              💡 <strong className="text-white">Tip:</strong> For faster response times, include your account email and
              describe your issue in detail.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
