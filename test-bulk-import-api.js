#!/usr/bin/env node

/**
 * Test script to verify bulk import API functionality
 */

const FormData = require('form-data')
const fs = require('fs')
const fetch = require('node-fetch')

async function testBulkImportAPI() {
  console.log('🧪 Testing Bulk Import API End-to-End')
  console.log('=====================================')

  try {
    // Create a test CSV content
    const testCSV = `Name	Address	Phone Number	Website	Rating	Total Ratings	Latitude	Longitude	Types
Test Pressure Washing	123 Test St, Phoenix, AZ 85001	(*************	https://testpw.com	4.5	10	33.4484	-112.0740	pressure_washing_service`

    // Write test CSV to file
    fs.writeFileSync('test-import.csv', testCSV)
    console.log('✅ Created test CSV file')

    // Create form data
    const formData = new FormData()
    formData.append('file', fs.createReadStream('test-import.csv'))
    formData.append('ownerId', '00000000-0000-0000-0000-000000000000') // Test UUID

    // Make API request
    console.log('📤 Sending bulk import request...')
    const response = await fetch('http://localhost:3001/api/admin/bulk-import', {
      method: 'POST',
      body: formData
    })

    const result = await response.json()
    
    if (response.ok) {
      console.log('✅ API request successful')
      console.log('📊 Import Results:')
      console.log(`   - Imported: ${result.imported}`)
      console.log(`   - Failed: ${result.failed}`)
      console.log(`   - Success: ${result.success}`)
      
      if (result.errors && result.errors.length > 0) {
        console.log('⚠️  Errors:')
        result.errors.forEach(error => {
          console.log(`   - ${error.business}: ${error.error}`)
        })
      }
    } else {
      console.log('❌ API request failed')
      console.log('Error:', result.error)
    }

    // Clean up test file
    fs.unlinkSync('test-import.csv')
    console.log('🧹 Cleaned up test file')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testBulkImportAPI()
  .then(() => {
    console.log('\n🎉 Bulk import API test completed!')
  })
  .catch(error => {
    console.error('❌ Test script failed:', error)
  })
