import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const next = requestUrl.searchParams.get('next') ?? '/dashboard'

  if (code) {
    const supabase = await createServerClient()

    if (!supabase) {
      return NextResponse.redirect(
        new URL('/auth/login?error=Authentication service unavailable', requestUrl.origin)
      )
    }

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)
      
      if (error) {
        console.error('Auth callback error:', error)
        return NextResponse.redirect(
          new URL(`/auth/login?error=${encodeURIComponent(error.message)}`, requestUrl.origin)
        )
      }

      if (data.user) {
        // Create or update user profile
        const profileData = {
          id: data.user.id,
          email: data.user.email,
          full_name: data.user.user_metadata?.full_name || data.user.email?.split('@')[0] || 'User',
          updated_at: new Date().toISOString()
        }

        const { error: profileError } = await supabase
          .from('profiles')
          .upsert(profileData, { onConflict: 'id' })

        if (profileError) {
          console.error('Error creating/updating profile:', profileError)
          // Don't fail the auth flow for profile errors
        }
      }

      // Successful authentication - redirect to intended destination
      return NextResponse.redirect(new URL(next, requestUrl.origin))
    } catch (error) {
      console.error('Unexpected auth callback error:', error)
      return NextResponse.redirect(
        new URL('/auth/login?error=Authentication failed', requestUrl.origin)
      )
    }
  }

  // No code provided - redirect to login
  return NextResponse.redirect(
    new URL('/auth/login?error=No authentication code provided', requestUrl.origin)
  )
}
