"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Quote } from "lucide-react"
import Image from "next/image"

export function PricingTestimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      business: "Crystal Clean Pressure Washing",
      location: "Phoenix, AZ",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "Since upgrading to Premium, I've seen a 300% increase in leads. The featured placement really makes a difference!",
      plan: "Premium",
    },
    {
      name: "<PERSON>",
      business: "Aqua Pro Services",
      location: "Dallas, TX",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "The analytics dashboard helps me understand my customers better. Best investment I've made for my business.",
      plan: "Premium",
    },
    {
      name: "<PERSON>",
      business: "Power Wash Plus",
      location: "Miami, FL",
      image: "/placeholder.svg?height=80&width=80",
      rating: 5,
      text: "Even the free plan helped me get started. Now with Premium, I'm booked solid every week!",
      plan: "Premium",
    },
  ]

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star key={i} className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-current" : "text-neutral-600"}`} />
    ))
  }

  return (
    <section className="py-16 px-4 bg-neutral-900/50">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">What Our Customers Say</h2>
          <p className="text-neutral-400 max-w-2xl mx-auto">
            See how pressure washing businesses are growing with PressureWash Pro
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="relative w-12 h-12">
                    <Image
                      src={testimonial.image || "/placeholder.svg"}
                      alt={testimonial.name}
                      fill
                      className="object-cover rounded-full"
                    />
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">{testimonial.name}</h4>
                    <p className="text-neutral-400 text-sm">{testimonial.business}</p>
                    <p className="text-neutral-500 text-xs">{testimonial.location}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2 mb-3">
                  <div className="flex">{renderStars(testimonial.rating)}</div>
                  <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20 text-xs">{testimonial.plan}</Badge>
                </div>

                <div className="relative">
                  <Quote className="h-4 w-4 text-blue-400 mb-2" />
                  <p className="text-neutral-300 text-sm italic">"{testimonial.text}"</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
