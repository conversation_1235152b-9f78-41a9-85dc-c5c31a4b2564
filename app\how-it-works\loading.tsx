import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export default function HowItWorksLoading() {
  return (
    <div className="min-h-screen bg-black">
      {/* Header Skeleton */}
      <div className="border-b border-neutral-800 px-4 py-4">
        <div className="container mx-auto flex items-center justify-between">
          <Skeleton className="h-8 w-40 bg-neutral-800" />
          <div className="flex items-center space-x-6">
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-6 w-20 bg-neutral-800" />
            <Skeleton className="h-9 w-20 bg-neutral-800" />
            <Skeleton className="h-9 w-24 bg-neutral-800" />
          </div>
        </div>
      </div>

      {/* Hero Section Skeleton */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Skeleton className="h-6 w-32 mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-16 w-full max-w-4xl mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-6 w-full max-w-3xl mx-auto bg-neutral-800" />
        </div>
      </section>

      {/* Steps Section Skeleton */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Step 1 Skeleton */}
            <div className="text-center">
              <Card className="bg-neutral-900 border-neutral-800 mb-6">
                <CardContent className="p-8">
                  <Skeleton className="w-20 h-20 rounded-full mx-auto mb-6 bg-neutral-800" />
                  <Skeleton className="h-8 w-32 mx-auto mb-4 bg-neutral-800" />
                  <Skeleton className="h-4 w-full mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-3/4 mx-auto mb-4 bg-neutral-800" />
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-3/4 bg-neutral-800" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Step 2 Skeleton */}
            <div className="text-center">
              <Card className="bg-neutral-900 border-neutral-800 mb-6">
                <CardContent className="p-8">
                  <Skeleton className="w-20 h-20 rounded-full mx-auto mb-6 bg-neutral-800" />
                  <Skeleton className="h-8 w-32 mx-auto mb-4 bg-neutral-800" />
                  <Skeleton className="h-4 w-full mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-3/4 mx-auto mb-4 bg-neutral-800" />
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-3/4 bg-neutral-800" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Step 3 Skeleton */}
            <div className="text-center">
              <Card className="bg-neutral-900 border-neutral-800 mb-6">
                <CardContent className="p-8">
                  <Skeleton className="w-20 h-20 rounded-full mx-auto mb-6 bg-neutral-800" />
                  <Skeleton className="h-8 w-32 mx-auto mb-4 bg-neutral-800" />
                  <Skeleton className="h-4 w-full mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-3/4 mx-auto mb-4 bg-neutral-800" />
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-full bg-neutral-800" />
                    <Skeleton className="h-3 w-3/4 bg-neutral-800" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section Skeleton */}
      <section className="py-16 px-4 bg-neutral-900/30">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-80 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-6 text-center">
                  <Skeleton className="h-12 w-12 mx-auto mb-4 bg-neutral-800" />
                  <Skeleton className="h-6 w-40 mx-auto mb-3 bg-neutral-800" />
                  <Skeleton className="h-4 w-full mb-2 bg-neutral-800" />
                  <Skeleton className="h-4 w-3/4 mx-auto bg-neutral-800" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section Skeleton */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-64 mx-auto mb-12 bg-neutral-800" />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Skeleton className="h-8 w-80 mb-6 bg-neutral-800" />
              <div className="space-y-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="flex items-start space-x-3">
                    <Skeleton className="h-6 w-6 mt-1 bg-neutral-800" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-48 mb-2 bg-neutral-800" />
                      <Skeleton className="h-4 w-full mb-1 bg-neutral-800" />
                      <Skeleton className="h-4 w-3/4 bg-neutral-800" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="bg-neutral-900 border-neutral-800">
                  <CardContent className="p-4 text-center">
                    <Skeleton className="h-8 w-16 mx-auto mb-2 bg-neutral-800" />
                    <Skeleton className="h-4 w-24 mx-auto bg-neutral-800" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section Skeleton */}
      <section className="py-20 px-4 bg-neutral-900/50">
        <div className="container mx-auto text-center">
          <Skeleton className="h-12 w-96 mx-auto mb-6 bg-neutral-800" />
          <Skeleton className="h-6 w-full max-w-2xl mx-auto mb-8 bg-neutral-800" />
          <Skeleton className="h-12 w-64 mx-auto mb-4 bg-neutral-800" />
          <Skeleton className="h-4 w-80 mx-auto bg-neutral-800" />
        </div>
      </section>

      {/* Footer Skeleton */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[1, 2, 3, 4].map((i) => (
              <div key={i}>
                <Skeleton className="h-6 w-32 mb-4 bg-neutral-800" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24 bg-neutral-800" />
                  <Skeleton className="h-4 w-28 bg-neutral-800" />
                  <Skeleton className="h-4 w-20 bg-neutral-800" />
                </div>
              </div>
            ))}
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center">
            <Skeleton className="h-4 w-64 mx-auto bg-neutral-800" />
          </div>
        </div>
      </footer>
    </div>
  )
}
