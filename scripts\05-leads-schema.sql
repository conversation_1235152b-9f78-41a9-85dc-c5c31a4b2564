-- Leads Management Schema for PressureWash Pro Directory
-- This script adds lead tracking functionality for businesses

-- <PERSON><PERSON> leads table
CREATE TABLE IF NOT EXISTS public.leads (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  
  -- Lead contact information
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  
  -- Lead details
  service_type TEXT,
  property_address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  
  -- Lead status and tracking
  status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'quoted', 'scheduled', 'completed', 'lost')),
  source TEXT DEFAULT 'website' CHECK (source IN ('website', 'referral', 'google', 'facebook', 'phone', 'other')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Lead value and notes
  estimated_value DECIMAL(10,2),
  notes TEXT,
  
  -- Follow-up tracking
  last_contact_date TIMESTAMPTZ,
  next_follow_up_date TIMESTAMPTZ,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_leads_business_id ON public.leads(business_id);
CREATE INDEX IF NOT EXISTS idx_leads_status ON public.leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON public.leads(created_at);
CREATE INDEX IF NOT EXISTS idx_leads_next_follow_up ON public.leads(next_follow_up_date);

-- Add comments
COMMENT ON TABLE public.leads IS 'Lead tracking and management for businesses';
COMMENT ON COLUMN public.leads.status IS 'Current status of the lead in the sales pipeline';
COMMENT ON COLUMN public.leads.source IS 'How the lead was acquired';
COMMENT ON COLUMN public.leads.priority IS 'Priority level for follow-up';

-- Create lead activities table for tracking interactions
CREATE TABLE IF NOT EXISTS public.lead_activities (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES public.leads(id) ON DELETE CASCADE,
  
  -- Activity details
  activity_type TEXT NOT NULL CHECK (activity_type IN ('call', 'email', 'meeting', 'quote_sent', 'follow_up', 'note')),
  description TEXT NOT NULL,
  
  -- Activity metadata
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for lead activities
CREATE INDEX IF NOT EXISTS idx_lead_activities_lead_id ON public.lead_activities(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_activities_created_at ON public.lead_activities(created_at);

-- Add comments
COMMENT ON TABLE public.lead_activities IS 'Activity log for lead interactions and follow-ups';

-- Enable Row Level Security
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_activities ENABLE ROW LEVEL SECURITY;

-- RLS Policies for leads
CREATE POLICY "Business owners can manage their leads" ON public.leads
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.businesses 
      WHERE id = business_id 
      AND owner_id = auth.uid()
    )
  );

-- RLS Policies for lead activities
CREATE POLICY "Business owners can manage their lead activities" ON public.lead_activities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.leads l
      JOIN public.businesses b ON l.business_id = b.id
      WHERE l.id = lead_id 
      AND b.owner_id = auth.uid()
    )
  );

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_leads_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_leads_updated_at
  BEFORE UPDATE ON public.leads
  FOR EACH ROW
  EXECUTE FUNCTION update_leads_updated_at();

-- Insert some sample lead statuses for reference
INSERT INTO public.leads (business_id, name, email, phone, service_type, property_address, city, state, zip_code, status, source, priority, estimated_value, notes)
SELECT 
  b.id,
  'John Smith',
  '<EMAIL>',
  '(*************',
  'Driveway Cleaning',
  '123 Main St',
  'Springfield',
  'IL',
  '62701',
  'new',
  'website',
  'medium',
  250.00,
  'Interested in driveway and sidewalk cleaning. Has a large property.'
FROM public.businesses b
LIMIT 1
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT ALL ON public.leads TO authenticated;
GRANT ALL ON public.lead_activities TO authenticated;
