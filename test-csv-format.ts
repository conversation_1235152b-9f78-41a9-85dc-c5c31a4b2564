// Test script to verify CSV parsing with the exact format provided

import { parseCSV } from './lib/bulk-import-utils'

const testCSVContent = `Name	Address	Phone Number	International Phone	Website	Google Maps URL	Rating	Total Ratings	Status	Business Status	Price Level	Price Level Text	Latitude	Longitude	Plus Code	Place ID	Types	Hours	Photo URLs	Icon URL	Recent Reviews Count	Average Recent Rating	Recent Reviews Text
Clean Home Power Washing	3813 Miriam Dr, Charlotte, NC 28205, United States	(*************	******-214-1485	https://www.cleanhomepowerwashing.com/	https://maps.google.com/?cid=4673589347672508557	5	647	Closed	OPERATIONAL		Unknown	35.1980212	-80.7962048	867X56X3+6G	ChIJNYAitQC9VogRjcgR_r_s20A	point_of_interest, establishment	Monday: 8:00 AM – 5:00 PM; Tuesday: 8:00 AM – 5:00 PM; Wednesday: 8:00 AM – 5:00 PM; Thursday: 8:00 AM – 5:00 PM; Friday: 8:00 AM – 5:00 PM; Saturday: 8:00 AM – 5:00 PM; Sunday: Closed		https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png	5	5	★★★★★ <PERSON> (3 months ago): What a wonderful experience we had with the Clean Home Power Washing company.
Mr. <PERSON> Bryan was pleasant, courteous and professional. He answered our questions, did an awesome job on the house, walkw...

★★★★★ Rick Hearn (3 weeks ago): My Homes Vinyl siding hasn't been cleaned in approximately 30 years and looked awful. I had no idea whether it could be cleaned or I'd need to replace it. Clean Home Power washing came out washed my s...

★★★★★ Anne-<PERSON> McMenamin (a week ago): Raymond and Cameron were prompt, professional and did an excellent job.  We had roof, gutters, windows, house, driveway and front porch stone floor cleaned and they all look so much better.  We especi...

★★★★★ Jim Hamilton (2 weeks ago): Cameron from Clean Home Power Washing was terrific, home looks great!  Entire experience - quote, quality, communications, timeliness - was outstanding, so I feel I received great value for a very com...

★★★★★ Tim Rieckmann (in the last week): Clean Home Power Washing did a great job ! The job was to power wash front sidewalk and driveway after years of wear and tear, mildew , algae etc.   The result was a bright  driveway, looked like it w...`

// Test the parsing
try {
  const businesses = parseCSV(testCSVContent)
  console.log('✅ CSV Parsing Test Results:')
  console.log(`📊 Parsed ${businesses.length} business(es)`)
  
  if (businesses.length > 0) {
    const business = businesses[0]
    console.log('\n📋 First Business Details:')
    console.log(`Name: ${business.name}`)
    console.log(`Address: ${business.address}`)
    console.log(`Phone: ${business.phoneNumber}`)
    console.log(`Website: ${business.website}`)
    console.log(`Rating: ${business.rating}`)
    console.log(`Total Ratings: ${business.totalRatings}`)
    console.log(`Latitude: ${business.latitude}`)
    console.log(`Longitude: ${business.longitude}`)
    console.log(`Business Status: ${business.businessStatus}`)
    console.log(`Place ID: ${business.placeId}`)
    console.log(`Types: ${business.types}`)
    console.log(`Hours: ${business.weekdayText}`)
    console.log(`Reviews Count: ${business.reviews?.length || 0}`)
    
    if (business.reviews && business.reviews.length > 0) {
      console.log('\n📝 Sample Review:')
      const review = business.reviews[0]
      console.log(`Author: ${review.author_name}`)
      console.log(`Rating: ${review.rating}`)
      console.log(`Time: ${review.relative_time_description}`)
      console.log(`Text: ${review.text.substring(0, 100)}...`)
    }
  }
  
  console.log('\n✅ CSV parsing successful!')
  
} catch (error) {
  console.error('❌ CSV Parsing Error:', error)
}

// Expected output structure for verification
const expectedStructure = {
  name: 'Clean Home Power Washing',
  address: '3813 Miriam Dr, Charlotte, NC 28205, United States',
  phoneNumber: '(*************',
  internationalPhoneNumber: '******-214-1485',
  website: 'https://www.cleanhomepowerwashing.com/',
  googleMapsUrl: 'https://maps.google.com/?cid=4673589347672508557',
  rating: 5,
  totalRatings: 647,
  status: 'Closed',
  businessStatus: 'OPERATIONAL',
  priceLevelText: 'Unknown',
  latitude: 35.1980212,
  longitude: -80.7962048,
  plusCode: '867X56X3+6G',
  placeId: 'ChIJNYAitQC9VogRjcgR_r_s20A',
  types: ['point_of_interest', 'establishment'],
  weekdayText: [
    'Monday: 8:00 AM – 5:00 PM',
    'Tuesday: 8:00 AM – 5:00 PM',
    'Wednesday: 8:00 AM – 5:00 PM',
    'Thursday: 8:00 AM – 5:00 PM',
    'Friday: 8:00 AM – 5:00 PM',
    'Saturday: 8:00 AM – 5:00 PM',
    'Sunday: Closed'
  ],
  iconUrl: 'https://maps.gstatic.com/mapfiles/place_api/icons/v1/png_71/generic_business-71.png',
  recentReviewsCount: 5,
  averageRecentRating: 5,
  reviews: [
    {
      author_name: 'Anthony Rivera',
      rating: 5,
      relative_time_description: '3 months ago',
      text: 'What a wonderful experience we had with the Clean Home Power Washing company...'
    }
    // ... more reviews
  ]
}

console.log('\n📋 Expected Structure Reference:')
console.log(JSON.stringify(expectedStructure, null, 2))
