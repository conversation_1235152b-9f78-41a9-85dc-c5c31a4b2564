import { NextRequest, NextResponse } from 'next/server'
import { ZodError } from 'zod'

// Custom error classes
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true
    this.code = code
    
    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR')
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR')
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR')
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR')
    this.name = 'ConflictError'
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR')
    this.name = 'RateLimitError'
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed') {
    super(message, 500, 'DATABASE_ERROR')
    this.name = 'DatabaseError'
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string = 'External service error') {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR')
    this.name = 'ExternalServiceError'
  }
}

// Error response interface
interface ErrorResponse {
  error: {
    message: string
    code?: string
    details?: any
    timestamp: string
    requestId?: string
  }
}

// Log error function
function logError(error: Error, request?: NextRequest, context?: any) {
  const errorLog = {
    message: error.message,
    stack: error.stack,
    name: error.name,
    timestamp: new Date().toISOString(),
    url: request?.url,
    method: request?.method,
    userAgent: request?.headers.get('user-agent'),
    context
  }

  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', errorLog)
  } else {
    // In production, send to logging service
    console.error('API Error:', {
      message: error.message,
      name: error.name,
      timestamp: errorLog.timestamp,
      url: errorLog.url,
      method: errorLog.method
    })
  }
}

// Main error handler
export function handleApiError(error: unknown, request?: NextRequest, context?: any): NextResponse<ErrorResponse> {
  let statusCode = 500
  let message = 'Internal server error'
  let code = 'INTERNAL_ERROR'
  let details: any = undefined

  // Log the error
  if (error instanceof Error) {
    logError(error, request, context)
  }

  // Handle different error types
  if (error instanceof AppError) {
    statusCode = error.statusCode
    message = error.message
    code = error.code || 'APP_ERROR'
  } else if (error instanceof ZodError) {
    statusCode = 400
    message = 'Validation failed'
    code = 'VALIDATION_ERROR'
    details = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }))
  } else if (error instanceof Error) {
    // Handle specific error patterns
    if (error.message.includes('unique constraint') || error.message.includes('duplicate key')) {
      statusCode = 409
      message = 'Resource already exists'
      code = 'DUPLICATE_ERROR'
    } else if (error.message.includes('foreign key constraint')) {
      statusCode = 400
      message = 'Invalid reference'
      code = 'REFERENCE_ERROR'
    } else if (error.message.includes('not found')) {
      statusCode = 404
      message = 'Resource not found'
      code = 'NOT_FOUND'
    } else if (error.message.includes('timeout')) {
      statusCode = 504
      message = 'Request timeout'
      code = 'TIMEOUT_ERROR'
    } else if (error.message.includes('connection')) {
      statusCode = 503
      message = 'Service unavailable'
      code = 'CONNECTION_ERROR'
    } else {
      message = process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    }
  }

  const errorResponse: ErrorResponse = {
    error: {
      message,
      code,
      timestamp: new Date().toISOString(),
      ...(details && { details }),
      ...(process.env.NODE_ENV === 'development' && error instanceof Error && { 
        stack: error.stack 
      })
    }
  }

  return NextResponse.json(errorResponse, { status: statusCode })
}

// Async wrapper for API routes
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ErrorResponse>> => {
    try {
      return await handler(...args)
    } catch (error) {
      const request = args.find(arg => arg instanceof NextRequest) as NextRequest | undefined
      return handleApiError(error, request)
    }
  }
}

// Middleware for API routes
export function apiErrorMiddleware(handler: Function) {
  return async (request: NextRequest, context: any) => {
    try {
      return await handler(request, context)
    } catch (error) {
      return handleApiError(error, request, context)
    }
  }
}

// Validation helper
export function validateRequest<T>(schema: any, data: unknown): T {
  try {
    return schema.parse(data)
  } catch (error) {
    if (error instanceof ZodError) {
      throw new ValidationError('Invalid request data', error.errors)
    }
    throw error
  }
}

// Database error handler
export function handleDatabaseError(error: any): never {
  if (error?.code === 'PGRST116') {
    throw new NotFoundError('Resource not found')
  } else if (error?.code === '23505') {
    throw new ConflictError('Resource already exists')
  } else if (error?.code === '23503') {
    throw new ValidationError('Invalid reference')
  } else if (error?.code === '23514') {
    throw new ValidationError('Data constraint violation')
  } else if (error?.message?.includes('timeout')) {
    throw new DatabaseError('Database timeout')
  } else if (error?.message?.includes('connection')) {
    throw new DatabaseError('Database connection error')
  } else {
    throw new DatabaseError(error?.message || 'Database operation failed')
  }
}

// Rate limiting helper
export function checkRateLimit(request: NextRequest, limit: number = 100, window: number = 60000) {
  // Simple in-memory rate limiting (in production, use Redis or similar)
  const ip = request.headers.get('x-forwarded-for') || 'unknown'
  const key = `rate_limit:${ip}`
  
  // This is a simplified example - in production, implement proper rate limiting
  // with Redis or a dedicated service
  
  return true // For now, always allow
}

// Request ID generator
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// CORS error handler
export function handleCorsError(origin: string): NextResponse {
  return NextResponse.json(
    {
      error: {
        message: 'CORS policy violation',
        code: 'CORS_ERROR',
        timestamp: new Date().toISOString()
      }
    },
    { 
      status: 403,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }
  )
}

// Health check helper
export function createHealthCheck() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  })
}
