# Design Document

## Overview

This design outlines the complete backend implementation for the PressureWash Pro Directory, a two-sided marketplace connecting homeowners with pressure washing service providers. The backend will be built using Next.js API routes with Supabase as the database and authentication provider. The design emphasizes security, scalability, and maintainability while supporting all core business functionality.

## Architecture

### System Architecture

The backend follows a layered architecture pattern:

1. **API Layer**: Next.js API routes handling HTTP requests and responses
2. **Business Logic Layer**: Service functions implementing core business rules
3. **Data Access Layer**: Database functions abstracting Supabase operations
4. **Authentication Layer**: Supabase Auth integration with custom middleware
5. **Storage Layer**: Supabase Storage for file uploads (portfolio images)

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **Database**: Supabase PostgreSQL with Row-Level Security
- **Authentication**: Supabase Auth with JWT tokens
- **File Storage**: Supabase Storage with CDN
- **Validation**: Zod for runtime type validation
- **Error Handling**: Custom error classes with structured logging

## Components and Interfaces

### 1. Database Schema Component

**Purpose**: Complete PostgreSQL schema with all required tables and relationships

**Key Tables**:
- `profiles` - User profile information
- `businesses` - Business profile data
- `business_members` - Business ownership and roles
- `locations` - Business location data with geographic indexing
- `services` - Available service types
- `business_services` - Many-to-many relationship between businesses and services
- `portfolio_images` - Business portfolio image storage
- `reviews` - Customer reviews and ratings
- `message_threads` - Communication thread management
- `messages` - Individual messages within threads

**Relationships**:
```sql
profiles (1) -> (M) business_members (M) <- (1) businesses
businesses (1) -> (M) locations
businesses (1) -> (M) business_services (M) <- (1) services
businesses (1) -> (M) portfolio_images
businesses (1) -> (M) reviews <- (1) profiles
businesses (1) -> (M) message_threads <- (1) profiles
message_threads (1) -> (M) messages <- (1) profiles
```

### 2. Authentication Component

**Purpose**: Secure user authentication and authorization

**Key Functions**:
- User registration with email verification
- User login with JWT token generation
- Password reset functionality
- Profile management
- Role-based access control

**API Endpoints**:
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `POST /api/auth/reset-password` - Password reset
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### 3. Business Management Component

**Purpose**: Complete business profile and service management

**Key Functions**:
- Business profile creation and updates
- Service management (add/remove services)
- Location management with geocoding
- Portfolio image upload and management
- Business member management

**API Endpoints**:
- `GET /api/businesses` - Search and list businesses
- `POST /api/businesses` - Create new business
- `GET /api/businesses/[slug]` - Get business details
- `PUT /api/businesses/[slug]` - Update business
- `DELETE /api/businesses/[slug]` - Delete business
- `POST /api/businesses/[slug]/services` - Manage services
- `POST /api/businesses/[slug]/location` - Update location
- `POST /api/businesses/[slug]/portfolio` - Upload portfolio images
- `DELETE /api/businesses/[slug]/portfolio/[imageId]` - Delete portfolio image

### 4. Search and Discovery Component

**Purpose**: Advanced search functionality with filtering and pagination

**Key Functions**:
- Location-based search with radius filtering
- Service type filtering
- Rating and review filtering
- Pagination and sorting
- Geographic distance calculations

**Search Parameters**:
- `location` - City, state, or zip code
- `radius` - Search radius in miles
- `services` - Array of service IDs
- `minRating` - Minimum average rating
- `sortBy` - Sort criteria (rating, distance, reviews)
- `page` - Pagination page number
- `limit` - Results per page

### 5. Messaging System Component

**Purpose**: Real-time communication between homeowners and businesses

**Key Functions**:
- Message thread creation
- Message sending and receiving
- Thread management and organization
- Real-time notifications (future enhancement)

**API Endpoints**:
- `GET /api/messages/threads` - List user's message threads
- `POST /api/messages/threads` - Create new message thread
- `GET /api/messages/threads/[threadId]` - Get thread messages
- `POST /api/messages/threads/[threadId]` - Send message
- `PUT /api/messages/threads/[threadId]` - Update thread status

### 6. Review System Component

**Purpose**: Customer review and rating management

**Key Functions**:
- Review creation and validation
- Rating aggregation and calculation
- Review moderation capabilities
- Business rating updates

**API Endpoints**:
- `GET /api/businesses/[slug]/reviews` - Get business reviews
- `POST /api/businesses/[slug]/reviews` - Create review
- `PUT /api/reviews/[reviewId]` - Update review
- `DELETE /api/reviews/[reviewId]` - Delete review
- `POST /api/reviews/[reviewId]/report` - Report inappropriate review

## Data Models

### Core Data Types

```typescript
interface Profile {
  id: string
  email: string
  full_name: string
  phone?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

interface Business {
  id: string
  name: string
  slug: string
  description?: string
  phone?: string
  email?: string
  website?: string
  status: 'active' | 'inactive' | 'pending'
  subscription_tier: 'free' | 'premium'
  avg_rating: number
  review_count: number
  created_at: string
  updated_at: string
}

interface Location {
  id: string
  business_id: string
  address: string
  city: string
  state: string
  zip_code: string
  latitude: number
  longitude: number
  is_primary: boolean
  created_at: string
  updated_at: string
}

interface Review {
  id: string
  business_id: string
  customer_id: string
  rating: number
  title?: string
  content?: string
  created_at: string
  updated_at: string
}

interface MessageThread {
  id: string
  business_id: string
  customer_id: string
  subject?: string
  status: 'active' | 'closed'
  created_at: string
  updated_at: string
}

interface Message {
  id: string
  thread_id: string
  sender_id: string
  content: string
  created_at: string
  updated_at: string
}
```

### API Response Types

```typescript
interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface SearchResponse {
  businesses: BusinessWithDetails[]
  pagination: PaginationInfo
  filters: AppliedFilters
}
```

## Error Handling

### Error Classification

1. **Validation Errors** (400): Invalid input data
2. **Authentication Errors** (401): Missing or invalid authentication
3. **Authorization Errors** (403): Insufficient permissions
4. **Not Found Errors** (404): Resource not found
5. **Conflict Errors** (409): Resource already exists
6. **Server Errors** (500): Internal server errors

### Error Response Format

```typescript
interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: any
    timestamp: string
    requestId: string
  }
}
```

### Error Handling Strategy

- **Input Validation**: Use Zod schemas for request validation
- **Database Errors**: Convert database errors to user-friendly messages
- **Authentication Errors**: Clear messages without exposing security details
- **Logging**: Structured logging with error tracking
- **Monitoring**: Error rate monitoring and alerting

## Testing Strategy

### Unit Testing

- **Database Functions**: Test all CRUD operations
- **Business Logic**: Test validation and business rules
- **API Endpoints**: Test request/response handling
- **Authentication**: Test auth flows and security

### Integration Testing

- **Database Integration**: Test with real database
- **API Integration**: Test complete request flows
- **Authentication Integration**: Test with Supabase Auth
- **File Upload Integration**: Test with Supabase Storage

### Test Data Management

- **Seed Scripts**: Realistic test data generation
- **Cleanup Scripts**: Test data cleanup between runs
- **Mock Data**: Consistent mock data for unit tests
- **Test Users**: Predefined test accounts for different roles

## Security Considerations

### Authentication Security

- **JWT Tokens**: Secure token generation and validation
- **Password Security**: Bcrypt hashing with salt
- **Session Management**: Secure session handling
- **Rate Limiting**: Prevent brute force attacks

### Data Security

- **Row-Level Security**: Database-level access control
- **Input Sanitization**: Prevent SQL injection and XSS
- **Data Validation**: Server-side validation for all inputs
- **Sensitive Data**: Proper handling of PII and business data

### API Security

- **CORS Configuration**: Proper cross-origin request handling
- **Request Validation**: Validate all incoming requests
- **Error Messages**: Don't expose sensitive information
- **Audit Logging**: Log security-relevant events

## Performance Considerations

### Database Performance

- **Indexing Strategy**: Optimize queries with proper indexes
- **Query Optimization**: Efficient SQL queries and joins
- **Connection Pooling**: Manage database connections
- **Caching Strategy**: Cache frequently accessed data

### API Performance

- **Response Caching**: Cache static and semi-static data
- **Pagination**: Limit result sets for large queries
- **Compression**: Gzip compression for responses
- **CDN Integration**: Use CDN for static assets

### Scalability

- **Horizontal Scaling**: Design for multiple server instances
- **Database Scaling**: Support read replicas and sharding
- **File Storage**: Use CDN for image delivery
- **Monitoring**: Performance monitoring and alerting

## Deployment Strategy

### Environment Configuration

- **Development**: Local development with hot reloading
- **Staging**: Production-like environment for testing
- **Production**: Optimized production deployment

### Database Deployment

- **Migration Scripts**: Version-controlled schema changes
- **Seed Data**: Production seed data for services
- **Backup Strategy**: Regular database backups
- **Monitoring**: Database performance monitoring

### API Deployment

- **Build Process**: Optimized production builds
- **Environment Variables**: Secure configuration management
- **Health Checks**: API health monitoring
- **Error Tracking**: Production error monitoring