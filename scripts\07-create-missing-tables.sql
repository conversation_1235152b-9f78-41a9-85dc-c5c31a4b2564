-- Create Missing Tables: Leads and Subscriptions
-- This script only creates the missing tables that are causing 404 errors
-- It's safe to run even if some tables already exist

-- ===== CREATE SUBSCRIPTIONS TABLE =====

CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL UNIQUE REFERENCES public.businesses(id) ON DELETE CASCADE,
  plan TEXT NOT NULL DEFAULT 'free' CHECK (plan IN ('free', 'premium')),
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'past_due', 'incomplete')),
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT UNIQUE,
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE LEADS TABLE =====

CREATE TABLE IF NOT EXISTS public.leads (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  
  -- Lead contact information
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  
  -- Lead details
  service_type TEXT,
  property_address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  
  -- Lead status and tracking
  status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'quoted', 'scheduled', 'completed', 'lost')),
  source TEXT DEFAULT 'website' CHECK (source IN ('website', 'referral', 'google', 'facebook', 'phone', 'other')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Lead value and notes
  estimated_value DECIMAL(10,2),
  notes TEXT,
  
  -- Follow-up tracking
  last_contact_date TIMESTAMPTZ,
  next_follow_up_date TIMESTAMPTZ,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE LEAD ACTIVITIES TABLE =====

CREATE TABLE IF NOT EXISTS public.lead_activities (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID NOT NULL REFERENCES public.leads(id) ON DELETE CASCADE,
  
  -- Activity details
  activity_type TEXT NOT NULL CHECK (activity_type IN ('call', 'email', 'meeting', 'quote_sent', 'follow_up', 'note')),
  description TEXT NOT NULL,
  
  -- Activity metadata
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===== CREATE INDEXES =====

-- Subscription indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_business_id ON public.subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);

-- Lead indexes
CREATE INDEX IF NOT EXISTS idx_leads_business_id ON public.leads(business_id);
CREATE INDEX IF NOT EXISTS idx_leads_status ON public.leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON public.leads(created_at);
CREATE INDEX IF NOT EXISTS idx_leads_next_follow_up ON public.leads(next_follow_up_date);

-- Lead activity indexes
CREATE INDEX IF NOT EXISTS idx_lead_activities_lead_id ON public.lead_activities(lead_id);
CREATE INDEX IF NOT EXISTS idx_lead_activities_created_at ON public.lead_activities(created_at);

-- ===== ENABLE ROW LEVEL SECURITY =====

ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lead_activities ENABLE ROW LEVEL SECURITY;

-- ===== CREATE RLS POLICIES (ONLY IF THEY DON'T EXIST) =====

-- Subscription policies
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'subscriptions' 
    AND policyname = 'Business owners can view their subscriptions'
  ) THEN
    CREATE POLICY "Business owners can view their subscriptions" ON public.subscriptions
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM public.businesses 
          WHERE id = business_id 
          AND owner_id = auth.uid()
        )
      );
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'subscriptions' 
    AND policyname = 'Business owners can manage their subscriptions'
  ) THEN
    CREATE POLICY "Business owners can manage their subscriptions" ON public.subscriptions
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM public.businesses 
          WHERE id = business_id 
          AND owner_id = auth.uid()
        )
      );
  END IF;
END $$;

-- Lead policies
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'leads' 
    AND policyname = 'Business owners can manage their leads'
  ) THEN
    CREATE POLICY "Business owners can manage their leads" ON public.leads
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM public.businesses 
          WHERE id = business_id 
          AND owner_id = auth.uid()
        )
      );
  END IF;
END $$;

-- Lead activity policies
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'lead_activities' 
    AND policyname = 'Business owners can manage their lead activities'
  ) THEN
    CREATE POLICY "Business owners can manage their lead activities" ON public.lead_activities
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM public.leads l
          JOIN public.businesses b ON l.business_id = b.id
          WHERE l.id = lead_id 
          AND b.owner_id = auth.uid()
        )
      );
  END IF;
END $$;

-- ===== CREATE TRIGGERS =====

-- Function to update updated_at timestamp (create only if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers (drop first if they exist)
DROP TRIGGER IF EXISTS trigger_update_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER trigger_update_subscriptions_updated_at
  BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_leads_updated_at ON public.leads;
CREATE TRIGGER trigger_update_leads_updated_at
  BEFORE UPDATE ON public.leads
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ===== GRANT PERMISSIONS =====

GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.leads TO authenticated;
GRANT ALL ON public.lead_activities TO authenticated;

-- ===== COMPLETION MESSAGE =====

DO $$
BEGIN
  RAISE NOTICE '=== MISSING TABLES CREATED ===';
  RAISE NOTICE 'subscriptions table: READY';
  RAISE NOTICE 'leads table: READY';
  RAISE NOTICE 'lead_activities table: READY';
  RAISE NOTICE 'RLS policies: CONFIGURED';
  RAISE NOTICE 'Indexes and triggers: CREATED';
  RAISE NOTICE '';
  RAISE NOTICE 'The 404 errors for leads and subscriptions should now be resolved!';
END $$;
