"use client"

import { useState, useEffect, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { SearchBar } from "@/components/search-bar"
import { BusinessCard } from "@/components/business-card"
import { MapView } from "@/components/map-view"
import { FilterPanel } from "@/components/filter-panel"
import { SortDropdown } from "@/components/sort-dropdown"
import { Button } from "@/components/ui/button"
import type { BusinessWithDetails } from "@/lib/types"
import { Loader2, ChevronLeft, ChevronRight } from "lucide-react"
import { getBusinessCoordinates, getCityCoordinates, getZipCodeCoordinates, getStateCoordinates } from "@/lib/geocoding"
import { getCenterPoint } from "@/lib/client-geocoding"

// Helper function to determine optimal zoom level based on search type
function getOptimalZoom(location: string, businessCount: number): number {
  if (!location) return businessCount > 1 ? 10 : 12

  const locationTrimmed = location.trim()

  // ZIP code searches - closer zoom
  if (/^\d{5}$/.test(locationTrimmed)) {
    return businessCount > 1 ? 12 : 14
  }

  // State-only searches - wider zoom
  if (/^[A-Za-z]{2}$/.test(locationTrimmed) || isFullStateName(locationTrimmed)) {
    return businessCount > 5 ? 8 : 9
  }

  // City searches - medium zoom
  return businessCount > 1 ? 10 : 12
}

// Helper to check if string is a full state name
function isFullStateName(str: string): boolean {
  const stateNames = [
    'alabama', 'alaska', 'arizona', 'arkansas', 'california', 'colorado', 'connecticut',
    'delaware', 'florida', 'georgia', 'hawaii', 'idaho', 'illinois', 'indiana', 'iowa',
    'kansas', 'kentucky', 'louisiana', 'maine', 'maryland', 'massachusetts', 'michigan',
    'minnesota', 'mississippi', 'missouri', 'montana', 'nebraska', 'nevada', 'new hampshire',
    'new jersey', 'new mexico', 'new york', 'north carolina', 'north dakota', 'ohio',
    'oklahoma', 'oregon', 'pennsylvania', 'rhode island', 'south carolina', 'south dakota',
    'tennessee', 'texas', 'utah', 'vermont', 'virginia', 'washington', 'west virginia',
    'wisconsin', 'wyoming'
  ]
  return stateNames.includes(str.toLowerCase())
}

export function SearchResults() {
  const searchParams = useSearchParams()
  const [businesses, setBusinesses] = useState<(BusinessWithDetails & { distance?: number })[]>([])
  const [loading, setLoading] = useState(true)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [hoveredBusinessId, setHoveredBusinessId] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    services: [] as number[],
    rating: 0,
    premium: false,
  })
  const [sort, setSort] = useState({
    sortBy: 'rating' as 'distance' | 'rating' | 'reviews' | 'name',
    sortOrder: 'desc' as 'asc' | 'desc',
  })

  const limit = 20
  const offset = (currentPage - 1) * limit
  const totalPages = Math.ceil(total / limit)

  const query = searchParams.get("q") || ""
  const location = searchParams.get("location") || ""

  // Enhanced map center calculation for reactive search
  const mapCenter = useMemo(() => {
    // First, try to get center from business locations if we have results
    if (businesses.length > 0) {
      const businessCoords = businesses
        .map(business => getBusinessCoordinates(business.location))
        .filter(coords => coords !== null) as { lat: number; lng: number }[]

      if (businessCoords.length > 0) {
        return getCenterPoint(businessCoords)
      }
    }

    // If no business coordinates, intelligently geocode the search location
    if (location) {
      const locationTrimmed = location.trim()

      // Handle ZIP code searches
      if (/^\d{5}$/.test(locationTrimmed)) {
        const zipCoords = getZipCodeCoordinates(locationTrimmed)
        if (zipCoords) return zipCoords
      }

      // Handle state-only searches
      if (/^[A-Za-z]{2}$/.test(locationTrimmed)) {
        const stateCoords = getStateCoordinates(locationTrimmed.toUpperCase())
        if (stateCoords) return stateCoords
      }

      // Handle full state name searches
      const stateCoords = getStateCoordinates(locationTrimmed)
      if (stateCoords) return stateCoords

      // Handle city, state format
      const parts = location.split(',').map(p => p.trim())
      if (parts.length >= 2) {
        const city = parts[0]
        const state = parts[1]
        const coords = getCityCoordinates(city, state)
        if (coords) return coords
      } else if (parts.length === 1) {
        // Try as city name with common states
        const cityName = parts[0].toLowerCase()
        if (cityName.includes('phoenix') || cityName.includes('arizona') || cityName.includes('az')) {
          return { lat: 33.4484, lng: -112.0740 } // Phoenix
        } else if (cityName.includes('charlotte') || cityName.includes('north carolina') || cityName.includes('nc')) {
          return { lat: 35.2271, lng: -80.8431 } // Charlotte
        } else if (cityName.includes('scottsdale')) {
          return { lat: 33.4942, lng: -111.9261 } // Scottsdale
        }
      }
    }

    // Default to Phoenix if no specific location found
    return { lat: 33.4484, lng: -112.0740 }
  }, [businesses, location])

  useEffect(() => {
    fetchBusinesses()
  }, [query, location, filters, currentPage, sort])

  const handleSortChange = (sortBy: 'distance' | 'rating' | 'reviews' | 'name', sortOrder: 'asc' | 'desc') => {
    setSort({ sortBy, sortOrder })
    setCurrentPage(1) // Reset to first page when sorting changes
  }

  const fetchBusinesses = async () => {
    setLoading(true)
    try {
      // Build search parameters
      const params = new URLSearchParams()
      if (query) params.set('query', query)
      if (location) params.set('location', location)
      if (filters.services.length > 0) {
        params.set('serviceIds', filters.services.join(','))
      }
      if (filters.rating > 0) {
        params.set('minRating', filters.rating.toString())
      }
      params.set('limit', limit.toString())
      params.set('offset', offset.toString())
      params.set('sortBy', sort.sortBy)
      params.set('sortOrder', sort.sortOrder)

      // Call the search API
      const response = await fetch(`/api/search?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Search failed')
      }

      const data = await response.json()
      setBusinesses(data.businesses || [])
      setTotal(data.total || 0)
    } catch (error) {
      console.error("Error fetching businesses:", error)
      setBusinesses([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Search Header */}
      <div className="bg-neutral-900 border border-neutral-800 rounded-xl p-6 mb-6">
        <div className="mb-4">
          <SearchBar initialQuery={query} initialLocation={location} />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-semibold text-white mb-1">
              {location ? `Pressure Washing Services in ${location}` : "Search Results"}
            </h1>
            <p className="text-neutral-400">
              {loading ? "Searching..." : `${total} businesses found`}
              {!loading && total > 0 && (
                <span className="ml-2 text-neutral-500">
                  (Page {currentPage} of {totalPages})
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <SortDropdown currentSort={sort} onSortChange={handleSortChange} />
            <FilterPanel filters={filters} onFiltersChange={setFilters} />
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 min-h-[600px]">
        {/* Business List - Main Column (3/5 width) */}
        <div className="lg:col-span-3">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
            </div>
          ) : businesses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-neutral-400 text-lg mb-4">No businesses found</p>
              <p className="text-neutral-500">Try adjusting your search criteria or location</p>
            </div>
          ) : (
            <div className="space-y-4">
              {businesses.map((business) => (
                <BusinessCard
                  key={business.id}
                  business={business}
                  isHovered={hoveredBusinessId === business.id}
                  onHover={setHoveredBusinessId}
                />
              ))}
            </div>
          )}

          {/* Pagination */}
          {!loading && totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + Math.max(1, currentPage - 2)
                  if (page > totalPages) return null

                  return (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className={page === currentPage
                        ? "bg-blue-600 text-white"
                        : "border-neutral-700 text-neutral-300 hover:bg-neutral-800"
                      }
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </div>

        {/* Map Sidebar - Right Column (2/5 width) */}
        <div className="lg:col-span-2">
          <div className="sticky top-20">
            <div className="bg-neutral-900 border border-neutral-800 rounded-xl overflow-hidden">
              <div className="p-4 border-b border-neutral-800">
                <h3 className="text-white font-medium">Map View</h3>
                <p className="text-neutral-400 text-sm">Interactive map showing business locations</p>
              </div>
              <div className="h-[400px]">
                <MapView
                  businesses={businesses}
                  center={mapCenter}
                  zoom={getOptimalZoom(location, businesses.length)}
                  hoveredBusinessId={hoveredBusinessId}
                  onBusinessHover={setHoveredBusinessId}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
