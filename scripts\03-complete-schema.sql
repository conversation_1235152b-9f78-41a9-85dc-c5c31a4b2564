-- Complete Database Schema for Pressure Washing Directory
-- Based on Section 4 of the Development Guide

-- ===== IDENTITY & ACCESS MANAGEMENT =====

-- Create a table for public user profiles
-- This table is an extension of the private auth.users table.
CREATE TABLE public.profiles (
  id UUID NOT NULL PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.profiles IS 'Public profile data for each user.';
COMMENT ON COLUMN public.profiles.id IS 'References auth.users.id';

-- Create a table for businesses (tenants)
CREATE TABLE public.businesses (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID NOT NULL REFERENCES public.profiles(id),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  phone TEXT,
  website_url TEXT,
  avg_rating NUMERIC(2, 1) DEFAULT 0.0,
  review_count INT DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.businesses IS 'Core table for business listings (tenants).';
COMMENT ON COLUMN public.businesses.slug IS 'URL-friendly unique identifier for public profiles.';

-- Create a join table for business members and their roles
CREATE TYPE public.business_role AS ENUM ('owner', 'admin', 'member');

CREATE TABLE public.business_members (
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  role business_role NOT NULL DEFAULT 'member',
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (business_id, user_id)
);
COMMENT ON TABLE public.business_members IS 'Links users to businesses, defining their role (multi-tenancy).';

-- ===== BUSINESS PROFILE CONTENT =====

-- Create a table for business locations
CREATE TABLE public.locations (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL UNIQUE REFERENCES public.businesses(id) ON DELETE CASCADE,
  street_address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT
  -- For advanced search, enable PostGIS and add:
  -- coordinates GEOGRAPHY(POINT, 4326)
);
COMMENT ON TABLE public.locations IS 'Physical locations for businesses.';

-- Create a master table for all possible services
CREATE TABLE public.services (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT
);
COMMENT ON TABLE public.services IS 'Master list of all pressure washing services offered.';

-- Create a join table for services offered by each business
CREATE TABLE public.business_services (
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  service_id INT NOT NULL REFERENCES public.services(id) ON DELETE CASCADE,
  PRIMARY KEY (business_id, service_id)
);
COMMENT ON TABLE public.business_services IS 'Links businesses to the specific services they provide.';

-- Create a table for portfolio images
CREATE TABLE public.portfolio_images (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  caption TEXT,
  display_order INT DEFAULT 0,
  uploaded_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.portfolio_images IS 'Portfolio images uploaded by businesses.';

-- ===== USER INTERACTION & SOCIAL PROOF =====

-- Create a table for reviews
CREATE TABLE public.reviews (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  author_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
  content TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.reviews IS 'Customer reviews and ratings for businesses.';

-- Create tables for the messaging system
CREATE TABLE public.message_threads (
  id UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, -- The homeowner
  subject TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.message_threads IS 'A conversation thread between a homeowner and a business.';

CREATE TABLE public.messages (
  id BIGSERIAL PRIMARY KEY,
  thread_id UUID NOT NULL REFERENCES public.message_threads(id) ON DELETE CASCADE,
  author_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  sent_at TIMESTAMPTZ DEFAULT NOW()
);
COMMENT ON TABLE public.messages IS 'Individual messages within a thread.';

-- ===== HELPER FUNCTIONS =====

-- Helper function to get all business IDs a user is a member of.
CREATE OR REPLACE FUNCTION public.get_user_business_ids()
RETURNS TABLE(id UUID)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  IF auth.uid() IS NULL THEN
    RETURN QUERY SELECT NULL::UUID WHERE 1=0; -- Return empty set if no user
  ELSE
    RETURN QUERY SELECT business_id FROM business_members WHERE user_id = auth.uid();
  END IF;
END;
$$;

-- ===== ROW-LEVEL SECURITY POLICIES =====

-- === PROFILES ===
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Profiles are publicly viewable." ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can insert their own profile." ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update their own profile." ON public.profiles FOR UPDATE USING (auth.uid() = id);

-- === BUSINESSES ===
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Businesses are publicly viewable." ON public.businesses FOR SELECT USING (true);
CREATE POLICY "Business owners can insert their own business." ON public.businesses FOR INSERT WITH CHECK (auth.uid() = owner_id);
CREATE POLICY "Business owners/admins can update their business." ON public.businesses FOR UPDATE USING (id IN (
  SELECT bm.business_id FROM business_members bm WHERE bm.user_id = auth.uid() AND bm.role IN ('owner', 'admin')
));

-- === BUSINESS_MEMBERS ===
ALTER TABLE public.business_members ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Business members can view their own memberships." ON public.business_members FOR SELECT USING (business_id IN (SELECT id FROM get_user_business_ids()));
CREATE POLICY "Business owners can manage their members." ON public.business_members FOR ALL USING (business_id IN (
  SELECT bm.business_id FROM business_members bm WHERE bm.user_id = auth.uid() AND bm.role = 'owner'
));

-- === LOCATIONS ===
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Locations are publicly viewable." ON public.locations FOR SELECT USING (true);
CREATE POLICY "Business members can manage their location." ON public.locations FOR ALL USING (business_id IN (SELECT id FROM get_user_business_ids()));

-- === SERVICES ===
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Services are publicly viewable." ON public.services FOR SELECT USING (true);

-- === BUSINESS_SERVICES ===
ALTER TABLE public.business_services ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Business services are publicly viewable." ON public.business_services FOR SELECT USING (true);
CREATE POLICY "Business members can manage their services." ON public.business_services FOR ALL USING (business_id IN (SELECT id FROM get_user_business_ids()));

-- === PORTFOLIO_IMAGES ===
ALTER TABLE public.portfolio_images ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Portfolio images are publicly viewable." ON public.portfolio_images FOR SELECT USING (true);
CREATE POLICY "Business members can manage their portfolio." ON public.portfolio_images FOR ALL USING (business_id IN (SELECT id FROM get_user_business_ids()));

-- === REVIEWS ===
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Reviews are publicly viewable." ON public.reviews FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert reviews." ON public.reviews FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can delete their own reviews." ON public.reviews FOR DELETE USING (auth.uid() = author_id);

-- === MESSAGE_THREADS & MESSAGES ===
ALTER TABLE public.message_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own message threads." ON public.message_threads FOR ALL USING (
  -- Homeowner who started the thread
  auth.uid() = user_id
  OR
  -- A member of the business in the thread
  business_id IN (SELECT id FROM get_user_business_ids())
);

CREATE POLICY "Users can manage messages in their own threads." ON public.messages FOR ALL USING (
  thread_id IN (SELECT id FROM message_threads) -- relies on the policy on message_threads
);

-- ===== TRIGGERS FOR DENORMALIZED DATA =====

-- Function to update business rating and review count
CREATE OR REPLACE FUNCTION update_business_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the business's avg_rating and review_count
  UPDATE businesses 
  SET 
    avg_rating = (
      SELECT COALESCE(AVG(rating), 0)::NUMERIC(2,1) 
      FROM reviews 
      WHERE business_id = COALESCE(NEW.business_id, OLD.business_id)
    ),
    review_count = (
      SELECT COUNT(*) 
      FROM reviews 
      WHERE business_id = COALESCE(NEW.business_id, OLD.business_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.business_id, OLD.business_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update business rating when reviews are inserted, updated, or deleted
CREATE TRIGGER update_business_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_business_rating();

-- Function to automatically create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, avatar_url, updated_at)
  VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'avatar_url', NOW());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ===== SEED DATA =====

-- Insert initial services
INSERT INTO public.services (name, description) VALUES
  ('House Washing', 'Complete exterior house cleaning including siding, trim, and windows'),
  ('Driveway Cleaning', 'Pressure washing of concrete, asphalt, and paver driveways'),
  ('Roof Cleaning', 'Soft washing and treatment of roof surfaces to remove algae and stains'),
  ('Deck & Patio Cleaning', 'Cleaning and restoration of wooden decks and concrete patios'),
  ('Fence Cleaning', 'Pressure washing of wooden, vinyl, and metal fencing'),
  ('Commercial Cleaning', 'Large-scale pressure washing for commercial properties'),
  ('Fleet Washing', 'Cleaning of commercial vehicles and equipment'),
  ('Graffiti Removal', 'Specialized removal of graffiti and vandalism'),
  ('Rust Stain Removal', 'Treatment and removal of rust stains from various surfaces'),
  ('Gutter Cleaning', 'Cleaning and maintenance of gutters and downspouts')
ON CONFLICT (name) DO NOTHING;