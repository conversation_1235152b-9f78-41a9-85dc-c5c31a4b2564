# Testing Infrastructure

This directory contains the comprehensive testing infrastructure for the PressureWash Pro Directory application.

## Overview

The testing infrastructure supports multiple types of testing:

- **Unit Tests**: Component and utility function tests using Vitest
- **Integration Tests**: Database and API integration tests
- **End-to-End Tests**: Full user journey tests using Playwright
- **Database Tests**: Schema validation and data integrity tests

## Quick Start

### 1. Initial Setup

```bash
# Set up the test environment
npm run test:setup

# Install dependencies (if not already done)
npm install
```

### 2. Configure Environment

Copy `.env.test` to `.env.test.local` and update with your test environment values:

```bash
cp .env.test .env.test.local
```

Update the following variables in `.env.test.local`:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

### 3. Run Tests

```bash
# Run all tests
npm run test:all

# Run specific test types
npm run test              # Unit tests
npm run test:integration  # Integration tests
npm run test:e2e         # End-to-end tests

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

## Test Structure

```
tests/
├── config/              # Test configuration files
│   ├── test-environments.ts
│   └── jest.config.js
├── database/            # Database testing utilities
│   ├── seed-manager.ts
│   ├── test-seed.sql
│   └── test-cleanup.sql
├── utils/               # Testing utilities and helpers
│   ├── test-helpers.ts
│   ├── auth-helpers.ts
│   └── ...
├── e2e/                 # End-to-end tests (Playwright)
├── integration/         # Integration tests
├── unit/                # Unit tests
├── setup.ts             # Global test setup
└── README.md           # This file
```

## Test Environments

The testing infrastructure supports multiple environments:

- **Local**: Uses local Supabase instance or development database
- **Staging**: Uses staging environment for integration testing
- **Production**: For production validation (use with caution)

Configure environments in `tests/config/test-environments.ts`.

## Database Testing

### Test Data Management

```bash
# Seed test database
npm run test:seed

# Clean up test data
npm run test:cleanup

# Reset database (cleanup + seed)
npm run test:reset
```

### Test Data Structure

The test database includes:
- 3 test user profiles (homeowner, business owner, admin)
- 3 test businesses with different subscription tiers
- Sample reviews, messages, and portfolio images
- Realistic location and service data

### Using Test Data

```typescript
import { TestSeedManager } from './database/seed-manager'

const seedManager = new TestSeedManager()

// Get test user data
const testUsers = seedManager.getTestUsers()
const homeowner = testUsers.homeowner

// Get test business data
const testBusinesses = seedManager.getTestBusinesses()
const business = testBusinesses.powerClean
```

## Authentication Testing

### Mock Authentication

```typescript
import { AuthTestHelper } from './utils/auth-helpers'

const authHelper = new AuthTestHelper()

// Create mock user and session
const mockUser = authHelper.createMockUser()
const mockSession = authHelper.createMockSession(mockUser)

// Mock auth state for unit tests
authHelper.mockAuthState(mockUser, mockSession)
```

### Real Authentication (Integration Tests)

```typescript
// Create test user
const user = await authHelper.createTestUser({
  email: '<EMAIL>',
  password: 'testpassword123',
  full_name: 'Test User'
})

// Get auth token for API requests
const token = await authHelper.getTestUserToken('<EMAIL>', 'testpassword123')

// Create auth headers
const headers = authHelper.createAuthHeaders(token)
```

## API Testing

### Using API Test Helper

```typescript
import { ApiTestHelper } from './utils/test-helpers'

const apiHelper = new ApiTestHelper()

// Make API requests
const response = await apiHelper.get('/api/businesses')
const createResponse = await apiHelper.post('/api/businesses', businessData, headers)

// Assertions
expect(response.status).toBe(200)
expect(createResponse.ok).toBe(true)
```

### Mock API Responses

```typescript
// Mock fetch for unit tests
global.testUtils.mockFetch({ success: true, data: mockData }, 200)

// Your component/function that uses fetch will now receive the mock response
```

## Component Testing

### Basic Component Test

```typescript
import { renderWithProviders } from './utils/test-helpers'
import { BusinessCard } from '@/components/business-card'

test('renders business card correctly', () => {
  const business = MockDataGenerator.business()
  
  const { getByText } = renderWithProviders(
    <BusinessCard business={business} />
  )
  
  expect(getByText(business.name)).toBeInTheDocument()
})
```

### Testing with User Interactions

```typescript
import userEvent from '@testing-library/user-event'

test('handles form submission', async () => {
  const user = userEvent.setup()
  const onSubmit = vi.fn()
  
  const { getByRole } = renderWithProviders(
    <ContactForm onSubmit={onSubmit} />
  )
  
  await user.type(getByRole('textbox', { name: /email/i }), '<EMAIL>')
  await user.click(getByRole('button', { name: /submit/i }))
  
  expect(onSubmit).toHaveBeenCalledWith(expect.objectContaining({
    email: '<EMAIL>'
  }))
})
```

## End-to-End Testing

### Basic E2E Test

```typescript
import { test, expect } from '@playwright/test'

test('user can search for businesses', async ({ page }) => {
  await page.goto('/')
  
  await page.fill('[data-testid="search-input"]', 'pressure washing')
  await page.click('[data-testid="search-button"]')
  
  await expect(page.locator('[data-testid="business-card"]')).toBeVisible()
})
```

### Running E2E Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific test file
npx playwright test tests/e2e/search.spec.ts

# Run tests in specific browser
npx playwright test --project=chromium
```

## Performance Testing

### Measuring Performance

```typescript
import { PerformanceHelper } from './utils/test-helpers'

test('API response time is acceptable', async () => {
  const { result, duration } = await PerformanceHelper.measureExecutionTime(
    () => apiHelper.get('/api/businesses')
  )
  
  PerformanceHelper.expectFastResponse(duration, 500) // Max 500ms
  expect(result.status).toBe(200)
})
```

## Best Practices

### Test Organization

1. **Group related tests** using `describe` blocks
2. **Use descriptive test names** that explain what is being tested
3. **Follow AAA pattern**: Arrange, Act, Assert
4. **Clean up after tests** to avoid side effects

### Mock Usage

1. **Mock external dependencies** (APIs, databases) in unit tests
2. **Use real services** in integration tests
3. **Reset mocks** between tests using `global.testUtils.resetMocks()`

### Test Data

1. **Use consistent test data** from `MockDataGenerator`
2. **Clean up test data** after integration tests
3. **Use realistic data** that matches production scenarios

### Assertions

1. **Use specific assertions** rather than generic ones
2. **Test both success and error cases**
3. **Verify side effects** (database changes, API calls)

## Troubleshooting

### Common Issues

1. **Environment variables not loaded**
   - Ensure `.env.test.local` exists and has correct values
   - Check that `NODE_ENV=test` is set

2. **Database connection fails**
   - Verify Supabase credentials are correct
   - Check that required tables exist
   - Run `npm run test:setup` to validate setup

3. **Tests timeout**
   - Increase timeout in test configuration
   - Check for infinite loops or hanging promises
   - Ensure proper cleanup in `afterEach` hooks

4. **Playwright browser issues**
   - Run `npx playwright install` to install browsers
   - Check that the development server is running for E2E tests

### Getting Help

1. Check the test output for specific error messages
2. Run tests with `--verbose` flag for more details
3. Use `console.log` for debugging (remove before committing)
4. Check the test setup in `tests/setup.ts`

## Contributing

When adding new tests:

1. Follow the existing file structure and naming conventions
2. Add appropriate test utilities to the `utils/` directory
3. Update this README if adding new testing patterns
4. Ensure tests are deterministic and don't depend on external state
5. Add both positive and negative test cases