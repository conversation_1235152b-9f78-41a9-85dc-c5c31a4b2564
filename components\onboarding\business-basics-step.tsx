"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { OnboardingData } from "./onboarding-flow"
import { Building2, Phone, MapPin } from "lucide-react"

interface BusinessBasicsStepProps {
  data: OnboardingData
  updateData: (data: Partial<OnboardingData>) => void
  onNext: () => void
  onBack: () => void
}

export function BusinessBasicsStep({ data, updateData, onNext, onBack }: BusinessBasicsStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const newErrors: Record<string, string> = {}

    if (!data.businessName.trim()) {
      newErrors.businessName = "Business name is required"
    }
    if (!data.city.trim()) {
      newErrors.city = "City is required"
    }
    if (!data.state.trim()) {
      newErrors.state = "State is required"
    }

    setErrors(newErrors)

    if (Object.keys(newErrors).length === 0) {
      onNext()
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    updateData({ [name]: value })
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader className="text-center">
          <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
            <Building2 className="h-6 w-6 text-white" />
          </div>
          <CardTitle className="text-2xl text-white">Tell us about your business</CardTitle>
          <CardDescription className="text-neutral-400">
            This information will help customers find and contact you
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Name */}
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-white mb-2">
                Business Name *
              </label>
              <Input
                id="businessName"
                name="businessName"
                type="text"
                value={data.businessName}
                onChange={handleChange}
                className={`bg-neutral-800 border-neutral-700 text-white ${errors.businessName ? "border-red-500" : ""}`}
                placeholder="e.g., Aqua Clean Power Washing"
              />
              {errors.businessName && <p className="text-red-400 text-sm mt-1">{errors.businessName}</p>}
            </div>

            {/* Business Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-white mb-2">
                Business Description
              </label>
              <Textarea
                id="description"
                name="description"
                value={data.description}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
                placeholder="Briefly describe your pressure washing services..."
                rows={3}
              />
              <p className="text-neutral-500 text-xs mt-1">This helps customers understand what you offer</p>
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-white mb-2">
                Business Phone
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
                <Input
                  id="phoneNumber"
                  name="phoneNumber"
                  type="tel"
                  value={data.phoneNumber}
                  onChange={handleChange}
                  className="pl-10 bg-neutral-800 border-neutral-700 text-white"
                  placeholder="(*************"
                />
              </div>
            </div>

            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Service Location *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <Input
                    name="city"
                    type="text"
                    placeholder="City"
                    value={data.city}
                    onChange={handleChange}
                    className={`bg-neutral-800 border-neutral-700 text-white ${errors.city ? "border-red-500" : ""}`}
                  />
                  {errors.city && <p className="text-red-400 text-sm mt-1">{errors.city}</p>}
                </div>
                <div>
                  <Input
                    name="state"
                    type="text"
                    placeholder="State"
                    value={data.state}
                    onChange={handleChange}
                    className={`bg-neutral-800 border-neutral-700 text-white ${errors.state ? "border-red-500" : ""}`}
                  />
                  {errors.state && <p className="text-red-400 text-sm mt-1">{errors.state}</p>}
                </div>
              </div>
            </div>

            {/* ZIP Code */}
            <div>
              <label htmlFor="zipCode" className="block text-sm font-medium text-white mb-2">
                ZIP Code
              </label>
              <div className="max-w-xs">
                <Input
                  id="zipCode"
                  name="zipCode"
                  type="text"
                  value={data.zipCode}
                  onChange={handleChange}
                  className="bg-neutral-800 border-neutral-700 text-white"
                  placeholder="85001"
                />
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="border-neutral-700 text-neutral-300 bg-transparent"
              >
                Back
              </Button>
              <Button type="submit" className="bg-blue-gradient-hover">
                Next Step
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
