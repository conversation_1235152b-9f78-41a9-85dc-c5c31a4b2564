import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Database not configured' },
        { status: 500 }
      )
    }

    // Get any existing profile from the database
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('id, full_name, role')
      .limit(1)
      .single()

    if (error) {
      console.error('Profile retrieval error:', error)
      return NextResponse.json(
        { error: 'No existing profiles found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      profile: {
        id: profile.id,
        full_name: profile.full_name,
        role: profile.role
      }
    })

  } catch (error) {
    console.error('Get existing profile error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
