"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { supabase } from "@/lib/supabase"
import { OnboardingFlow } from "@/components/onboarding/onboarding-flow"
import { Loader2 } from "lucide-react"

export default function OnboardingPage() {
  const router = useRouter()
  const { user } = useUser()
  const [checkingBusiness, setCheckingBusiness] = useState(true)

  useEffect(() => {
    // Authentication disabled - always check for existing business
    checkExistingBusiness()
  }, [])

  const checkExistingBusiness = async () => {
    if (!user || !supabase) {
      setCheckingBusiness(false)
      return
    }

    try {
      setCheckingBusiness(true)

      // For now, allow users to create multiple businesses
      // In a production app, you might want to limit this or redirect to a "manage businesses" page
      console.log("Onboarding: Allowing user to create additional business")

    } catch (error) {
      console.error("Business check error:", error)
    } finally {
      setCheckingBusiness(false)
    }
  }

  // Show loading while checking business status
  if (checkingBusiness) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-white">Loading onboarding...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if no user (will redirect)
  if (!user) {
    return null
  }

  return <OnboardingFlow />
}
