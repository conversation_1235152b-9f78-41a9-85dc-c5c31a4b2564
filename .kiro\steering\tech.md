# Technology Stack

## Core Framework
- **Next.js 15** with App Router for full-stack React application
- **TypeScript** for type safety and developer experience
- **React 19** for modern component architecture

## Backend & Database
- **Supabase** as Backend-as-a-Service providing:
  - PostgreSQL database with Row-Level Security (RLS)
  - Authentication and user management
  - Real-time subscriptions
  - File storage for portfolio images
- **Server-side rendering** with Next.js API routes
- **Multi-tenant architecture** with UUID-based data isolation

## Styling & UI
- **Tailwind CSS** for utility-first styling
- **Radix UI** components for accessible, unstyled primitives
- **Shadcn/ui** component library built on Radix
- **Lucide React** for consistent iconography
- **Custom CSS variables** for theming support

## Forms & Validation
- **React Hook Form** for performant form handling
- **Zod** for runtime type validation and schema definition
- **@hookform/resolvers** for Zod integration

## Development Tools
- **pnpm** as package manager
- **ESLint** for code linting (build errors ignored for rapid development)
- **PostCSS** for CSS processing
- **Autoprefixer** for browser compatibility

## Common Commands

### Development
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

### Database Setup
```bash
# Run complete schema in Supabase SQL Editor
# File: scripts/03-complete-schema.sql

# Test integration
node scripts/test-integration.js
```

### Environment Setup
```bash
# Required environment variables in .env.local
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Architecture Patterns
- **Server Components** for data fetching and SEO
- **Client Components** for interactivity
- **Middleware** for authentication and route protection
- **Custom hooks** for shared logic (use-user, use-toast)
- **Database abstraction layer** in `lib/database.ts`
- **Type definitions** centralized in `lib/types.ts`