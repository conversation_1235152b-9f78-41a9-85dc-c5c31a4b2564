"use client"

import { Suspense } from "react"
import type { BusinessWithDetails } from "@/lib/types"
import { getBusinessCoordinates, formatDistance } from "@/lib/geocoding"
import { MapPin, Star } from "lucide-react"
import { GoogleMapView } from "./google-map-view"

interface MapViewProps {
  businesses: (BusinessWithDetails & { distance?: number })[]
  center?: { lat: number; lng: number }
  zoom?: number
  hoveredBusinessId?: string | null
  onBusinessHover?: (businessId: string | null) => void
}

// Fallback placeholder map component
function PlaceholderMapView({ businesses }: { businesses: (BusinessWithDetails & { distance?: number })[] }) {
  return (
    <div className="bg-neutral-900 border border-neutral-800 rounded-lg h-[600px] relative overflow-hidden">
      {/* Map Header */}
      <div className="absolute top-4 left-4 right-4 z-10">
        <div className="bg-neutral-800/90 backdrop-blur-sm rounded-lg p-3">
          <h3 className="text-white font-medium mb-1">Map View</h3>
          <p className="text-neutral-400 text-sm">{businesses.length} businesses in this area</p>
        </div>
      </div>

      {/* Placeholder Map Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-800 to-neutral-900">
        <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 400 300">
            {/* Grid pattern to simulate map */}
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Business Pins */}
      <div className="absolute inset-0 p-4">
        {businesses.slice(0, 8).map((business, index) => {
          const coordinates = getBusinessCoordinates(business.location)
          const hasCoordinates = coordinates !== null

          return (
            <div
              key={business.id}
              className="absolute group cursor-pointer"
              style={{
                left: hasCoordinates
                  ? `${Math.min(90, Math.max(10, 20 + (coordinates!.lng + 120) * 0.5))}%`
                  : `${20 + (index % 4) * 20}%`,
                top: hasCoordinates
                  ? `${Math.min(85, Math.max(15, 50 - (coordinates!.lat - 35) * 2))}%`
                  : `${30 + Math.floor(index / 4) * 25}%`,
              }}
            >
            {/* Pin */}
            <div className="relative">
              <div className="bg-blue-gradient p-2 rounded-full shadow-lg glow-blue group-hover:glow-blue-strong transition-all">
                <MapPin className="h-4 w-4 text-white" />
              </div>

              {/* Business Info Popup */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-3 shadow-xl min-w-[200px]">
                  <h4 className="text-white font-medium text-sm mb-1">{business.name}</h4>
                  <div className="flex items-center gap-1 mb-1">
                    <div className="flex">
                      {Array.from({ length: 5 }, (_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < Math.floor(business.avg_rating || 0)
                              ? "text-yellow-400 fill-current"
                              : "text-neutral-600"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-neutral-400">({business.review_count || 0})</span>
                  </div>
                  <p className="text-xs text-neutral-400">
                    {business.location?.city}, {business.location?.state}
                  </p>
                  {business.distance && (
                    <p className="text-xs text-green-400 mt-1">{formatDistance(business.distance)} away</p>
                  )}
                  {business.services && business.services[0] && (
                    <p className="text-xs text-blue-400 mt-1">{business.services[0].service?.name}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          )
        })}
      </div>

      {/* Map Integration Notice */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-neutral-800/90 backdrop-blur-sm rounded-lg p-3 text-center">
          <p className="text-neutral-400 text-sm">Placeholder map - Add Google Maps API key for interactive map</p>
        </div>
      </div>
    </div>
  )
}

// Main MapView component with Google Maps integration
export function MapView({ businesses, center, zoom, hoveredBusinessId, onBusinessHover }: MapViewProps) {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  const hasValidGoogleMapsKey = apiKey && apiKey !== 'your_google_maps_api_key_here'

  // Use Google Maps if valid API key is available, otherwise use placeholder
  if (hasValidGoogleMapsKey) {
    return (
      <Suspense fallback={<PlaceholderMapView businesses={businesses} />}>
        <GoogleMapView
          businesses={businesses}
          center={center}
          zoom={zoom}
          hoveredBusinessId={hoveredBusinessId}
          onBusinessHover={onBusinessHover}
        />
      </Suspense>
    )
  }

  // Fallback to placeholder map
  return <PlaceholderMapView businesses={businesses} />
}
