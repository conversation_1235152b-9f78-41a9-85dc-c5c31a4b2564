import { NextRequest, NextResponse } from 'next/server'
import { getBusinesses, createBusiness } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import { z } from 'zod'

const createBusinessSchema = z.object({
  name: z.string().min(1, 'Business name is required').max(100, 'Business name must be less than 100 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)\.]+$/, 'Invalid phone number format').optional(),
  website_url: z.string().url('Invalid website URL').optional().or(z.literal(''))
})

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  const options = {
    city: searchParams.get('city') || undefined,
    state: searchParams.get('state') || undefined,
    zipCode: searchParams.get('zipCode') || undefined,
    serviceId: searchParams.get('serviceId') ? parseInt(searchParams.get('serviceId')!) : undefined,
    minRating: searchParams.get('minRating') ? parseFloat(searchParams.get('minRating')!) : undefined,
    limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
    offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
  }
  
  const { businesses, error } = await getBusinesses(options)
  
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
  
  return NextResponse.json({ 
    businesses,
    pagination: {
      limit: options.limit,
      offset: options.offset,
      hasMore: businesses.length === options.limit
    }
  })
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const body = await request.json()
    
    // Validate input
    const validation = createBusinessSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: validation.error.errors 
      }, { status: 400 })
    }
    
    const { name, description, phone, website_url } = validation.data
    
    // Generate slug from business name
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
    
    // Ensure slug uniqueness by checking existing businesses
    let slug = baseSlug
    let counter = 1
    
    // This is a simple approach - in production you might want to check the database
    // For now, we'll let the database handle uniqueness constraints
    
    const { business, error } = await createBusiness({
      name,
      slug,
      description,
      phone,
      website_url: website_url || undefined,
      owner_id: user.id,
    })
    
    if (error) {
      // Handle duplicate slug error
      if (error.message?.includes('duplicate') || error.message?.includes('unique')) {
        return NextResponse.json({ 
          error: 'A business with this name already exists. Please choose a different name.' 
        }, { status: 409 })
      }
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ business }, { status: 201 })
  } catch (error) {
    console.error('Error creating business:', error)
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}