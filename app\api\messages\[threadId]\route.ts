import { NextRequest, NextResponse } from 'next/server'
import { sendMessage } from '@/lib/database'
import { requireAuth } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ threadId: string }> }
) {
  try {
    await requireAuth()
    const { threadId } = await params
    const body = await request.json()
    const { content } = body
    
    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Message content is required' }, { status: 400 })
    }
    
    const { message, error } = await sendMessage(threadId, content.trim())
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ message })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}