"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { OnboardingData } from "./onboarding-flow"
import { HelpCircle, Users, Calendar, UserPlus, Megaphone, MoreHorizontal } from "lucide-react"

interface SurveyStepProps {
  data: OnboardingData
  updateData: (data: Partial<OnboardingData>) => void
  onNext: () => void
  onBack: () => void
}

export function SurveyStep({ data, updateData, onNext, onBack }: SurveyStepProps) {
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>(data.challenges || [])

  const challenges = [
    {
      id: "lead-generation",
      icon: Users,
      title: "Finding new customers / Lead generation",
      description: "Getting more people to discover and contact your business",
    },
    {
      id: "scheduling",
      icon: Calendar,
      title: "Managing quotes and scheduling",
      description: "Organizing appointments and keeping track of customer requests",
    },
    {
      id: "hiring",
      icon: UserPlus,
      title: "Hiring and training reliable employees",
      description: "Finding and developing a trustworthy team",
    },
    {
      id: "marketing",
      icon: Megaphone,
      title: "Marketing and building my brand online",
      description: "Creating awareness and establishing credibility",
    },
    {
      id: "other",
      icon: MoreHorizontal,
      title: "Other",
      description: "Something else not listed above",
    },
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateData({ challenges: selectedChallenges })
    onNext()
  }

  const handleChallengeToggle = (challengeId: string) => {
    setSelectedChallenges(prev =>
      prev.includes(challengeId)
        ? prev.filter(id => id !== challengeId)
        : [...prev, challengeId]
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader className="text-center">
          <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-4 glow-blue">
            <HelpCircle className="h-6 w-6 text-white" />
          </div>
          <CardTitle className="text-2xl text-white">What are your biggest business challenges?</CardTitle>
          <CardDescription className="text-neutral-400">
            Select all that apply - this helps us build better tools for you and provide relevant tips
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Challenge Options */}
            <div className="space-y-3">
              {challenges.map((challenge) => {
                const Icon = challenge.icon
                const isSelected = selectedChallenges.includes(challenge.id)

                return (
                  <div
                    key={challenge.id}
                    onClick={() => handleChallengeToggle(challenge.id)}
                    className={`p-4 rounded-xl border cursor-pointer transition-all ${
                      isSelected
                        ? "border-blue-500 bg-blue-500/10 glow-blue"
                        : "border-neutral-700 bg-neutral-800 hover:border-neutral-600"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        <div
                          className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                            isSelected ? "border-blue-500 bg-blue-500" : "border-neutral-500"
                          }`}
                        >
                          {isSelected && <div className="w-2 h-2 bg-white rounded-full" />}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Icon className={`h-4 w-4 ${isSelected ? "text-blue-400" : "text-neutral-400"}`} />
                          <h3 className={`font-medium ${isSelected ? "text-blue-400" : "text-white"}`}>
                            {challenge.title}
                          </h3>
                        </div>
                        <p className="text-neutral-400 text-sm">{challenge.description}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Help Text */}
            <div className="bg-neutral-800 rounded-xl p-4 mt-6">
              <p className="text-neutral-400 text-sm">
                💡 <strong className="text-white">Pro tip:</strong> Based on your selections, we'll provide personalized
                recommendations and highlight features that can help solve your specific challenges.
              </p>
            </div>

            {/* Navigation */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="border-neutral-700 text-neutral-300 bg-transparent"
              >
                Back
              </Button>
              <Button type="submit" disabled={selectedChallenges.length === 0} className="bg-blue-gradient-hover">
                Next Step
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
