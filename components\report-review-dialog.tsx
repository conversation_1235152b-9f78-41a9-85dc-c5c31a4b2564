"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Flag, Send } from "lucide-react"

interface ReportReviewDialogProps {
  reviewId: string
  isOpen: boolean
  onClose: () => void
}

const reportReasons = [
  { value: 'spam', label: 'Spam or fake review', description: 'This review appears to be spam or fake' },
  { value: 'inappropriate', label: 'Inappropriate content', description: 'Contains inappropriate language or content' },
  { value: 'fake', label: 'Fake or misleading', description: 'This review seems fake or misleading' },
  { value: 'offensive', label: 'Offensive or abusive', description: 'Contains offensive or abusive language' },
  { value: 'other', label: 'Other', description: 'Other reason not listed above' }
]

export function ReportReviewDialog({ reviewId, isOpen, onClose }: ReportReviewDialogProps) {
  const { toast } = useToast()
  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!reason) {
      toast({
        title: "Reason Required",
        description: "Please select a reason for reporting this review.",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/reviews/${reviewId}/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason,
          description: description.trim() || undefined
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to report review')
      }

      toast({
        title: "Review Reported",
        description: data.message || "Thank you for reporting this review. Our moderation team will review it shortly."
      })

      onClose()
      setReason('')
      setDescription('')
    } catch (error) {
      console.error('Error reporting review:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to report review. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-neutral-900 border-neutral-800 max-w-md">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Flag className="h-5 w-5 mr-2 text-yellow-400" />
            Report Review
          </DialogTitle>
          <DialogDescription className="text-neutral-400">
            Help us maintain quality by reporting inappropriate reviews. All reports are reviewed by our moderation team.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-4">
          {/* Reason Selection */}
          <div>
            <Label className="text-sm font-medium text-neutral-300 mb-3 block">
              Why are you reporting this review? *
            </Label>
            <RadioGroup value={reason} onValueChange={setReason} className="space-y-3">
              {reportReasons.map((reasonOption) => (
                <div key={reasonOption.value} className="flex items-start space-x-3">
                  <RadioGroupItem 
                    value={reasonOption.value} 
                    id={reasonOption.value}
                    className="border-neutral-600 text-blue-400 mt-1"
                  />
                  <div className="flex-1">
                    <Label 
                      htmlFor={reasonOption.value} 
                      className="text-neutral-300 font-medium cursor-pointer"
                    >
                      {reasonOption.label}
                    </Label>
                    <p className="text-xs text-neutral-500 mt-1">
                      {reasonOption.description}
                    </p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Additional Description */}
          <div>
            <Label htmlFor="description" className="text-sm font-medium text-neutral-300 mb-2 block">
              Additional Details (Optional)
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Provide any additional details about why you're reporting this review..."
              className="bg-neutral-800 border-neutral-700 text-white placeholder:text-neutral-500 min-h-[80px]"
              maxLength={1000}
            />
            <p className="text-xs text-neutral-500 mt-1">
              {description.length}/1000 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-neutral-700 text-neutral-300 hover:bg-neutral-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!reason || isSubmitting}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Reporting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Submit Report
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
