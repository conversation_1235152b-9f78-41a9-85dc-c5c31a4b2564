/**
 * Testing Infrastructure Validation
 * Verifies that the testing setup is working correctly
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { TestSeedManager } from './database/seed-manager'
import { AuthTestHelper } from './utils/auth-helpers'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MockDataGenerator } from './utils/test-helpers'

describe('Testing Infrastructure', () => {
  describe('Environment Configuration', () => {
    it('should have required environment variables', () => {
      // These should be set in test environment
      expect(process.env.NODE_ENV).toBe('test')
      
      // Supabase configuration should be available
      expect(process.env.NEXT_PUBLIC_SUPABASE_URL).toBeDefined()
      expect(process.env.SUPABASE_SERVICE_ROLE_KEY).toBeDefined()
    })

    it('should load test configuration correctly', () => {
      const { getTestEnvironment } = require('./config/test-environments')
      
      const env = getTestEnvironment('local')
      expect(env.name).toBe('local')
      expect(env.isLocal).toBe(true)
      expect(env.supabaseUrl).toBeDefined()
    })
  })

  describe('Database Test Utilities', () => {
    let seedManager: TestSeedManager

    beforeAll(() => {
      seedManager = new TestSeedManager()
    })

    it('should create seed manager instance', () => {
      expect(seedManager).toBeDefined()
      expect(typeof seedManager.getTestUsers).toBe('function')
      expect(typeof seedManager.getTestBusinesses).toBe('function')
    })

    it('should provide test user data', () => {
      const testUsers = seedManager.getTestUsers()
      
      expect(testUsers.homeowner).toBeDefined()
      expect(testUsers.businessOwner).toBeDefined()
      expect(testUsers.admin).toBeDefined()
      
      expect(testUsers.homeowner.email).toBe('<EMAIL>')
      expect(testUsers.businessOwner.email).toBe('<EMAIL>')
    })

    it('should provide test business data', () => {
      const testBusinesses = seedManager.getTestBusinesses()
      
      expect(testBusinesses.powerClean).toBeDefined()
      expect(testBusinesses.sparkleWash).toBeDefined()
      expect(testBusinesses.elitePressure).toBeDefined()
      
      expect(testBusinesses.powerClean.name).toBe('Test Power Clean Pro')
      expect(testBusinesses.powerClean.slug).toBe('test-power-clean-pro')
    })
  })

  describe('Authentication Test Utilities', () => {
    let authHelper: AuthTestHelper

    beforeAll(() => {
      authHelper = new AuthTestHelper()
    })

    it('should create auth helper instance', () => {
      expect(authHelper).toBeDefined()
      expect(typeof authHelper.createMockUser).toBe('function')
      expect(typeof authHelper.createMockSession).toBe('function')
    })

    it('should create mock user data', () => {
      const mockUser = authHelper.createMockUser({
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User' }
      })
      
      expect(mockUser.email).toBe('<EMAIL>')
      expect(mockUser.user_metadata.full_name).toBe('Test User')
      expect(mockUser.id).toBeDefined()
    })

    it('should create mock session data', () => {
      const mockUser = authHelper.createMockUser()
      const mockSession = authHelper.createMockSession(mockUser)
      
      expect(mockSession.access_token).toBe('mock-access-token')
      expect(mockSession.user).toEqual(mockUser)
      expect(mockSession.expires_in).toBe(3600)
    })

    it('should provide test credentials', () => {
      const credentials = authHelper.getTestCredentials()
      
      expect(credentials.homeowner).toBeDefined()
      expect(credentials.businessOwner).toBeDefined()
      expect(credentials.admin).toBeDefined()
      
      expect(credentials.homeowner.email).toBe('<EMAIL>')
      expect(credentials.homeowner.password).toBe('testpassword123')
    })
  })

  describe('API Test Utilities', () => {
    let apiHelper: ApiTestHelper

    beforeAll(() => {
      apiHelper = new ApiTestHelper('http://localhost:3000')
    })

    it('should create API helper instance', () => {
      expect(apiHelper).toBeDefined()
      expect(typeof apiHelper.get).toBe('function')
      expect(typeof apiHelper.post).toBe('function')
      expect(typeof apiHelper.put).toBe('function')
      expect(typeof apiHelper.delete).toBe('function')
    })

    it('should create authorization headers', () => {
      const token = 'test-token'
      const headers = apiHelper.createAuthHeader(token)
      
      expect(headers.Authorization).toBe('Bearer test-token')
    })
  })

  describe('Mock Data Generators', () => {
    it('should generate mock user data', () => {
      const user = MockDataGenerator.user({
        email: '<EMAIL>',
        full_name: 'Custom User'
      })
      
      expect(user.email).toBe('<EMAIL>')
      expect(user.full_name).toBe('Custom User')
      expect(user.id).toBeDefined()
      expect(user.created_at).toBeDefined()
    })

    it('should generate mock business data', () => {
      const business = MockDataGenerator.business({
        name: 'Custom Business',
        slug: 'custom-business'
      })
      
      expect(business.name).toBe('Custom Business')
      expect(business.slug).toBe('custom-business')
      expect(business.status).toBe('active')
      expect(business.subscription_tier).toBe('free')
    })

    it('should generate mock review data', () => {
      const review = MockDataGenerator.review({
        rating: 4,
        title: 'Good Service'
      })
      
      expect(review.rating).toBe(4)
      expect(review.title).toBe('Good Service')
      expect(review.business_id).toBeDefined()
      expect(review.customer_id).toBeDefined()
    })

    it('should generate mock message data', () => {
      const message = MockDataGenerator.message({
        content: 'Test message'
      })
      
      expect(message.content).toBe('Test message')
      expect(message.thread_id).toBeDefined()
      expect(message.sender_id).toBeDefined()
    })
  })

  describe('Global Test Utilities', () => {
    it('should have global test utilities available', () => {
      expect(global.testUtils).toBeDefined()
      expect(typeof global.testUtils.waitFor).toBe('function')
      expect(typeof global.testUtils.mockFetch).toBe('function')
      expect(typeof global.testUtils.resetMocks).toBe('function')
    })

    it('should mock fetch correctly', () => {
      const mockResponse = { success: true, data: 'test' }
      global.testUtils.mockFetch(mockResponse, 200)
      
      expect(global.fetch).toBeDefined()
      expect(typeof global.fetch).toBe('function')
    })
  })
})