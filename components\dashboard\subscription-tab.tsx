"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import type { Business, Subscription } from "@/lib/types"
import { Crown, Check, Star, TrendingUp, MessageSquare } from "lucide-react"

interface SubscriptionTabProps {
  business: Business
}

export function SubscriptionTab({ business }: SubscriptionTabProps) {
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSubscription()
  }, [business.id])

  const fetchSubscription = async () => {
    try {
      if (!supabase) {
        // Mock subscription data when Supabase is not configured
        setSubscription({
          id: '1',
          business_id: business.id,
          plan: 'free',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        return
      }

      const { data, error } = await supabase.from("subscriptions").select("*").eq("business_id", business.id).single()

      if (error && error.code !== "PGRST116") {
        throw error
      }

      setSubscription(data)
    } catch (error) {
      console.error("Error fetching subscription:", error)
    } finally {
      setLoading(false)
    }
  }

  const premiumFeatures = [
    "Priority placement in search results",
    "Featured business badge",
    "Unlimited photo uploads",
    "Advanced analytics dashboard",
    "Customer inquiry notifications",
    "Custom business profile URL",
    "Priority customer support",
    "Social media integration",
  ]

  const freeFeatures = [
    "Basic business listing",
    "Up to 5 photos",
    "Customer reviews",
    "Basic contact information",
    "Service category selection",
  ]

  return (
    <div className="space-y-6">
      {/* Current Plan Status */}
      <Card className="bg-neutral-900 border-neutral-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Current Plan
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-neutral-400">Loading subscription details...</p>
          ) : business.is_premium ? (
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium Plan
                  </Badge>
                  <Badge className="bg-green-500/10 text-green-400 border-green-500/20">Active</Badge>
                </div>
                <p className="text-neutral-400">Your premium listing is active and getting maximum visibility.</p>
                {subscription?.current_period_end && (
                  <p className="text-neutral-500 text-sm mt-1">
                    Next billing: {new Date(subscription.current_period_end).toLocaleDateString()}
                  </p>
                )}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-white">$29</div>
                <div className="text-neutral-400 text-sm">/month</div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">Free Plan</Badge>
                </div>
                <p className="text-neutral-400">You're currently on the free plan with basic listing features.</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-white">$0</div>
                <div className="text-neutral-400 text-sm">/month</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Plan Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Free Plan */}
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader>
            <CardTitle className="text-white">Free Plan</CardTitle>
            <CardDescription className="text-neutral-400">Basic listing features to get started</CardDescription>
            <div className="text-2xl font-bold text-white">
              $0<span className="text-sm font-normal text-neutral-400">/month</span>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 mb-6">
              {freeFeatures.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-neutral-300">
                  <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
            {business.is_premium && (
              <Button variant="outline" className="w-full border-neutral-600 text-neutral-400 bg-transparent" disabled>
                Current Plan
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Premium Plan */}
        <Card
          className={`border-2 ${business.is_premium ? "border-yellow-500/50 bg-yellow-500/5" : "border-blue-500/50 bg-blue-500/5"}`}
        >
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white flex items-center gap-2">
                  <Crown className="h-5 w-5 text-yellow-400" />
                  Premium Plan
                </CardTitle>
                <CardDescription className="text-neutral-400">Maximum visibility and advanced features</CardDescription>
              </div>
              {!business.is_premium && (
                <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20">Most Popular</Badge>
              )}
            </div>
            <div className="text-2xl font-bold text-white">
              $29<span className="text-sm font-normal text-neutral-400">/month</span>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 mb-6">
              {premiumFeatures.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-neutral-300">
                  <Check className="h-4 w-4 text-blue-400 flex-shrink-0" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>

            {business.is_premium ? (
              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full border-yellow-500/20 text-yellow-400 bg-yellow-500/5"
                  disabled
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Current Plan
                </Button>
                <Button
                  variant="outline"
                  className="w-full border-red-500/20 text-red-400 hover:bg-red-500/10 bg-transparent"
                >
                  Cancel Subscription
                </Button>
              </div>
            ) : (
              <Button className="w-full bg-blue-gradient-hover">
                <TrendingUp className="h-4 w-4 mr-2" />
                Upgrade to Premium
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Benefits Section */}
      {!business.is_premium && (
        <Card className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-400" />
              Why Upgrade to Premium?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-white font-medium mb-1">3x More Visibility</h3>
                <p className="text-neutral-400 text-sm">Premium listings appear first in search results</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                  <Crown className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-white font-medium mb-1">Stand Out</h3>
                <p className="text-neutral-400 text-sm">Premium badge builds trust with customers</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-gradient p-3 rounded-full w-12 h-12 mx-auto mb-3 glow-blue">
                  <MessageSquare className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-white font-medium mb-1">More Leads</h3>
                <p className="text-neutral-400 text-sm">Get instant notifications for new inquiries</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
