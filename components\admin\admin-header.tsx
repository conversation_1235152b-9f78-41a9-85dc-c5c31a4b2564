"use client"

import { memo } from "react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { LogOut, User, Settings, Droplets } from "lucide-react"
import Link from "next/link"

export const AdminHeader = memo(function AdminHeader() {
  return (
    <header className="bg-neutral-900 border-b border-neutral-800 px-6 py-4 sticky top-0 z-50 admin-header">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin" className="flex items-center space-x-2">
            <div className="bg-blue-gradient p-2 rounded-xl glow-blue">
              <Droplets className="h-5 w-5 text-white admin-icon" />
            </div>
            <span className="text-white font-semibold">PressureWash Pro</span>
          </Link>
          <div className="text-neutral-400">|</div>
          <span className="text-blue-400 font-medium">ADMIN PANEL</span>
        </div>

        <div className="flex items-center space-x-4">
          <span className="text-neutral-400">Welcome, Admin User</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10">
                <User className="h-4 w-4 mr-2" />
                Account
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-neutral-900 border-neutral-800">
              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem className="text-neutral-300 hover:text-white hover:bg-blue-500/10">
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
})
