import { Suspense } from "react"
import { BulkImportForm } from "@/components/admin/bulk-import-form"
import { AdminLoading } from "@/components/admin/admin-loading"

export default function BulkImportPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Bulk Import Businesses</h1>
        <p className="text-neutral-400">
          Import multiple businesses from JSON or CSV files. Supports Google Places API data format.
        </p>
      </div>

      <Suspense fallback={<AdminLoading type="bulk-import" />}>
        <BulkImportForm />
      </Suspense>
    </div>
  )
}
