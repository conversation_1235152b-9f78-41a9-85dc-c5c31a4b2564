"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ArrowUpDown, Check } from "lucide-react"

interface SortOption {
  value: string
  label: string
  sortBy: 'distance' | 'rating' | 'reviews' | 'name'
  sortOrder: 'asc' | 'desc'
}

const SORT_OPTIONS: SortOption[] = [
  { value: 'rating-desc', label: 'Highest Rated', sortBy: 'rating', sortOrder: 'desc' },
  { value: 'rating-asc', label: 'Lowest Rated', sortBy: 'rating', sortOrder: 'asc' },
  { value: 'distance-asc', label: 'Nearest First', sortBy: 'distance', sortOrder: 'asc' },
  { value: 'distance-desc', label: 'Farthest First', sortBy: 'distance', sortOrder: 'desc' },
  { value: 'reviews-desc', label: 'Most Reviews', sortBy: 'reviews', sortOrder: 'desc' },
  { value: 'reviews-asc', label: 'Fewest Reviews', sortBy: 'reviews', sortOrder: 'asc' },
  { value: 'name-asc', label: 'Name A-Z', sortBy: 'name', sortOrder: 'asc' },
  { value: 'name-desc', label: 'Name Z-A', sortBy: 'name', sortOrder: 'desc' },
]

interface SortDropdownProps {
  currentSort: {
    sortBy: 'distance' | 'rating' | 'reviews' | 'name'
    sortOrder: 'asc' | 'desc'
  }
  onSortChange: (sortBy: 'distance' | 'rating' | 'reviews' | 'name', sortOrder: 'asc' | 'desc') => void
}

export function SortDropdown({ currentSort, onSortChange }: SortDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  const currentOption = SORT_OPTIONS.find(
    option => option.sortBy === currentSort.sortBy && option.sortOrder === currentSort.sortOrder
  ) || SORT_OPTIONS[0]

  const handleSortSelect = (option: SortOption) => {
    onSortChange(option.sortBy, option.sortOrder)
    setIsOpen(false)
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="border-neutral-700 text-neutral-300 hover:bg-neutral-800 bg-transparent"
        >
          <ArrowUpDown className="h-4 w-4 mr-2" />
          Sort: {currentOption.label}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 bg-neutral-900 border-neutral-800" align="end">
        <div className="space-y-1">
          <h3 className="font-medium text-white mb-2 px-2">Sort by</h3>
          {SORT_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => handleSortSelect(option)}
              className={`w-full flex items-center justify-between px-2 py-2 text-sm rounded hover:bg-neutral-800 transition-colors ${
                option.value === currentOption.value
                  ? "text-blue-400 bg-blue-500/10"
                  : "text-neutral-300"
              }`}
            >
              <span>{option.label}</span>
              {option.value === currentOption.value && (
                <Check className="h-4 w-4" />
              )}
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}
