import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }
    
    // Refresh the current session
    const { data, error } = await supabase.auth.refreshSession()
    
    if (error) {
      console.error('Session refresh error:', error)
      return NextResponse.json(
        { error: 'Failed to refresh session' },
        { status: 401 }
      )
    }
    
    if (!data.session) {
      return NextResponse.json(
        { error: 'No active session to refresh' },
        { status: 401 }
      )
    }
    
    return NextResponse.json({
      session: data.session,
      user: data.user,
      message: 'Session refreshed successfully!'
    })
    
  } catch (error) {
    console.error('Session refresh error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}