"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import type { Business, BusinessWithDetails } from "@/lib/types"
import { Save } from "lucide-react"

interface ProfileTabProps {
  business: BusinessWithDetails
  onBusinessUpdate: (business: Business) => void
}

export function ProfileTab({ business, onBusinessUpdate }: ProfileTabProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: business.name || "",
    description: business.description || "",
    phone: business.phone || "",
    website_url: business.website_url || "",
    street_address: business.location?.street_address || "",
    city: business.location?.city || "",
    state: business.location?.state || "",
    zip_code: business.location?.zip_code || "",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Update business info
      const businessData = {
        name: formData.name,
        description: formData.description,
        phone: formData.phone,
        website_url: formData.website_url,
      }

      const { data: businessResult, error: businessError } = await supabase
        .from("businesses")
        .update(businessData)
        .eq("id", business.id)
        .select()
        .single()

      if (businessError) throw businessError

      // Update location info
      const locationData = {
        street_address: formData.street_address,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zip_code,
      }

      const { error: locationError } = await supabase
        .from("locations")
        .upsert({
          business_id: business.id,
          ...locationData
        })

      if (locationError) throw locationError

      toast({
        title: "Profile Updated!",
        description: "Your business profile has been updated successfully.",
      })

      onBusinessUpdate(businessResult)
    } catch (error) {
      console.error("Error updating business:", error)
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }))
  }

  return (
    <Card className="bg-neutral-900 border-neutral-800">
      <CardHeader>
        <CardTitle className="text-white">Edit Business Profile</CardTitle>
        <CardDescription className="text-neutral-400">
          Update your business information to attract more customers.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-white mb-2">
              Business Name *
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              required
              value={formData.name}
              onChange={handleChange}
              className="bg-neutral-800 border-neutral-700 text-white"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-white mb-2">
              Business Description
            </label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="bg-neutral-800 border-neutral-700 text-white"
              placeholder="Describe your pressure washing services, experience, and what makes you unique..."
              rows={4}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-white mb-2">
                Phone Number
              </label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
              />
            </div>

            <div>
              <label htmlFor="website_url" className="block text-sm font-medium text-white mb-2">
                Website URL
              </label>
              <Input
                id="website_url"
                name="website_url"
                type="url"
                value={formData.website_url}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
              />
            </div>
          </div>

          <div>
            <label htmlFor="street_address" className="block text-sm font-medium text-white mb-2">
              Street Address
            </label>
            <Input
              id="street_address"
              name="street_address"
              type="text"
              value={formData.street_address}
              onChange={handleChange}
              className="bg-neutral-800 border-neutral-700 text-white"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="city" className="block text-sm font-medium text-white mb-2">
                City *
              </label>
              <Input
                id="city"
                name="city"
                type="text"
                required
                value={formData.city}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
              />
            </div>

            <div>
              <label htmlFor="state" className="block text-sm font-medium text-white mb-2">
                State *
              </label>
              <Input
                id="state"
                name="state"
                type="text"
                required
                value={formData.state}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
              />
            </div>

            <div>
              <label htmlFor="zip_code" className="block text-sm font-medium text-white mb-2">
                ZIP Code
              </label>
              <Input
                id="zip_code"
                name="zip_code"
                type="text"
                value={formData.zip_code}
                onChange={handleChange}
                className="bg-neutral-800 border-neutral-700 text-white"
              />
            </div>
          </div>

          <Button type="submit" disabled={loading} className="bg-blue-gradient-hover">
            <Save className="h-4 w-4 mr-2" />
            {loading ? "Saving..." : "Save Changes"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
