"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, User, ArrowLeft, Share2, BookOpen, ThumbsUp, MessageCircle } from "lucide-react"

interface BlogArticleProps {
  articleId: string
}

export function BlogArticle({ articleId }: BlogArticleProps) {
  const [article, setArticle] = useState<any>(null)
  const [relatedArticles, setRelatedArticles] = useState<any[]>([])

  // Mock article data - in real app, this would fetch from API
  useEffect(() => {
    const mockArticle = {
      id: Number.parseInt(articleId),
      title: "How to Choose the Right Nozzle for the Job",
      excerpt:
        "A pro's guide to pressure washer nozzles and when to use each type for maximum effectiveness and safety.",
      content: `
        <p>Choosing the right nozzle for your pressure washing job is crucial for both effectiveness and safety. The wrong nozzle can damage surfaces or provide inadequate cleaning power. In this comprehensive guide, we'll walk you through everything you need to know about pressure washer nozzles.</p>

        <h2>Understanding Nozzle Color Codes</h2>
        <p>Pressure washer nozzles are color-coded to indicate their spray angle and intensity:</p>
        <ul>
          <li><strong>Red (0°):</strong> Most concentrated spray, highest pressure</li>
          <li><strong>Yellow (15°):</strong> Narrow spray for tough stains</li>
          <li><strong>Green (25°):</strong> Medium spray for general cleaning</li>
          <li><strong>White (40°):</strong> Wide spray for delicate surfaces</li>
          <li><strong>Black (65°):</strong> Widest spray, lowest pressure</li>
        </ul>

        <h2>When to Use Each Nozzle Type</h2>
        
        <h3>Red Nozzle (0°) - The Precision Tool</h3>
        <p>The red nozzle creates a concentrated stream that's perfect for:</p>
        <ul>
          <li>Removing stubborn stains from concrete</li>
          <li>Cleaning small, tough spots</li>
          <li>Cutting through heavy grime</li>
        </ul>
        <p><strong>Warning:</strong> Never use the red nozzle on delicate surfaces like wood, vinyl siding, or painted surfaces.</p>

        <h3>Yellow Nozzle (15°) - The Heavy-Duty Cleaner</h3>
        <p>The yellow nozzle is ideal for:</p>
        <ul>
          <li>Stripping paint or stain from surfaces</li>
          <li>Cleaning heavily soiled concrete</li>
          <li>Removing mildew from hard surfaces</li>
        </ul>

        <h3>Green Nozzle (25°) - The All-Purpose Choice</h3>
        <p>This is your go-to nozzle for most cleaning tasks:</p>
        <ul>
          <li>General house washing</li>
          <li>Cleaning driveways and sidewalks</li>
          <li>Washing vehicles (from a safe distance)</li>
        </ul>

        <h3>White Nozzle (40°) - The Gentle Giant</h3>
        <p>Perfect for delicate surfaces:</p>
        <ul>
          <li>Washing windows and screens</li>
          <li>Cleaning vinyl siding</li>
          <li>Rinsing soap from surfaces</li>
        </ul>

        <h3>Black Nozzle (65°) - The Soap Applicator</h3>
        <p>Primarily used for:</p>
        <ul>
          <li>Applying detergent or soap</li>
          <li>Very gentle rinsing</li>
          <li>Watering plants (though a garden hose is better)</li>
        </ul>

        <h2>Pro Tips for Nozzle Selection</h2>
        <ol>
          <li><strong>Start wide, go narrow:</strong> Always begin with a wider spray pattern and work your way to narrower angles if needed.</li>
          <li><strong>Test first:</strong> Always test your chosen nozzle on an inconspicuous area first.</li>
          <li><strong>Maintain distance:</strong> Keep appropriate distance between the nozzle and surface - typically 6-12 inches.</li>
          <li><strong>Consider the surface:</strong> Harder surfaces can handle narrower angles, while softer materials need wider patterns.</li>
        </ol>

        <h2>Common Mistakes to Avoid</h2>
        <p>Here are the most common nozzle-related mistakes we see:</p>
        <ul>
          <li>Using too narrow an angle on delicate surfaces</li>
          <li>Holding the nozzle too close to the surface</li>
          <li>Not adjusting pressure for different materials</li>
          <li>Forgetting to switch nozzles when changing tasks</li>
        </ul>

        <h2>Conclusion</h2>
        <p>Mastering nozzle selection is fundamental to successful pressure washing. Remember: when in doubt, start with a wider angle and work your way to narrower patterns as needed. Your surfaces (and your customers) will thank you for taking the time to choose the right tool for the job.</p>
      `,
      image: "/placeholder.svg?height=400&width=800",
      category: "tips",
      author: "Mike Rodriguez",
      publishDate: "2025-01-15",
      readTime: "5 min read",
      featured: true,
    }

    const mockRelatedArticles = [
      {
        id: 2,
        title: "5 Mistakes to Avoid When Washing a Driveway",
        image: "/placeholder.svg?height=150&width=200",
        readTime: "4 min read",
      },
      {
        id: 4,
        title: "Soft Washing vs Pressure Washing: When to Use Each",
        image: "/placeholder.svg?height=150&width=200",
        readTime: "6 min read",
      },
      {
        id: 6,
        title: "Winter Maintenance for Your Pressure Washing Equipment",
        image: "/placeholder.svg?height=150&width=200",
        readTime: "5 min read",
      },
    ]

    setArticle(mockArticle)
    setRelatedArticles(mockRelatedArticles)
  }, [articleId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.excerpt,
        url: window.location.href,
      })
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
    }
  }

  if (!article) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <p className="text-neutral-400">Loading article...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Navigation */}
      <div className="mb-8">
        <Link href="/blog" className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Articles
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Article */}
        <div className="lg:col-span-3">
          <article>
            {/* Header */}
            <div className="mb-8">
              {article.featured && <Badge className="bg-blue-500 text-white mb-4">Featured Article</Badge>}

              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">{article.title}</h1>

              <p className="text-xl text-neutral-400 mb-6">{article.excerpt}</p>

              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-neutral-500 mb-6">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{article.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(article.publishDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{article.readTime}</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  <Badge className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs">Tips & Guides</Badge>
                </div>
              </div>

              {/* Share Button */}
              <div className="flex gap-3 mb-8">
                <Button
                  onClick={handleShare}
                  variant="outline"
                  className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button
                  variant="outline"
                  className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                >
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Like
                </Button>
              </div>
            </div>

            {/* Featured Image */}
            <div className="mb-8">
              <div className="aspect-video bg-neutral-800 rounded-lg overflow-hidden">
                <Image
                  src={article.image || "/placeholder.svg"}
                  alt={article.title}
                  width={800}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Article Content */}
            <div
              className="prose prose-invert prose-blue max-w-none"
              dangerouslySetInnerHTML={{ __html: article.content }}
              style={{
                color: "#d1d5db",
                lineHeight: "1.7",
              }}
            />

            {/* Article Footer */}
            <div className="mt-12 pt-8 border-t border-neutral-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                  >
                    <ThumbsUp className="h-4 w-4 mr-2" />
                    Helpful (24)
                  </Button>
                  <Button
                    variant="outline"
                    className="border-neutral-700 text-neutral-300 bg-transparent hover:bg-neutral-800"
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Comments (8)
                  </Button>
                </div>
                <Button
                  onClick={handleShare}
                  variant="outline"
                  className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Article
                </Button>
              </div>
            </div>
          </article>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Author Info */}
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-gradient rounded-full mx-auto mb-4 flex items-center justify-center glow-blue">
                    <User className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-white font-semibold mb-2">{article.author}</h3>
                  <p className="text-neutral-400 text-sm mb-4">
                    Professional pressure washing contractor with 15+ years of experience
                  </p>
                  <Button
                    variant="outline"
                    className="w-full border-blue-500/20 text-blue-400 hover:bg-blue-500/10 bg-transparent"
                  >
                    View Profile
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Related Articles */}
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-4">Related Articles</h3>
                <div className="space-y-4">
                  {relatedArticles.map((relatedArticle) => (
                    <Link key={relatedArticle.id} href={`/blog/${relatedArticle.id}`} className="block group">
                      <div className="flex gap-3">
                        <div className="w-16 h-16 bg-neutral-800 rounded-xl overflow-hidden flex-shrink-0">
                          <Image
                            src={relatedArticle.image || "/placeholder.svg"}
                            alt={relatedArticle.title}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-white text-sm font-medium line-clamp-2 group-hover:text-blue-400 transition-colors">
                            {relatedArticle.title}
                          </h4>
                          <p className="text-neutral-500 text-xs mt-1">{relatedArticle.readTime}</p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter Signup */}
            <Card className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border-blue-500/20">
              <CardContent className="p-6">
                <h3 className="text-white font-semibold mb-2">Stay Updated</h3>
                <p className="text-neutral-400 text-sm mb-4">
                  Get weekly tips and industry insights delivered to your inbox.
                </p>
                <Button className="w-full bg-blue-gradient-hover">Subscribe Now</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
